[{"/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/app/api/search/route.ts": "1", "/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/app/layout.tsx": "2", "/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/app/page.tsx": "3", "/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/components/ChartsSection.tsx": "4", "/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/components/FileUpload.tsx": "5", "/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/components/LogTable.tsx": "6", "/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/components/SearchInterface.tsx": "7", "/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/components/StatsCards.tsx": "8"}, {"size": 782, "mtime": 1752490387503, "results": "9", "hashOfConfig": "10"}, {"size": 1337, "mtime": 1752489977795, "results": "11", "hashOfConfig": "10"}, {"size": 2802, "mtime": 1752489993565, "results": "12", "hashOfConfig": "10"}, {"size": 5422, "mtime": 1752490330545, "results": "13", "hashOfConfig": "10"}, {"size": 5277, "mtime": 1752490377764, "results": "14", "hashOfConfig": "10"}, {"size": 6712, "mtime": 1752490049189, "results": "15", "hashOfConfig": "10"}, {"size": 5680, "mtime": 1752490021169, "results": "16", "hashOfConfig": "10"}, {"size": 2784, "mtime": 1752490350557, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "cjzk3m", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/Sher<PERSON>/Project Repos/AI Projects /logAnalyzer2/src/app/api/search/route.ts", ["42", "43"], [], "/home/<USER>/Sher<PERSON>/Project Repos/AI Projects /logAnalyzer2/src/app/layout.tsx", ["44", "45"], [], "/home/<USER>/Sher<PERSON>/Project Repos/AI Projects /logAnalyzer2/src/app/page.tsx", ["46", "47"], [], "/home/<USER>/Sher<PERSON>/Project Repos/AI Projects /logAnalyzer2/src/components/ChartsSection.tsx", ["48", "49"], [], "/home/<USER>/Sher<PERSON>/Project Repos/AI Projects /logAnalyzer2/src/components/FileUpload.tsx", ["50", "51"], [], "/home/<USER>/Sher<PERSON>/Project Repos/AI Projects /logAnalyzer2/src/components/LogTable.tsx", ["52", "53"], [], "/home/<USER>/Sher<PERSON>/Project Repos/AI Projects /logAnalyzer2/src/components/SearchInterface.tsx", ["54", "55"], [], "/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/components/StatsCards.tsx", ["56", "57"], [], {"ruleId": "58", "message": "59", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "60", "message": "61", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "58", "message": "59", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "60", "message": "61", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "58", "message": "59", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "60", "message": "61", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "58", "message": "59", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "60", "message": "61", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "58", "message": "59", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "60", "message": "61", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "58", "message": "59", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "60", "message": "61", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "58", "message": "59", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "60", "message": "61", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "58", "message": "59", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, {"ruleId": "60", "message": "61", "line": 1, "column": 1, "endLine": 1, "endColumn": 2, "severity": 2, "nodeType": null}, "@typescript-eslint/no-unused-vars", "Definition for rule '@typescript-eslint/no-unused-vars' was not found.", "@typescript-eslint/no-explicit-any", "Definition for rule '@typescript-eslint/no-explicit-any' was not found."]