"use strict";(()=>{var e={};e.id=280,e.ids=[280],e.modules={524:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6417:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>l,originalPathname:()=>P,requestAsyncStorage:()=>u,routeModule:()=>c,serverHooks:()=>d,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>h});var a={};t.r(a),t.d(a,{POST:()=>POST});var o=t(884),s=t(6132),n=t(5798);let i=process.env.BACKEND_URL||"http://localhost:8000";async function POST(e){try{let r=await e.json(),t=await fetch(`${i}/api/search`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!t.ok)throw Error(`Backend request failed: ${t.statusText}`);let a=await t.json();return n.Z.json(a)}catch(e){return console.error("API route error:",e),n.Z.json({error:"Failed to search logs"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/search/route",pathname:"/api/search",filename:"route",bundlePath:"app/api/search/route"},resolvedPagePath:"/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/app/api/search/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:u,staticGenerationAsyncStorage:p,serverHooks:d,headerHooks:l,staticGenerationBailout:h}=c,P="/api/search/route"}};var r=require("../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[997],()=>__webpack_exec__(6417));module.exports=t})();