(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7310:e=>{"use strict";e.exports=require("url")},8798:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>n});var a=t(7096),r=t(6132),l=t(7284),i=t.n(l),o=t(2564),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(s,d);let n=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8203)),"/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,9113)),"/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9291,23)),"next/dist/client/components/not-found-error"]}],c=["/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/app/page.tsx"],x="/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},1755:(e,s,t)=>{Promise.resolve().then(t.bind(t,9043))},9043:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>HomePage});var a=t(784),r=t(9885),l=t(9518),i=t(6315),o=t(5256),d=t(87);function SearchInterface({onResults:e}){let[s,t]=(0,r.useState)(""),[i,o]=(0,r.useState)(!1),[n,c]=(0,r.useState)(""),[x,m]=(0,r.useState)(10),handleSearch=async t=>{if(t.preventDefault(),s.trim()){o(!0),c("");try{let t=await fetch("http://localhost:8000/api/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:s.trim(),max_results:x})});if(!t.ok)throw Error(`Search failed: ${t.statusText}`);let a=await t.json();e(a.results||[])}catch(s){c(s instanceof Error?s.message:"Search failed"),e([])}finally{o(!1)}}},handleCreateSampleData=async()=>{o(!0),c("");try{let e=await fetch("http://localhost:8000/api/create-sample-data",{method:"POST"});if(!e.ok)throw Error(`Failed to create sample data: ${e.statusText}`);let s=await e.json();c(`Created ${s.sample_logs_created} sample logs successfully!`)}catch(e){c(e instanceof Error?e.message:"Failed to create sample data")}finally{o(!1)}};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Natural Language Log Search"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Search your logs using natural language queries. Ask about errors, performance issues, or specific events."})]}),a.jsx("form",{onSubmit:handleSearch,className:"space-y-4",children:(0,a.jsxs)("div",{className:"flex space-x-4",children:[a.jsx("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(l.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),a.jsx("input",{type:"text",value:s,onChange:e=>t(e.target.value),placeholder:"e.g., flight booking errors in the last hour",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:i})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("label",{htmlFor:"maxResults",className:"text-sm text-gray-600",children:"Max:"}),(0,a.jsxs)("select",{id:"maxResults",value:x,onChange:e=>m(Number(e.target.value)),className:"border border-gray-300 rounded-md px-2 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:i,children:[a.jsx("option",{value:5,children:"5"}),a.jsx("option",{value:10,children:"10"}),a.jsx("option",{value:20,children:"20"}),a.jsx("option",{value:50,children:"50"})]})]}),(0,a.jsxs)("button",{type:"submit",disabled:i||!s.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[i?a.jsx(d.Z,{className:"h-4 w-4 animate-spin"}):a.jsx(l.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"Search"})]})]})}),n&&a.jsx("div",{className:`mt-4 p-3 rounded-md ${n.includes("successfully")?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"}`,children:n}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[a.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Example Queries"}),a.jsx("button",{onClick:handleCreateSampleData,disabled:i,className:"text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded text-gray-600 disabled:opacity-50",children:"Create Sample Data"})]}),a.jsx("div",{className:"flex flex-wrap gap-2",children:["flight booking errors in the last hour","slow hotel reservation requests","loyalty point transfer failures","mobile platform issues","status code 500 errors"].map((e,s)=>a.jsx("button",{onClick:()=>t(e),className:"text-xs bg-blue-50 hover:bg-blue-100 text-blue-700 px-2 py-1 rounded-full transition-colors",disabled:i,children:e},s))})]})]})}var n=t(2032),c=t(4409),x=t(1057),m=t(6078),h=t(5441),u=t(3680);function LogTable({logs:e}){let getStatusIcon=e=>{let s=parseInt(e);return s>=200&&s<300?a.jsx(n.Z,{className:"h-4 w-4 text-green-500"}):s>=400&&s<500?a.jsx(c.Z,{className:"h-4 w-4 text-yellow-500"}):s>=500?a.jsx(x.Z,{className:"h-4 w-4 text-red-500"}):a.jsx("div",{className:"h-4 w-4 bg-gray-300 rounded-full"})},getLevelColor=e=>{switch(e.toUpperCase()){case"ERROR":return"bg-red-100 text-red-800";case"WARN":return"bg-yellow-100 text-yellow-800";case"INFO":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},formatTimestamp=e=>{try{return new Date(e).toLocaleString()}catch{return e}},formatResponseTime=e=>e>=1e3?(0,a.jsxs)("span",{className:"text-red-600 font-medium",children:[e,"ms"]}):e>=500?(0,a.jsxs)("span",{className:"text-yellow-600 font-medium",children:[e,"ms"]}):(0,a.jsxs)("span",{className:"text-green-600",children:[e,"ms"]});return 0===e.length?(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-8 text-center",children:[a.jsx("div",{className:"text-gray-400 mb-2",children:a.jsx(m.Z,{className:"h-12 w-12 mx-auto"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No logs found"}),a.jsx("p",{className:"text-gray-600",children:"Try a different search query or upload some log files."})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border overflow-hidden",children:[a.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Search Results (",e.length," logs)"]})}),a.jsx("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Timestamp"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Level"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Module"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Response Time"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Platform"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Message"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Similarity"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map((e,s)=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(h.Z,{className:"h-4 w-4 text-gray-400 mr-2"}),formatTimestamp(e.metadata.timestamp)]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getLevelColor(e.metadata.level)}`,children:e.metadata.level})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.metadata.module}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,a.jsxs)("div",{className:"flex items-center",children:[getStatusIcon(e.metadata.status),a.jsx("span",{className:"ml-2",children:e.metadata.status})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:e.metadata.response_time?formatResponseTime(e.metadata.response_time):"N/A"}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(u.Z,{className:"h-4 w-4 text-gray-400 mr-2"}),e.metadata.platform]})}),a.jsx("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-xs",children:a.jsx("div",{className:"truncate",title:e.metadata.raw_data,children:e.metadata.raw_data})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-16 bg-gray-200 rounded-full h-2 mr-2",children:a.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${Math.min(100*e.similarity_score,100)}%`}})}),(0,a.jsxs)("span",{className:"text-xs",children:[(100*e.similarity_score).toFixed(1),"%"]})]})})]},e.log_id||s))})]})})]})}var p=t(3647),g=t(4482),b=t(4300),y=t(5522),j=t(4839),f=t(4206),N=t(8741),v=t(4578),w=t(4723),_=t(8081),S=t(4707),k=t(7106);function ChartsSection({stats:e,logs:s}){let t=e?.logs_by_level?Object.entries(e.logs_by_level).map(([e,s])=>({level:e,count:s})):[],r=e?.logs_by_source?Object.entries(e.logs_by_source).map(([e,s])=>({source:e,count:s})):[],l=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8"],i=s.slice(0,20).map((e,s)=>({index:s+1,responseTime:e.metadata?.response_time||0,status:e.metadata?.status||"200"}));return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Log Levels Distribution"}),a.jsx(p.h,{width:"100%",height:300,children:(0,a.jsxs)(g.v,{data:t,children:[a.jsx(b.q,{strokeDasharray:"3 3"}),a.jsx(y.K,{dataKey:"level"}),a.jsx(j.B,{}),a.jsx(f.u,{}),a.jsx(N.$,{dataKey:"count",fill:"#3B82F6"})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Log Sources Distribution"}),a.jsx(p.h,{width:"100%",height:300,children:(0,a.jsxs)(v.u,{children:[a.jsx(w.b,{data:r,cx:"50%",cy:"50%",labelLine:!1,label:({source:e,percent:s})=>`${e} ${(100*s).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"count",children:r.map((e,s)=>a.jsx(_.b,{fill:l[s%l.length]},`cell-${s}`))}),a.jsx(f.u,{})]})})]})]}),i.length>0&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Response Time Trend"}),a.jsx(p.h,{width:"100%",height:300,children:(0,a.jsxs)(S.w,{data:i,children:[a.jsx(b.q,{strokeDasharray:"3 3"}),a.jsx(y.K,{dataKey:"index"}),a.jsx(j.B,{}),a.jsx(f.u,{formatter:(e,s)=>[`${e}ms`,"Response Time"],labelFormatter:e=>`Log ${e}`}),a.jsx(k.x,{type:"monotone",dataKey:"responseTime",stroke:"#10B981",strokeWidth:2,dot:{fill:"#10B981",strokeWidth:2,r:4}})]})})]}),e&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[a.jsx("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Average Response Time"}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-gray-900",children:[e.avg_response_time?.toFixed(1)||0,"ms"]}),a.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Target: <500ms"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[a.jsx("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Error Rate"}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-red-600",children:[e.error_rate?.toFixed(1)||0,"%"]}),a.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Target: <5%"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[a.jsx("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Total Logs"}),a.jsx("div",{className:"text-2xl font-bold text-gray-900",children:e.total_logs?.toLocaleString()||0}),a.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Last 24 hours"})]})]}),!e&&0===s.length&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-8 text-center",children:[a.jsx("div",{className:"text-gray-400 mb-4",children:a.jsx(g.v,{className:"h-16 w-16 mx-auto"})}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Analytics Data"}),a.jsx("p",{className:"text-gray-600",children:"Upload some logs or perform searches to see analytics charts."})]})]})}var T=t(9815),F=t(1831),P=t(7);function StatsCards({stats:e}){var s;let t=e.logs_by_level?.ERROR||0,r=e.logs_by_level?.WARN||0,l=[{title:"Total Logs",value:(s=e.total_logs)>=1e3?`${(s/1e3).toFixed(1)}k`:s.toLocaleString(),icon:T.Z,color:"blue",subtitle:"Last 24 hours"},{title:"Error Rate",value:`${e.error_rate.toFixed(1)}%`,icon:F.Z,color:e.error_rate>5?"red":"green",subtitle:`${t} errors, ${r} warnings`},{title:"Avg Response Time",value:`${e.avg_response_time.toFixed(0)}ms`,icon:h.Z,color:e.avg_response_time>1e3?"red":e.avg_response_time>500?"yellow":"green",subtitle:"Target: <500ms"},{title:"Performance Trend",value:"↗ +2.3%",icon:P.Z,color:"green",subtitle:"vs last hour"}],getColorClasses=e=>{switch(e){case"red":return"text-red-600 bg-red-50 border-red-200";case"yellow":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"green":return"text-green-600 bg-green-50 border-green-200";case"blue":return"text-blue-600 bg-blue-50 border-blue-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}};return a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:l.map((e,s)=>{let t=e.icon,r=getColorClasses(e.color);return a.jsx("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:e.title}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:e.value}),a.jsx("p",{className:"text-sm text-gray-500 mt-1",children:e.subtitle})]}),a.jsx("div",{className:`p-3 rounded-lg ${r}`,children:a.jsx(t,{className:"h-6 w-6"})})]})},s)})})}var R=t(5595);function FileUpload({onUploadSuccess:e}){let[s,t]=(0,r.useState)(!1),[l,o]=(0,r.useState)(""),[c,m]=(0,r.useState)(""),handleFileUpload=async s=>{let a=s.target.files?.[0];if(a){t(!0),o("");try{let s=new FormData;s.append("file",a);let t=await fetch("http://localhost:8000/api/upload-logs",{method:"POST",body:s});if(!t.ok)throw Error(`Upload failed: ${t.statusText}`);let r=await t.json();o(`Successfully uploaded and parsed ${r.parsed_logs.length} logs from ${r.filename}`),m("success"),e()}catch(e){o(e instanceof Error?e.message:"Upload failed"),m("error")}finally{t(!1),s.target.value=""}}};return(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors",onDragOver:e=>{e.preventDefault()},onDrop:e=>{e.preventDefault();let s=e.dataTransfer.files;s.length>0&&handleFileUpload({target:{files:s}})},children:(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("div",{className:"mx-auto w-12 h-12 text-gray-400",children:s?a.jsx(d.Z,{className:"w-12 h-12 animate-spin"}):a.jsx(i.Z,{className:"w-12 h-12"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Upload Log Files"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Drag and drop your log files here, or click to browse"})]}),a.jsx("div",{children:(0,a.jsxs)("label",{htmlFor:"file-upload",className:"cursor-pointer",children:[(0,a.jsxs)("span",{className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[a.jsx(R.Z,{className:"h-4 w-4 mr-2"}),"Choose Files"]}),a.jsx("input",{id:"file-upload",type:"file",className:"sr-only",accept:".log,.txt",onChange:handleFileUpload,disabled:s})]})}),a.jsx("p",{className:"text-xs text-gray-500",children:"Supports .log and .txt files"})]})}),l&&a.jsx("div",{className:`rounded-md p-4 ${"success"===c?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"}`,children:(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"flex-shrink-0",children:"success"===c?a.jsx(n.Z,{className:"h-5 w-5 text-green-400"}):a.jsx(x.Z,{className:"h-5 w-5 text-red-400"})}),a.jsx("div",{className:"ml-3",children:a.jsx("p",{className:`text-sm font-medium ${"success"===c?"text-green-800":"text-red-800"}`,children:l})})]})}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:[a.jsx("h4",{className:"text-sm font-medium text-blue-800 mb-2",children:"Expected Log Format"}),a.jsx("p",{className:"text-sm text-blue-700 mb-2",children:"Your log files should follow this format:"}),a.jsx("code",{className:"text-xs bg-white p-2 rounded border block text-gray-800 overflow-x-auto",children:"[timestamp][level][module][sessionId][platform][customerId][clientIp][requestId][auditType][appName][responseTime][status][method][originUrl][extraData] : rawData"}),a.jsx("p",{className:"text-xs text-blue-600 mt-2",children:"Example: [2024-07-14 10:30:15.123][INFO][FlightBooking][sess_123][web][cust_456][***********][req_789][BOOKING][AxisFlights][250ms][200][POST][/api/flights/book][userId:456] : Successfully booked flight"})]})]})}function HomePage(){let[e,s]=(0,r.useState)("search"),[t,d]=(0,r.useState)([]),[n,c]=(0,r.useState)(null),[x,m]=(0,r.useState)(!1);(0,r.useEffect)(()=>{fetchStats()},[]);let fetchStats=async()=>{try{let e=await fetch("http://localhost:8000/api/stats"),s=await e.json();c(s)}catch(e){console.error("Error fetching stats:",e)}},h=[{id:"search",name:"Search Logs",icon:l.Z},{id:"upload",name:"Upload Logs",icon:i.Z},{id:"charts",name:"Analytics",icon:o.Z}];return(0,a.jsxs)("div",{className:"space-y-6",children:[n&&a.jsx(StatsCards,{stats:n}),a.jsx("div",{className:"border-b border-gray-200",children:a.jsx("nav",{className:"-mb-px flex space-x-8",children:h.map(t=>{let r=t.icon;return(0,a.jsxs)("button",{onClick:()=>s(t.id),className:`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${e===t.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[a.jsx(r,{className:"h-4 w-4"}),a.jsx("span",{children:t.name})]},t.id)})})}),(0,a.jsxs)("div",{className:"mt-6",children:["search"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx(SearchInterface,{onResults:e=>{d(e)}}),t.length>0&&a.jsx(LogTable,{logs:t})]}),"upload"===e&&a.jsx("div",{className:"space-y-6",children:a.jsx(FileUpload,{onUploadSuccess:fetchStats})}),"charts"===e&&a.jsx("div",{className:"space-y-6",children:a.jsx(ChartsSection,{stats:n,logs:t})})]})]})}},8203:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>d});var a=t(5153);let r=(0,a.createProxy)(String.raw`/home/<USER>/Sherwyn/Project Repos/AI Projects /logAnalyzer2/src/app/page.tsx`),{__esModule:l,$$typeof:i}=r,o=r.default,d=o}};var s=require("../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[485,205,308],()=>__webpack_exec__(8798));module.exports=t})();