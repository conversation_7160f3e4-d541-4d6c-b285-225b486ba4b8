exports.id=308,exports.ids=[308],exports.modules={7837:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,3724,23)),Promise.resolve().then(t.t.bind(t,5365,23)),Promise.resolve().then(t.t.bind(t,4900,23)),Promise.resolve().then(t.t.bind(t,4714,23)),Promise.resolve().then(t.t.bind(t,5392,23)),Promise.resolve().then(t.t.bind(t,8898,23))},183:()=>{},9113:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>RootLayout,metadata:()=>i});var a=t(4656),l=t(177),r=t.n(l);t(5023);let i={title:"RAG Log Analyzer",description:"AI-powered log analysis with natural language search"};function RootLayout({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:r().className,children:(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("header",{className:"bg-white shadow-sm border-b",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[a.jsx("div",{className:"flex items-center",children:a.jsx("h1",{className:"text-xl font-semibold text-gray-900",children:"RAG Log Analyzer"})}),a.jsx("div",{className:"flex items-center space-x-4",children:a.jsx("span",{className:"text-sm text-gray-500",children:"AI-Powered Log Analysis"})})]})})}),a.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e})]})})})}},5023:()=>{}};