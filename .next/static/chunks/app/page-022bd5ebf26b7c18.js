(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{7991:function(e,s,t){Promise.resolve().then(t.bind(t,1288))},1288:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return HomePage}});var a=t(7437),r=t(2265),l=t(1827),d=t(1541),i=t(2457),c=t(6264);function SearchInterface(e){let{onResults:s}=e,[t,d]=(0,r.useState)(""),[i,o]=(0,r.useState)(!1),[n,m]=(0,r.useState)(""),[x,h]=(0,r.useState)(10),handleSearch=async e=>{if(e.preventDefault(),t.trim()){o(!0),m("");try{let e=await fetch("http://localhost:8000/api/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:t.trim(),max_results:x})});if(!e.ok)throw Error("Search failed: ".concat(e.statusText));let a=await e.json();s(a.results||[])}catch(e){m(e instanceof Error?e.message:"Search failed"),s([])}finally{o(!1)}}},handleCreateSampleData=async()=>{o(!0),m("");try{let e=await fetch("http://localhost:8000/api/create-sample-data",{method:"POST"});if(!e.ok)throw Error("Failed to create sample data: ".concat(e.statusText));let s=await e.json();m("Created ".concat(s.sample_logs_created," sample logs successfully!"))}catch(e){m(e instanceof Error?e.message:"Failed to create sample data")}finally{o(!1)}};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Natural Language Log Search"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Search your logs using natural language queries. Ask about errors, performance issues, or specific events."})]}),(0,a.jsx)("form",{onSubmit:handleSearch,className:"space-y-4",children:(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(l.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)("input",{type:"text",value:t,onChange:e=>d(e.target.value),placeholder:"e.g., flight booking errors in the last hour",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:i})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("label",{htmlFor:"maxResults",className:"text-sm text-gray-600",children:"Max:"}),(0,a.jsxs)("select",{id:"maxResults",value:x,onChange:e=>h(Number(e.target.value)),className:"border border-gray-300 rounded-md px-2 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent",disabled:i,children:[(0,a.jsx)("option",{value:5,children:"5"}),(0,a.jsx)("option",{value:10,children:"10"}),(0,a.jsx)("option",{value:20,children:"20"}),(0,a.jsx)("option",{value:50,children:"50"})]})]}),(0,a.jsxs)("button",{type:"submit",disabled:i||!t.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[i?(0,a.jsx)(c.Z,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(l.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Search"})]})]})}),n&&(0,a.jsx)("div",{className:"mt-4 p-3 rounded-md ".concat(n.includes("successfully")?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"),children:n}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Example Queries"}),(0,a.jsx)("button",{onClick:handleCreateSampleData,disabled:i,className:"text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded text-gray-600 disabled:opacity-50",children:"Create Sample Data"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:["flight booking errors in the last hour","slow hotel reservation requests","loyalty point transfer failures","mobile platform issues","status code 500 errors"].map((e,s)=>(0,a.jsx)("button",{onClick:()=>d(e),className:"text-xs bg-blue-50 hover:bg-blue-100 text-blue-700 px-2 py-1 rounded-full transition-colors",disabled:i,children:e},s))})]})]})}var o=t(3008),n=t(2894),m=t(2104),x=t(8956),h=t(6141),u=t(7972);function LogTable(e){let{logs:s}=e,getStatusIcon=e=>{let s=parseInt(e);return s>=200&&s<300?(0,a.jsx)(o.Z,{className:"h-4 w-4 text-green-500"}):s>=400&&s<500?(0,a.jsx)(n.Z,{className:"h-4 w-4 text-yellow-500"}):s>=500?(0,a.jsx)(m.Z,{className:"h-4 w-4 text-red-500"}):(0,a.jsx)("div",{className:"h-4 w-4 bg-gray-300 rounded-full"})},getLevelColor=e=>{switch(e.toUpperCase()){case"ERROR":return"bg-red-100 text-red-800";case"WARN":return"bg-yellow-100 text-yellow-800";case"INFO":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},formatTimestamp=e=>{try{return new Date(e).toLocaleString()}catch(s){return e}},formatResponseTime=e=>e>=1e3?(0,a.jsxs)("span",{className:"text-red-600 font-medium",children:[e,"ms"]}):e>=500?(0,a.jsxs)("span",{className:"text-yellow-600 font-medium",children:[e,"ms"]}):(0,a.jsxs)("span",{className:"text-green-600",children:[e,"ms"]});return 0===s.length?(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-8 text-center",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-2",children:(0,a.jsx)(x.Z,{className:"h-12 w-12 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No logs found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try a different search query or upload some log files."})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Search Results (",s.length," logs)"]})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Timestamp"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Level"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Module"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Response Time"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Platform"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Message"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Similarity"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map((e,s)=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 text-gray-400 mr-2"}),formatTimestamp(e.metadata.timestamp)]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat(getLevelColor(e.metadata.level)),children:e.metadata.level})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.metadata.module}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,a.jsxs)("div",{className:"flex items-center",children:[getStatusIcon(e.metadata.status),(0,a.jsx)("span",{className:"ml-2",children:e.metadata.status})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:e.metadata.response_time?formatResponseTime(e.metadata.response_time):"N/A"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.Z,{className:"h-4 w-4 text-gray-400 mr-2"}),e.metadata.platform]})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-xs",children:(0,a.jsx)("div",{className:"truncate",title:e.metadata.raw_data,children:e.metadata.raw_data})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-2 mr-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(Math.min(100*e.similarity_score,100),"%")}})}),(0,a.jsxs)("span",{className:"text-xs",children:[(100*e.similarity_score).toFixed(1),"%"]})]})})]},e.log_id||s))})]})})]})}var g=t(5253),p=t(4909),b=t(6573),f=t(4235),j=t(39),y=t(6812),N=t(1346),v=t(7703),w=t(8485),S=t(6612),_=t(8227),k=t(9223);function ChartsSection(e){var s,t,r;let{stats:l,logs:d}=e,i=(null==l?void 0:l.logs_by_level)?Object.entries(l.logs_by_level).map(e=>{let[s,t]=e;return{level:s,count:t}}):[],c=(null==l?void 0:l.logs_by_source)?Object.entries(l.logs_by_source).map(e=>{let[s,t]=e;return{source:s,count:t}}):[],o=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8"],n=d.slice(0,20).map((e,s)=>{var t,a;return{index:s+1,responseTime:(null===(t=e.metadata)||void 0===t?void 0:t.response_time)||0,status:(null===(a=e.metadata)||void 0===a?void 0:a.status)||"200"}});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Log Levels Distribution"}),(0,a.jsx)(g.h,{width:"100%",height:300,children:(0,a.jsxs)(p.v,{data:i,children:[(0,a.jsx)(b.q,{strokeDasharray:"3 3"}),(0,a.jsx)(f.K,{dataKey:"level"}),(0,a.jsx)(j.B,{}),(0,a.jsx)(y.u,{}),(0,a.jsx)(N.$,{dataKey:"count",fill:"#3B82F6"})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Log Sources Distribution"}),(0,a.jsx)(g.h,{width:"100%",height:300,children:(0,a.jsxs)(v.u,{children:[(0,a.jsx)(w.b,{data:c,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{source:s,percent:t}=e;return"".concat(s," ").concat((100*t).toFixed(0),"%")},outerRadius:80,fill:"#8884d8",dataKey:"count",children:c.map((e,s)=>(0,a.jsx)(S.b,{fill:o[s%o.length]},"cell-".concat(s)))}),(0,a.jsx)(y.u,{})]})})]})]}),n.length>0&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Response Time Trend"}),(0,a.jsx)(g.h,{width:"100%",height:300,children:(0,a.jsxs)(_.w,{data:n,children:[(0,a.jsx)(b.q,{strokeDasharray:"3 3"}),(0,a.jsx)(f.K,{dataKey:"index"}),(0,a.jsx)(j.B,{}),(0,a.jsx)(y.u,{formatter:(e,s)=>["".concat(e,"ms"),"Response Time"],labelFormatter:e=>"Log ".concat(e)}),(0,a.jsx)(k.x,{type:"monotone",dataKey:"responseTime",stroke:"#10B981",strokeWidth:2,dot:{fill:"#10B981",strokeWidth:2,r:4}})]})})]}),l&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Average Response Time"}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-gray-900",children:[(null===(s=l.avg_response_time)||void 0===s?void 0:s.toFixed(1))||0,"ms"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Target: <500ms"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Error Rate"}),(0,a.jsxs)("div",{className:"text-2xl font-bold text-red-600",children:[(null===(t=l.error_rate)||void 0===t?void 0:t.toFixed(1))||0,"%"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Target: <5%"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Total Logs"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(null===(r=l.total_logs)||void 0===r?void 0:r.toLocaleString())||0}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Last 24 hours"})]})]}),!l&&0===d.length&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-8 text-center",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(p.v,{className:"h-16 w-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Analytics Data"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Upload some logs or perform searches to see analytics charts."})]})]})}var T=t(998),F=t(1981),C=t(5790);function StatsCards(e){var s,t,r;let{stats:l}=e,d=(null===(s=l.logs_by_level)||void 0===s?void 0:s.ERROR)||0,i=(null===(t=l.logs_by_level)||void 0===t?void 0:t.WARN)||0,c=[{title:"Total Logs",value:(r=l.total_logs)>=1e3?"".concat((r/1e3).toFixed(1),"k"):r.toLocaleString(),icon:T.Z,color:"blue",subtitle:"Last 24 hours"},{title:"Error Rate",value:"".concat(l.error_rate.toFixed(1),"%"),icon:F.Z,color:l.error_rate>5?"red":"green",subtitle:"".concat(d," errors, ").concat(i," warnings")},{title:"Avg Response Time",value:"".concat(l.avg_response_time.toFixed(0),"ms"),icon:h.Z,color:l.avg_response_time>1e3?"red":l.avg_response_time>500?"yellow":"green",subtitle:"Target: <500ms"},{title:"Performance Trend",value:"↗ +2.3%",icon:C.Z,color:"green",subtitle:"vs last hour"}],getColorClasses=e=>{switch(e){case"red":return"text-red-600 bg-red-50 border-red-200";case"yellow":return"text-yellow-600 bg-yellow-50 border-yellow-200";case"green":return"text-green-600 bg-green-50 border-green-200";case"blue":return"text-blue-600 bg-blue-50 border-blue-200";default:return"text-gray-600 bg-gray-50 border-gray-200"}};return(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:c.map((e,s)=>{let t=e.icon,r=getColorClasses(e.color);return(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:e.value}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:e.subtitle})]}),(0,a.jsx)("div",{className:"p-3 rounded-lg ".concat(r),children:(0,a.jsx)(t,{className:"h-6 w-6"})})]})},s)})})}var L=t(8734);function FileUpload(e){let{onUploadSuccess:s}=e,[t,l]=(0,r.useState)(!1),[i,n]=(0,r.useState)(""),[x,h]=(0,r.useState)(""),handleFileUpload=async e=>{var t;let a=null===(t=e.target.files)||void 0===t?void 0:t[0];if(a){l(!0),n("");try{let e=new FormData;e.append("file",a);let t=await fetch("http://localhost:8000/api/upload-logs",{method:"POST",body:e});if(!t.ok)throw Error("Upload failed: ".concat(t.statusText));let r=await t.json();n("Successfully uploaded and parsed ".concat(r.parsed_logs.length," logs from ").concat(r.filename)),h("success"),s()}catch(e){n(e instanceof Error?e.message:"Upload failed"),h("error")}finally{l(!1),e.target.value=""}}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors",onDragOver:e=>{e.preventDefault()},onDrop:e=>{e.preventDefault();let s=e.dataTransfer.files;s.length>0&&handleFileUpload({target:{files:s}})},children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"mx-auto w-12 h-12 text-gray-400",children:t?(0,a.jsx)(c.Z,{className:"w-12 h-12 animate-spin"}):(0,a.jsx)(d.Z,{className:"w-12 h-12"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Upload Log Files"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Drag and drop your log files here, or click to browse"})]}),(0,a.jsx)("div",{children:(0,a.jsxs)("label",{htmlFor:"file-upload",className:"cursor-pointer",children:[(0,a.jsxs)("span",{className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[(0,a.jsx)(L.Z,{className:"h-4 w-4 mr-2"}),"Choose Files"]}),(0,a.jsx)("input",{id:"file-upload",type:"file",className:"sr-only",accept:".log,.txt",onChange:handleFileUpload,disabled:t})]})}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Supports .log and .txt files"})]})}),i&&(0,a.jsx)("div",{className:"rounded-md p-4 ".concat("success"===x?"bg-green-50 border border-green-200":"bg-red-50 border border-red-200"),children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:"success"===x?(0,a.jsx)(o.Z,{className:"h-5 w-5 text-green-400"}):(0,a.jsx)(m.Z,{className:"h-5 w-5 text-red-400"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm font-medium ".concat("success"===x?"text-green-800":"text-red-800"),children:i})})]})}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-800 mb-2",children:"Expected Log Format"}),(0,a.jsx)("p",{className:"text-sm text-blue-700 mb-2",children:"Your log files should follow this format:"}),(0,a.jsx)("code",{className:"text-xs bg-white p-2 rounded border block text-gray-800 overflow-x-auto",children:"[timestamp][level][module][sessionId][platform][customerId][clientIp][requestId][auditType][appName][responseTime][status][method][originUrl][extraData] : rawData"}),(0,a.jsx)("p",{className:"text-xs text-blue-600 mt-2",children:"Example: [2024-07-14 10:30:15.123][INFO][FlightBooking][sess_123][web][cust_456][***********][req_789][BOOKING][AxisFlights][250ms][200][POST][/api/flights/book][userId:456] : Successfully booked flight"})]})]})}function HomePage(){let[e,s]=(0,r.useState)("search"),[t,c]=(0,r.useState)([]),[o,n]=(0,r.useState)(null),[m,x]=(0,r.useState)(!1);(0,r.useEffect)(()=>{fetchStats()},[]);let fetchStats=async()=>{try{let e=await fetch("http://localhost:8000/api/stats"),s=await e.json();n(s)}catch(e){console.error("Error fetching stats:",e)}},h=[{id:"search",name:"Search Logs",icon:l.Z},{id:"upload",name:"Upload Logs",icon:d.Z},{id:"charts",name:"Analytics",icon:i.Z}];return(0,a.jsxs)("div",{className:"space-y-6",children:[o&&(0,a.jsx)(StatsCards,{stats:o}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:h.map(t=>{let r=t.icon;return(0,a.jsxs)("button",{onClick:()=>s(t.id),className:"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ".concat(e===t.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,a.jsx)(r,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:t.name})]},t.id)})})}),(0,a.jsxs)("div",{className:"mt-6",children:["search"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(SearchInterface,{onResults:e=>{c(e)}}),t.length>0&&(0,a.jsx)(LogTable,{logs:t})]}),"upload"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(FileUpload,{onUploadSuccess:fetchStats})}),"charts"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(ChartsSection,{stats:o,logs:t})})]})]})}}},function(e){e.O(0,[29,971,472,744],function(){return e(e.s=7991)}),_N_E=e.O()}]);