(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[29],{539:function(e,t,n){var o;/*! decimal.js-light v2.5.1 https://github.com/MikeMcl/decimal.js-light/LICENCE */!function(i){"use strict";var a,c={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,s="[DecimalError] ",l=s+"Invalid argument: ",f=s+"Exponent out of range: ",p=Math.floor,d=Math.pow,y=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,h=p(1286742750677284.5),g={};function add(e,t){var n,o,i,a,c,s,l,f,p=e.constructor,d=p.precision;if(!e.s||!t.s)return t.s||(t=new p(e)),u?round(t,d):t;if(l=e.d,f=t.d,c=e.e,i=t.e,l=l.slice(),a=c-i){for(a<0?(o=l,a=-a,s=f.length):(o=f,i=c,s=l.length),a>(s=(c=Math.ceil(d/7))>s?c+1:s+1)&&(a=s,o.length=1),o.reverse();a--;)o.push(0);o.reverse()}for((s=l.length)-(a=f.length)<0&&(a=s,o=f,f=l,l=o),n=0;a;)n=(l[--a]=l[a]+f[a]+n)/1e7|0,l[a]%=1e7;for(n&&(l.unshift(n),++i),s=l.length;0==l[--s];)l.pop();return t.d=l,t.e=i,u?round(t,d):t}function checkInt32(e,t,n){if(e!==~~e||e<t||e>n)throw Error(l+e)}function digitsToString(e){var t,n,o,i=e.length-1,a="",c=e[0];if(i>0){for(a+=c,t=1;t<i;t++)(n=7-(o=e[t]+"").length)&&(a+=getZeroString(n)),a+=o;(n=7-(o=(c=e[t])+"").length)&&(a+=getZeroString(n))}else if(0===c)return"0";for(;c%10==0;)c/=10;return a+c}g.absoluteValue=g.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},g.comparedTo=g.cmp=function(e){var t,n,o,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,n=(o=this.d.length)<(i=e.d.length)?o:i;t<n;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return o===i?0:o>i^this.s<0?1:-1},g.decimalPlaces=g.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},g.dividedBy=g.div=function(e){return b(this,new this.constructor(e))},g.dividedToIntegerBy=g.idiv=function(e){var t=this.constructor;return round(b(this,new t(e),0,1),t.precision)},g.equals=g.eq=function(e){return!this.cmp(e)},g.exponent=function(){return getBase10Exponent(this)},g.greaterThan=g.gt=function(e){return this.cmp(e)>0},g.greaterThanOrEqualTo=g.gte=function(e){return this.cmp(e)>=0},g.isInteger=g.isint=function(){return this.e>this.d.length-2},g.isNegative=g.isneg=function(){return this.s<0},g.isPositive=g.ispos=function(){return this.s>0},g.isZero=function(){return 0===this.s},g.lessThan=g.lt=function(e){return 0>this.cmp(e)},g.lessThanOrEqualTo=g.lte=function(e){return 1>this.cmp(e)},g.logarithm=g.log=function(e){var t,n=this.constructor,o=n.precision,i=o+5;if(void 0===e)e=new n(10);else if((e=new n(e)).s<1||e.eq(a))throw Error(s+"NaN");if(this.s<1)throw Error(s+(this.s?"NaN":"-Infinity"));return this.eq(a)?new n(0):(u=!1,t=b(ln(this,i),ln(e,i),i),u=!0,round(t,o))},g.minus=g.sub=function(e){return e=new this.constructor(e),this.s==e.s?subtract(this,e):add(this,(e.s=-e.s,e))},g.modulo=g.mod=function(e){var t,n=this.constructor,o=n.precision;if(!(e=new n(e)).s)throw Error(s+"NaN");return this.s?(u=!1,t=b(this,e,0,1).times(e),u=!0,this.minus(t)):round(new n(this),o)},g.naturalExponential=g.exp=function(){return exp(this)},g.naturalLogarithm=g.ln=function(){return ln(this)},g.negated=g.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},g.plus=g.add=function(e){return e=new this.constructor(e),this.s==e.s?add(this,e):subtract(this,(e.s=-e.s,e))},g.precision=g.sd=function(e){var t,n,o;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(l+e);if(t=getBase10Exponent(this)+1,n=7*(o=this.d.length-1)+1,o=this.d[o]){for(;o%10==0;o/=10)n--;for(o=this.d[0];o>=10;o/=10)n++}return e&&t>n?t:n},g.squareRoot=g.sqrt=function(){var e,t,n,o,i,a,c,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(s+"NaN")}for(e=getBase10Exponent(this),u=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=digitsToString(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=p((e+1)/2)-(e<0||e%2),o=new l(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):o=new l(i.toString()),i=c=(n=l.precision)+3;;)if(o=(a=o).plus(b(this,a,c+2)).times(.5),digitsToString(a.d).slice(0,c)===(t=digitsToString(o.d)).slice(0,c)){if(t=t.slice(c-3,c+1),i==c&&"4999"==t){if(round(a,n+1,0),a.times(a).eq(this)){o=a;break}}else if("9999"!=t)break;c+=4}return u=!0,round(o,n)},g.times=g.mul=function(e){var t,n,o,i,a,c,s,l,f,p=this.constructor,d=this.d,y=(e=new p(e)).d;if(!this.s||!e.s)return new p(0);for(e.s*=this.s,n=this.e+e.e,(l=d.length)<(f=y.length)&&(a=d,d=y,y=a,c=l,l=f,f=c),a=[],o=c=l+f;o--;)a.push(0);for(o=f;--o>=0;){for(t=0,i=l+o;i>o;)s=a[i]+y[o]*d[i-o-1]+t,a[i--]=s%1e7|0,t=s/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--c];)a.pop();return t?++n:a.shift(),e.d=a,e.e=n,u?round(e,p.precision):e},g.toDecimalPlaces=g.todp=function(e,t){var n=this,o=n.constructor;return(n=new o(n),void 0===e)?n:(checkInt32(e,0,1e9),void 0===t?t=o.rounding:checkInt32(t,0,8),round(n,e+getBase10Exponent(n)+1,t))},g.toExponential=function(e,t){var n,o=this,i=o.constructor;return void 0===e?n=toString(o,!0):(checkInt32(e,0,1e9),void 0===t?t=i.rounding:checkInt32(t,0,8),n=toString(o=round(new i(o),e+1,t),!0,e+1)),n},g.toFixed=function(e,t){var n,o,i=this.constructor;return void 0===e?toString(this):(checkInt32(e,0,1e9),void 0===t?t=i.rounding:checkInt32(t,0,8),n=toString((o=round(new i(this),e+getBase10Exponent(this)+1,t)).abs(),!1,e+getBase10Exponent(o)+1),this.isneg()&&!this.isZero()?"-"+n:n)},g.toInteger=g.toint=function(){var e=this.constructor;return round(new e(this),getBase10Exponent(this)+1,e.rounding)},g.toNumber=function(){return+this},g.toPower=g.pow=function(e){var t,n,o,i,c,l,f=this,d=f.constructor,y=+(e=new d(e));if(!e.s)return new d(a);if(!(f=new d(f)).s){if(e.s<1)throw Error(s+"Infinity");return f}if(f.eq(a))return f;if(o=d.precision,e.eq(a))return round(f,o);if(l=(t=e.e)>=(n=e.d.length-1),c=f.s,l){if((n=y<0?-y:y)<=9007199254740991){for(i=new d(a),t=Math.ceil(o/7+4),u=!1;n%2&&truncate((i=i.times(f)).d,t),0!==(n=p(n/2));)truncate((f=f.times(f)).d,t);return u=!0,e.s<0?new d(a).div(i):round(i,o)}}else if(c<0)throw Error(s+"NaN");return c=c<0&&1&e.d[Math.max(t,n)]?-1:1,f.s=1,u=!1,i=e.times(ln(f,o+12)),u=!0,(i=exp(i)).s=c,i},g.toPrecision=function(e,t){var n,o,i=this,a=i.constructor;return void 0===e?(n=getBase10Exponent(i),o=toString(i,n<=a.toExpNeg||n>=a.toExpPos)):(checkInt32(e,1,1e9),void 0===t?t=a.rounding:checkInt32(t,0,8),n=getBase10Exponent(i=round(new a(i),e,t)),o=toString(i,e<=n||n<=a.toExpNeg,e)),o},g.toSignificantDigits=g.tosd=function(e,t){var n=this.constructor;return void 0===e?(e=n.precision,t=n.rounding):(checkInt32(e,1,1e9),void 0===t?t=n.rounding:checkInt32(t,0,8)),round(new n(this),e,t)},g.toString=g.valueOf=g.val=g.toJSON=function(){var e=getBase10Exponent(this),t=this.constructor;return toString(this,e<=t.toExpNeg||e>=t.toExpPos)};var b=function(){function multiplyInteger(e,t){var n,o=0,i=e.length;for(e=e.slice();i--;)n=e[i]*t+o,e[i]=n%1e7|0,o=n/1e7|0;return o&&e.unshift(o),e}function compare(e,t,n,o){var i,a;if(n!=o)a=n>o?1:-1;else for(i=a=0;i<n;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function subtract(e,t,n){for(var o=0;n--;)e[n]-=o,o=e[n]<t[n]?1:0,e[n]=1e7*o+e[n]-t[n];for(;!e[0]&&e.length>1;)e.shift()}return function(e,t,n,o){var i,a,c,u,l,f,p,d,y,h,g,b,m,_,x,S,P,O,j=e.constructor,w=e.s==t.s?1:-1,C=e.d,A=t.d;if(!e.s)return new j(e);if(!t.s)throw Error(s+"Division by zero");for(c=0,a=e.e-t.e,P=A.length,x=C.length,d=(p=new j(w)).d=[];A[c]==(C[c]||0);)++c;if(A[c]>(C[c]||0)&&--a,(b=null==n?n=j.precision:o?n+(getBase10Exponent(e)-getBase10Exponent(t))+1:n)<0)return new j(0);if(b=b/7+2|0,c=0,1==P)for(u=0,A=A[0],b++;(c<x||u)&&b--;c++)m=1e7*u+(C[c]||0),d[c]=m/A|0,u=m%A|0;else{for((u=1e7/(A[0]+1)|0)>1&&(A=multiplyInteger(A,u),C=multiplyInteger(C,u),P=A.length,x=C.length),_=P,h=(y=C.slice(0,P)).length;h<P;)y[h++]=0;(O=A.slice()).unshift(0),S=A[0],A[1]>=1e7/2&&++S;do u=0,(i=compare(A,y,P,h))<0?(g=y[0],P!=h&&(g=1e7*g+(y[1]||0)),(u=g/S|0)>1?(u>=1e7&&(u=1e7-1),f=(l=multiplyInteger(A,u)).length,h=y.length,1==(i=compare(l,y,f,h))&&(u--,subtract(l,P<f?O:A,f))):(0==u&&(i=u=1),l=A.slice()),(f=l.length)<h&&l.unshift(0),subtract(y,l,h),-1==i&&(h=y.length,(i=compare(A,y,P,h))<1&&(u++,subtract(y,P<h?O:A,h))),h=y.length):0===i&&(u++,y=[0]),d[c++]=u,i&&y[0]?y[h++]=C[_]||0:(y=[C[_]],h=1);while((_++<x||void 0!==y[0])&&b--)}return d[0]||d.shift(),p.e=a,round(p,o?n+getBase10Exponent(p)+1:n)}}();function exp(e,t){var n,o,i,c,s,l=0,p=0,y=e.constructor,h=y.precision;if(getBase10Exponent(e)>16)throw Error(f+getBase10Exponent(e));if(!e.s)return new y(a);for(null==t?(u=!1,s=h):s=t,c=new y(.03125);e.abs().gte(.1);)e=e.times(c),p+=5;for(s+=Math.log(d(2,p))/Math.LN10*2+5|0,n=o=i=new y(a),y.precision=s;;){if(o=round(o.times(e),s),n=n.times(++l),digitsToString((c=i.plus(b(o,n,s))).d).slice(0,s)===digitsToString(i.d).slice(0,s)){for(;p--;)i=round(i.times(i),s);return y.precision=h,null==t?(u=!0,round(i,h)):i}i=c}}function getBase10Exponent(e){for(var t=7*e.e,n=e.d[0];n>=10;n/=10)t++;return t}function getLn10(e,t,n){if(t>e.LN10.sd())throw u=!0,n&&(e.precision=n),Error(s+"LN10 precision limit exceeded");return round(new e(e.LN10),t)}function getZeroString(e){for(var t="";e--;)t+="0";return t}function ln(e,t){var n,o,i,c,l,f,p,d,y,h=1,g=e,m=g.d,_=g.constructor,x=_.precision;if(g.s<1)throw Error(s+(g.s?"NaN":"-Infinity"));if(g.eq(a))return new _(0);if(null==t?(u=!1,d=x):d=t,g.eq(10))return null==t&&(u=!0),getLn10(_,d);if(d+=10,_.precision=d,o=(n=digitsToString(m)).charAt(0),!(15e14>Math.abs(c=getBase10Exponent(g))))return p=getLn10(_,d+2,x).times(c+""),g=ln(new _(o+"."+n.slice(1)),d-10).plus(p),_.precision=x,null==t?(u=!0,round(g,x)):g;for(;o<7&&1!=o||1==o&&n.charAt(1)>3;)o=(n=digitsToString((g=g.times(e)).d)).charAt(0),h++;for(c=getBase10Exponent(g),o>1?(g=new _("0."+n),c++):g=new _(o+"."+n.slice(1)),f=l=g=b(g.minus(a),g.plus(a),d),y=round(g.times(g),d),i=3;;){if(l=round(l.times(y),d),digitsToString((p=f.plus(b(l,new _(i),d))).d).slice(0,d)===digitsToString(f.d).slice(0,d))return f=f.times(2),0!==c&&(f=f.plus(getLn10(_,d+2,x).times(c+""))),f=b(f,new _(h),d),_.precision=x,null==t?(u=!0,round(f,x)):f;f=p,i+=2}}function parseDecimal(e,t){var n,o,i;for((n=t.indexOf("."))>-1&&(t=t.replace(".","")),(o=t.search(/e/i))>0?(n<0&&(n=o),n+=+t.slice(o+1),t=t.substring(0,o)):n<0&&(n=t.length),o=0;48===t.charCodeAt(o);)++o;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(o,i)){if(i-=o,n=n-o-1,e.e=p(n/7),e.d=[],o=(n+1)%7,n<0&&(o+=7),o<i){for(o&&e.d.push(+t.slice(0,o)),i-=7;o<i;)e.d.push(+t.slice(o,o+=7));o=7-(t=t.slice(o)).length}else o-=i;for(;o--;)t+="0";if(e.d.push(+t),u&&(e.e>h||e.e<-h))throw Error(f+n)}else e.s=0,e.e=0,e.d=[0];return e}function round(e,t,n){var o,i,a,c,s,l,y,g,b=e.d;for(c=1,a=b[0];a>=10;a/=10)c++;if((o=t-c)<0)o+=7,i=t,y=b[g=0];else{if((g=Math.ceil((o+1)/7))>=(a=b.length))return e;for(c=1,y=a=b[g];a>=10;a/=10)c++;o%=7,i=o-7+c}if(void 0!==n&&(s=y/(a=d(10,c-i-1))%10|0,l=t<0||void 0!==b[g+1]||y%a,l=n<4?(s||l)&&(0==n||n==(e.s<0?3:2)):s>5||5==s&&(4==n||l||6==n&&(o>0?i>0?y/d(10,c-i):0:b[g-1])%10&1||n==(e.s<0?8:7))),t<1||!b[0])return l?(a=getBase10Exponent(e),b.length=1,t=t-a-1,b[0]=d(10,(7-t%7)%7),e.e=p(-t/7)||0):(b.length=1,b[0]=e.e=e.s=0),e;if(0==o?(b.length=g,a=1,g--):(b.length=g+1,a=d(10,7-o),b[g]=i>0?(y/d(10,c-i)%d(10,i)|0)*a:0),l)for(;;){if(0==g){1e7==(b[0]+=a)&&(b[0]=1,++e.e);break}if(b[g]+=a,1e7!=b[g])break;b[g--]=0,a=1}for(o=b.length;0===b[--o];)b.pop();if(u&&(e.e>h||e.e<-h))throw Error(f+getBase10Exponent(e));return e}function subtract(e,t){var n,o,i,a,c,s,l,f,p,d,y=e.constructor,h=y.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new y(e),u?round(t,h):t;if(l=e.d,d=t.d,o=t.e,f=e.e,l=l.slice(),c=f-o){for((p=c<0)?(n=l,c=-c,s=d.length):(n=d,o=f,s=l.length),c>(i=Math.max(Math.ceil(h/7),s)+2)&&(c=i,n.length=1),n.reverse(),i=c;i--;)n.push(0);n.reverse()}else{for((p=(i=l.length)<(s=d.length))&&(s=i),i=0;i<s;i++)if(l[i]!=d[i]){p=l[i]<d[i];break}c=0}for(p&&(n=l,l=d,d=n,t.s=-t.s),s=l.length,i=d.length-s;i>0;--i)l[s++]=0;for(i=d.length;i>c;){if(l[--i]<d[i]){for(a=i;a&&0===l[--a];)l[a]=1e7-1;--l[a],l[i]+=1e7}l[i]-=d[i]}for(;0===l[--s];)l.pop();for(;0===l[0];l.shift())--o;return l[0]?(t.d=l,t.e=o,u?round(t,h):t):new y(0)}function toString(e,t,n){var o,i=getBase10Exponent(e),a=digitsToString(e.d),c=a.length;return t?(n&&(o=n-c)>0?a=a.charAt(0)+"."+a.slice(1)+getZeroString(o):c>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+getZeroString(-i-1)+a,n&&(o=n-c)>0&&(a+=getZeroString(o))):i>=c?(a+=getZeroString(i+1-c),n&&(o=n-i-1)>0&&(a=a+"."+getZeroString(o))):((o=i+1)<c&&(a=a.slice(0,o)+"."+a.slice(o)),n&&(o=n-c)>0&&(i+1===c&&(a+="."),a+=getZeroString(o))),e.s<0?"-"+a:a}function truncate(e,t){if(e.length>t)return e.length=t,!0}function clone(e){var t,n,o;function Decimal(e){if(!(this instanceof Decimal))return new Decimal(e);if(this.constructor=Decimal,e instanceof Decimal){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(l+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return parseDecimal(this,e.toString())}if("string"!=typeof e)throw Error(l+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,y.test(e))parseDecimal(this,e);else throw Error(l+e)}if(Decimal.prototype=g,Decimal.ROUND_UP=0,Decimal.ROUND_DOWN=1,Decimal.ROUND_CEIL=2,Decimal.ROUND_FLOOR=3,Decimal.ROUND_HALF_UP=4,Decimal.ROUND_HALF_DOWN=5,Decimal.ROUND_HALF_EVEN=6,Decimal.ROUND_HALF_CEIL=7,Decimal.ROUND_HALF_FLOOR=8,Decimal.clone=clone,Decimal.config=Decimal.set=config,void 0===e&&(e={}),e)for(t=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];t<o.length;)e.hasOwnProperty(n=o[t++])||(e[n]=this[n]);return Decimal.config(e),Decimal}function config(e){if(!e||"object"!=typeof e)throw Error(s+"Object expected");var t,n,o,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(o=e[n=i[t]])){if(p(o)===o&&o>=i[t+1]&&o<=i[t+2])this[n]=o;else throw Error(l+n+": "+o)}if(void 0!==(o=e[n="LN10"])){if(o==Math.LN10)this[n]=new this(o);else throw Error(l+n+": "+o)}return this}(c=clone(c)).default=c.Decimal=c,a=new c(1),void 0!==(o=(function(){return c}).call(t,n,t,e))&&(e.exports=o)}(0)},8729:function(e){"use strict";var t=Object.prototype.hasOwnProperty,n="~";function Events(){}function EE(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function addListener(e,t,o,i,a){if("function"!=typeof o)throw TypeError("The listener must be a function");var c=new EE(o,i||e,a),u=n?n+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],c]:e._events[u].push(c):(e._events[u]=c,e._eventsCount++),e}function clearEvent(e,t){0==--e._eventsCount?e._events=new Events:delete e._events[t]}function EventEmitter(){this._events=new Events,this._eventsCount=0}Object.create&&(Events.prototype=Object.create(null),new Events().__proto__||(n=!1)),EventEmitter.prototype.eventNames=function(){var e,o,i=[];if(0===this._eventsCount)return i;for(o in e=this._events)t.call(e,o)&&i.push(n?o.slice(1):o);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},EventEmitter.prototype.listeners=function(e){var t=n?n+e:e,o=this._events[t];if(!o)return[];if(o.fn)return[o.fn];for(var i=0,a=o.length,c=Array(a);i<a;i++)c[i]=o[i].fn;return c},EventEmitter.prototype.listenerCount=function(e){var t=n?n+e:e,o=this._events[t];return o?o.fn?1:o.length:0},EventEmitter.prototype.emit=function(e,t,o,i,a,c){var u=n?n+e:e;if(!this._events[u])return!1;var s,l,f=this._events[u],p=arguments.length;if(f.fn){switch(f.once&&this.removeListener(e,f.fn,void 0,!0),p){case 1:return f.fn.call(f.context),!0;case 2:return f.fn.call(f.context,t),!0;case 3:return f.fn.call(f.context,t,o),!0;case 4:return f.fn.call(f.context,t,o,i),!0;case 5:return f.fn.call(f.context,t,o,i,a),!0;case 6:return f.fn.call(f.context,t,o,i,a,c),!0}for(l=1,s=Array(p-1);l<p;l++)s[l-1]=arguments[l];f.fn.apply(f.context,s)}else{var d,y=f.length;for(l=0;l<y;l++)switch(f[l].once&&this.removeListener(e,f[l].fn,void 0,!0),p){case 1:f[l].fn.call(f[l].context);break;case 2:f[l].fn.call(f[l].context,t);break;case 3:f[l].fn.call(f[l].context,t,o);break;case 4:f[l].fn.call(f[l].context,t,o,i);break;default:if(!s)for(d=1,s=Array(p-1);d<p;d++)s[d-1]=arguments[d];f[l].fn.apply(f[l].context,s)}}return!0},EventEmitter.prototype.on=function(e,t,n){return addListener(this,e,t,n,!1)},EventEmitter.prototype.once=function(e,t,n){return addListener(this,e,t,n,!0)},EventEmitter.prototype.removeListener=function(e,t,o,i){var a=n?n+e:e;if(!this._events[a])return this;if(!t)return clearEvent(this,a),this;var c=this._events[a];if(c.fn)c.fn!==t||i&&!c.once||o&&c.context!==o||clearEvent(this,a);else{for(var u=0,s=[],l=c.length;u<l;u++)(c[u].fn!==t||i&&!c[u].once||o&&c[u].context!==o)&&s.push(c[u]);s.length?this._events[a]=1===s.length?s[0]:s:clearEvent(this,a)}return this},EventEmitter.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&clearEvent(this,t)):(this._events=new Events,this._eventsCount=0),this},EventEmitter.prototype.off=EventEmitter.prototype.removeListener,EventEmitter.prototype.addListener=EventEmitter.prototype.on,EventEmitter.prefixed=n,EventEmitter.EventEmitter=EventEmitter,e.exports=EventEmitter},2666:function(e,t,n){var o=n(2603)(n(2242),"DataView");e.exports=o},5464:function(e,t,n){var o=n(2644),i=n(9334),a=n(1696),c=n(1553),u=n(9789);function Hash(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}Hash.prototype.clear=o,Hash.prototype.delete=i,Hash.prototype.get=a,Hash.prototype.has=c,Hash.prototype.set=u,e.exports=Hash},2096:function(e,t,n){var o=n(7767),i=n(4805),a=n(861),c=n(5153),u=n(4441);function ListCache(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}ListCache.prototype.clear=o,ListCache.prototype.delete=i,ListCache.prototype.get=a,ListCache.prototype.has=c,ListCache.prototype.set=u,e.exports=ListCache},5956:function(e,t,n){var o=n(2603)(n(2242),"Map");e.exports=o},9612:function(e,t,n){var o=n(3310),i=n(9530),a=n(4465),c=n(338),u=n(9610);function MapCache(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}MapCache.prototype.clear=o,MapCache.prototype.delete=i,MapCache.prototype.get=a,MapCache.prototype.has=c,MapCache.prototype.set=u,e.exports=MapCache},5710:function(e,t,n){var o=n(2603)(n(2242),"Promise");e.exports=o},8921:function(e,t,n){var o=n(2603)(n(2242),"Set");e.exports=o},6427:function(e,t,n){var o=n(9612),i=n(566),a=n(2884);function SetCache(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new o;++t<n;)this.add(e[t])}SetCache.prototype.add=SetCache.prototype.push=i,SetCache.prototype.has=a,e.exports=SetCache},8624:function(e,t,n){var o=n(2096),i=n(464),a=n(7473),c=n(5563),u=n(5518),s=n(5319);function Stack(e){var t=this.__data__=new o(e);this.size=t.size}Stack.prototype.clear=i,Stack.prototype.delete=a,Stack.prototype.get=c,Stack.prototype.has=u,Stack.prototype.set=s,e.exports=Stack},9029:function(e,t,n){var o=n(2242).Symbol;e.exports=o},8709:function(e,t,n){var o=n(2242).Uint8Array;e.exports=o},3449:function(e,t,n){var o=n(2603)(n(2242),"WeakMap");e.exports=o},2412:function(e){function apply(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}e.exports=apply},8110:function(e){function arrayEvery(e,t){for(var n=-1,o=null==e?0:e.length;++n<o;)if(!t(e[n],n,e))return!1;return!0}e.exports=arrayEvery},4822:function(e){function arrayFilter(e,t){for(var n=-1,o=null==e?0:e.length,i=0,a=[];++n<o;){var c=e[n];t(c,n,e)&&(a[i++]=c)}return a}e.exports=arrayFilter},7941:function(e,t,n){var o=n(5915);function arrayIncludes(e,t){return!!(null==e?0:e.length)&&o(e,t,0)>-1}e.exports=arrayIncludes},1128:function(e){function arrayIncludesWith(e,t,n){for(var o=-1,i=null==e?0:e.length;++o<i;)if(n(t,e[o]))return!0;return!1}e.exports=arrayIncludesWith},6515:function(e,t,n){var o=n(9843),i=n(514),a=n(2068),c=n(5067),u=n(1197),s=n(8160),l=Object.prototype.hasOwnProperty;function arrayLikeKeys(e,t){var n=a(e),f=!n&&i(e),p=!n&&!f&&c(e),d=!n&&!f&&!p&&s(e),y=n||f||p||d,h=y?o(e.length,String):[],g=h.length;for(var b in e)(t||l.call(e,b))&&!(y&&("length"==b||p&&("offset"==b||"parent"==b)||d&&("buffer"==b||"byteLength"==b||"byteOffset"==b)||u(b,g)))&&h.push(b);return h}e.exports=arrayLikeKeys},9121:function(e){function arrayMap(e,t){for(var n=-1,o=null==e?0:e.length,i=Array(o);++n<o;)i[n]=t(e[n],n,e);return i}e.exports=arrayMap},7238:function(e){function arrayPush(e,t){for(var n=-1,o=t.length,i=e.length;++n<o;)e[i+n]=t[n];return e}e.exports=arrayPush},8795:function(e){function arraySome(e,t){for(var n=-1,o=null==e?0:e.length;++n<o;)if(t(e[n],n,e))return!0;return!1}e.exports=arraySome},696:function(e){function asciiToArray(e){return e.split("")}e.exports=asciiToArray},9345:function(e,t,n){var o=n(600);function assocIndexOf(e,t){for(var n=e.length;n--;)if(o(e[n][0],t))return n;return -1}e.exports=assocIndexOf},173:function(e,t,n){var o=n(1674);function baseAssignValue(e,t,n){"__proto__"==t&&o?o(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}e.exports=baseAssignValue},3074:function(e,t,n){var o=n(3629),i=n(3835)(o);e.exports=i},8912:function(e,t,n){var o=n(3074);function baseEvery(e,t){var n=!0;return o(e,function(e,o,i){return n=!!t(e,o,i)}),n}e.exports=baseEvery},2042:function(e,t,n){var o=n(1087);function baseExtremum(e,t,n){for(var i=-1,a=e.length;++i<a;){var c=e[i],u=t(c);if(null!=u&&(void 0===s?u==u&&!o(u):n(u,s)))var s=u,l=c}return l}e.exports=baseExtremum},1787:function(e){function baseFindIndex(e,t,n,o){for(var i=e.length,a=n+(o?1:-1);o?a--:++a<i;)if(t(e[a],a,e))return a;return -1}e.exports=baseFindIndex},1101:function(e,t,n){var o=n(7238),i=n(4936);function baseFlatten(e,t,n,a,c){var u=-1,s=e.length;for(n||(n=i),c||(c=[]);++u<s;){var l=e[u];t>0&&n(l)?t>1?baseFlatten(l,t-1,n,a,c):o(c,l):a||(c[c.length]=l)}return c}e.exports=baseFlatten},4228:function(e,t,n){var o=n(1244)();e.exports=o},3629:function(e,t,n){var o=n(4228),i=n(5346);function baseForOwn(e,t){return e&&o(e,t,i)}e.exports=baseForOwn},1456:function(e,t,n){var o=n(9649),i=n(7990);function baseGet(e,t){t=o(t,e);for(var n=0,a=t.length;null!=e&&n<a;)e=e[i(t[n++])];return n&&n==a?e:void 0}e.exports=baseGet},766:function(e,t,n){var o=n(7238),i=n(2068);function baseGetAllKeys(e,t,n){var a=t(e);return i(e)?a:o(a,n(e))}e.exports=baseGetAllKeys},6714:function(e,t,n){var o=n(9029),i=n(5078),a=n(6276),c=o?o.toStringTag:void 0;function baseGetTag(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":c&&c in Object(e)?i(e):a(e)}e.exports=baseGetTag},218:function(e){function baseGt(e,t){return e>t}e.exports=baseGt},6457:function(e){function baseHasIn(e,t){return null!=e&&t in Object(e)}e.exports=baseHasIn},5915:function(e,t,n){var o=n(1787),i=n(9093),a=n(8665);function baseIndexOf(e,t,n){return t==t?a(e,t,n):o(e,i,n)}e.exports=baseIndexOf},2298:function(e,t,n){var o=n(6714),i=n(2387);function baseIsArguments(e){return i(e)&&"[object Arguments]"==o(e)}e.exports=baseIsArguments},6425:function(e,t,n){var o=n(6634),i=n(2387);function baseIsEqual(e,t,n,a,c){return e===t||(null!=e&&null!=t&&(i(e)||i(t))?o(e,t,n,a,baseIsEqual,c):e!=e&&t!=t)}e.exports=baseIsEqual},6634:function(e,t,n){var o=n(8624),i=n(9507),a=n(91),c=n(2080),u=n(9e3),s=n(2068),l=n(5067),f=n(8160),p="[object Arguments]",d="[object Array]",y="[object Object]",h=Object.prototype.hasOwnProperty;function baseIsEqualDeep(e,t,n,g,b,m){var _=s(e),x=s(t),S=_?d:u(e),P=x?d:u(t);S=S==p?y:S,P=P==p?y:P;var O=S==y,j=P==y,w=S==P;if(w&&l(e)){if(!l(t))return!1;_=!0,O=!1}if(w&&!O)return m||(m=new o),_||f(e)?i(e,t,n,g,b,m):a(e,t,S,n,g,b,m);if(!(1&n)){var C=O&&h.call(e,"__wrapped__"),A=j&&h.call(t,"__wrapped__");if(C||A){var T=C?e.value():e,k=A?t.value():t;return m||(m=new o),b(T,k,n,g,m)}}return!!w&&(m||(m=new o),c(e,t,n,g,b,m))}e.exports=baseIsEqualDeep},2757:function(e,t,n){var o=n(8624),i=n(6425);function baseIsMatch(e,t,n,a){var c=n.length,u=c,s=!a;if(null==e)return!u;for(e=Object(e);c--;){var l=n[c];if(s&&l[2]?l[1]!==e[l[0]]:!(l[0]in e))return!1}for(;++c<u;){var f=(l=n[c])[0],p=e[f],d=l[1];if(s&&l[2]){if(void 0===p&&!(f in e))return!1}else{var y=new o;if(a)var h=a(p,d,f,e,t,y);if(!(void 0===h?i(d,p,3,a,y):h))return!1}}return!0}e.exports=baseIsMatch},9093:function(e){function baseIsNaN(e){return e!=e}e.exports=baseIsNaN},6431:function(e,t,n){var o=n(8293),i=n(2981),a=n(6905),c=n(8825),u=/^\[object .+?Constructor\]$/,s=Object.prototype,l=Function.prototype.toString,f=s.hasOwnProperty,p=RegExp("^"+l.call(f).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function baseIsNative(e){return!(!a(e)||i(e))&&(o(e)?p:u).test(c(e))}e.exports=baseIsNative},1571:function(e,t,n){var o=n(6714),i=n(4594),a=n(2387),c={};function baseIsTypedArray(e){return a(e)&&i(e.length)&&!!c[o(e)]}c["[object Float32Array]"]=c["[object Float64Array]"]=c["[object Int8Array]"]=c["[object Int16Array]"]=c["[object Int32Array]"]=c["[object Uint8Array]"]=c["[object Uint8ClampedArray]"]=c["[object Uint16Array]"]=c["[object Uint32Array]"]=!0,c["[object Arguments]"]=c["[object Array]"]=c["[object ArrayBuffer]"]=c["[object Boolean]"]=c["[object DataView]"]=c["[object Date]"]=c["[object Error]"]=c["[object Function]"]=c["[object Map]"]=c["[object Number]"]=c["[object Object]"]=c["[object RegExp]"]=c["[object Set]"]=c["[object String]"]=c["[object WeakMap]"]=!1,e.exports=baseIsTypedArray},7322:function(e,t,n){var o=n(8127),i=n(4643),a=n(4646),c=n(2068),u=n(8919);function baseIteratee(e){return"function"==typeof e?e:null==e?a:"object"==typeof e?c(e)?i(e[0],e[1]):o(e):u(e)}e.exports=baseIteratee},9605:function(e,t,n){var o=n(4097),i=n(9024),a=Object.prototype.hasOwnProperty;function baseKeys(e){if(!o(e))return i(e);var t=[];for(var n in Object(e))a.call(e,n)&&"constructor"!=n&&t.push(n);return t}e.exports=baseKeys},101:function(e){function baseLt(e,t){return e<t}e.exports=baseLt},8829:function(e,t,n){var o=n(3074),i=n(5635);function baseMap(e,t){var n=-1,a=i(e)?Array(e.length):[];return o(e,function(e,o,i){a[++n]=t(e,o,i)}),a}e.exports=baseMap},8127:function(e,t,n){var o=n(2757),i=n(2055),a=n(8069);function baseMatches(e){var t=i(e);return 1==t.length&&t[0][2]?a(t[0][0],t[0][1]):function(n){return n===e||o(n,e,t)}}e.exports=baseMatches},4643:function(e,t,n){var o=n(6425),i=n(8614),a=n(2556),c=n(1846),u=n(8460),s=n(8069),l=n(7990);function baseMatchesProperty(e,t){return c(e)&&u(t)?s(l(e),t):function(n){var c=i(n,e);return void 0===c&&c===t?a(n,e):o(t,c,3)}}e.exports=baseMatchesProperty},9651:function(e,t,n){var o=n(9121),i=n(1456),a=n(7322),c=n(8829),u=n(5493),s=n(287),l=n(1319),f=n(4646),p=n(2068);function baseOrderBy(e,t,n){t=t.length?o(t,function(e){return p(e)?function(t){return i(t,1===e.length?e[0]:e)}:e}):[f];var d=-1;return t=o(t,s(a)),u(c(e,function(e,n,i){return{criteria:o(t,function(t){return t(e)}),index:++d,value:e}}),function(e,t){return l(e,t,n)})}e.exports=baseOrderBy},1357:function(e){function baseProperty(e){return function(t){return null==t?void 0:t[e]}}e.exports=baseProperty},9266:function(e,t,n){var o=n(1456);function basePropertyDeep(e){return function(t){return o(t,e)}}e.exports=basePropertyDeep},9022:function(e){var t=Math.ceil,n=Math.max;function baseRange(e,o,i,a){for(var c=-1,u=n(t((o-e)/(i||1)),0),s=Array(u);u--;)s[a?u:++c]=e,e+=i;return s}e.exports=baseRange},7485:function(e,t,n){var o=n(4646),i=n(5046),a=n(4017);function baseRest(e,t){return a(i(e,t,o),e+"")}e.exports=baseRest},2811:function(e,t,n){var o=n(2400),i=n(1674),a=n(4646),c=i?function(e,t){return i(e,"toString",{configurable:!0,enumerable:!1,value:o(t),writable:!0})}:a;e.exports=c},4932:function(e){function baseSlice(e,t,n){var o=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(n=n>i?i:n)<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var a=Array(i);++o<i;)a[o]=e[o+t];return a}e.exports=baseSlice},7339:function(e,t,n){var o=n(3074);function baseSome(e,t){var n;return o(e,function(e,o,i){return!(n=t(e,o,i))}),!!n}e.exports=baseSome},5493:function(e){function baseSortBy(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}e.exports=baseSortBy},9843:function(e){function baseTimes(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}e.exports=baseTimes},3511:function(e,t,n){var o=n(9029),i=n(9121),a=n(2068),c=n(1087),u=1/0,s=o?o.prototype:void 0,l=s?s.toString:void 0;function baseToString(e){if("string"==typeof e)return e;if(a(e))return i(e,baseToString)+"";if(c(e))return l?l.call(e):"";var t=e+"";return"0"==t&&1/e==-u?"-0":t}e.exports=baseToString},3225:function(e,t,n){var o=n(121),i=/^\s+/;function baseTrim(e){return e?e.slice(0,o(e)+1).replace(i,""):e}e.exports=baseTrim},287:function(e){function baseUnary(e){return function(t){return e(t)}}e.exports=baseUnary},9824:function(e,t,n){var o=n(6427),i=n(7941),a=n(1128),c=n(5225),u=n(1590),s=n(7782);function baseUniq(e,t,n){var l=-1,f=i,p=e.length,d=!0,y=[],h=y;if(n)d=!1,f=a;else if(p>=200){var g=t?null:u(e);if(g)return s(g);d=!1,f=c,h=new o}else h=t?[]:y;e:for(;++l<p;){var b=e[l],m=t?t(b):b;if(b=n||0!==b?b:0,d&&m==m){for(var _=h.length;_--;)if(h[_]===m)continue e;t&&h.push(m),y.push(b)}else f(h,m,n)||(h!==y&&h.push(m),y.push(b))}return y}e.exports=baseUniq},5225:function(e){function cacheHas(e,t){return e.has(t)}e.exports=cacheHas},9649:function(e,t,n){var o=n(2068),i=n(1846),a=n(9450),c=n(665);function castPath(e,t){return o(e)?e:i(e,t)?[e]:a(c(e))}e.exports=castPath},4298:function(e,t,n){var o=n(4932);function castSlice(e,t,n){var i=e.length;return n=void 0===n?i:n,!t&&n>=i?e:o(e,t,n)}e.exports=castSlice},3142:function(e,t,n){var o=n(1087);function compareAscending(e,t){if(e!==t){var n=void 0!==e,i=null===e,a=e==e,c=o(e),u=void 0!==t,s=null===t,l=t==t,f=o(t);if(!s&&!f&&!c&&e>t||c&&u&&l&&!s&&!f||i&&u&&l||!n&&l||!a)return 1;if(!i&&!c&&!f&&e<t||f&&n&&a&&!i&&!c||s&&n&&a||!u&&a||!l)return -1}return 0}e.exports=compareAscending},1319:function(e,t,n){var o=n(3142);function compareMultiple(e,t,n){for(var i=-1,a=e.criteria,c=t.criteria,u=a.length,s=n.length;++i<u;){var l=o(a[i],c[i]);if(l){if(i>=s)return l;return l*("desc"==n[i]?-1:1)}}return e.index-t.index}e.exports=compareMultiple},1186:function(e,t,n){var o=n(2242)["__core-js_shared__"];e.exports=o},3835:function(e,t,n){var o=n(5635);function createBaseEach(e,t){return function(n,i){if(null==n)return n;if(!o(n))return e(n,i);for(var a=n.length,c=t?a:-1,u=Object(n);(t?c--:++c<a)&&!1!==i(u[c],c,u););return n}}e.exports=createBaseEach},1244:function(e){function createBaseFor(e){return function(t,n,o){for(var i=-1,a=Object(t),c=o(t),u=c.length;u--;){var s=c[e?u:++i];if(!1===n(a[s],s,a))break}return t}}e.exports=createBaseFor},2937:function(e,t,n){var o=n(4298),i=n(7564),a=n(885),c=n(665);function createCaseFirst(e){return function(t){var n=i(t=c(t))?a(t):void 0,u=n?n[0]:t.charAt(0),s=n?o(n,1).join(""):t.slice(1);return u[e]()+s}}e.exports=createCaseFirst},8872:function(e,t,n){var o=n(7322),i=n(5635),a=n(5346);function createFind(e){return function(t,n,c){var u=Object(t);if(!i(t)){var s=o(n,3);t=a(t),n=function(e){return s(u[e],e,u)}}var l=e(t,n,c);return l>-1?u[s?t[l]:l]:void 0}}e.exports=createFind},9838:function(e,t,n){var o=n(9022),i=n(2664),a=n(8206);function createRange(e){return function(t,n,c){return c&&"number"!=typeof c&&i(t,n,c)&&(n=c=void 0),t=a(t),void 0===n?(n=t,t=0):n=a(n),c=void 0===c?t<n?1:-1:a(c),o(t,n,c,e)}}e.exports=createRange},1590:function(e,t,n){var o=n(8921),i=n(8660),a=n(7782),c=o&&1/a(new o([,-0]))[1]==1/0?function(e){return new o(e)}:i;e.exports=c},1674:function(e,t,n){var o=n(2603),i=function(){try{var e=o(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=i},9507:function(e,t,n){var o=n(6427),i=n(8795),a=n(5225);function equalArrays(e,t,n,c,u,s){var l=1&n,f=e.length,p=t.length;if(f!=p&&!(l&&p>f))return!1;var d=s.get(e),y=s.get(t);if(d&&y)return d==t&&y==e;var h=-1,g=!0,b=2&n?new o:void 0;for(s.set(e,t),s.set(t,e);++h<f;){var m=e[h],_=t[h];if(c)var x=l?c(_,m,h,t,e,s):c(m,_,h,e,t,s);if(void 0!==x){if(x)continue;g=!1;break}if(b){if(!i(t,function(e,t){if(!a(b,t)&&(m===e||u(m,e,n,c,s)))return b.push(t)})){g=!1;break}}else if(!(m===_||u(m,_,n,c,s))){g=!1;break}}return s.delete(e),s.delete(t),g}e.exports=equalArrays},91:function(e,t,n){var o=n(9029),i=n(8709),a=n(600),c=n(9507),u=n(9883),s=n(7782),l=o?o.prototype:void 0,f=l?l.valueOf:void 0;function equalByTag(e,t,n,o,l,p,d){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":if(e.byteLength!=t.byteLength||!p(new i(e),new i(t)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return a(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var y=u;case"[object Set]":var h=1&o;if(y||(y=s),e.size!=t.size&&!h)break;var g=d.get(e);if(g)return g==t;o|=2,d.set(e,t);var b=c(y(e),y(t),o,l,p,d);return d.delete(e),b;case"[object Symbol]":if(f)return f.call(e)==f.call(t)}return!1}e.exports=equalByTag},2080:function(e,t,n){var o=n(989),i=Object.prototype.hasOwnProperty;function equalObjects(e,t,n,a,c,u){var s=1&n,l=o(e),f=l.length;if(f!=o(t).length&&!s)return!1;for(var p=f;p--;){var d=l[p];if(!(s?d in t:i.call(t,d)))return!1}var y=u.get(e),h=u.get(t);if(y&&h)return y==t&&h==e;var g=!0;u.set(e,t),u.set(t,e);for(var b=s;++p<f;){var m=e[d=l[p]],_=t[d];if(a)var x=s?a(_,m,d,t,e,u):a(m,_,d,e,t,u);if(!(void 0===x?m===_||c(m,_,n,a,u):x)){g=!1;break}b||(b="constructor"==d)}if(g&&!b){var S=e.constructor,P=t.constructor;S!=P&&"constructor"in e&&"constructor"in t&&!("function"==typeof S&&S instanceof S&&"function"==typeof P&&P instanceof P)&&(g=!1)}return u.delete(e),u.delete(t),g}e.exports=equalObjects},6503:function(e,t,n){var o="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;e.exports=o},989:function(e,t,n){var o=n(766),i=n(1500),a=n(5346);function getAllKeys(e){return o(e,a,i)}e.exports=getAllKeys},2367:function(e,t,n){var o=n(3946);function getMapData(e,t){var n=e.__data__;return o(t)?n["string"==typeof t?"string":"hash"]:n.map}e.exports=getMapData},2055:function(e,t,n){var o=n(8460),i=n(5346);function getMatchData(e){for(var t=i(e),n=t.length;n--;){var a=t[n],c=e[a];t[n]=[a,c,o(c)]}return t}e.exports=getMatchData},2603:function(e,t,n){var o=n(6431),i=n(7747);function getNative(e,t){var n=i(e,t);return o(n)?n:void 0}e.exports=getNative},3362:function(e,t,n){var o=n(2184)(Object.getPrototypeOf,Object);e.exports=o},5078:function(e,t,n){var o=n(9029),i=Object.prototype,a=i.hasOwnProperty,c=i.toString,u=o?o.toStringTag:void 0;function getRawTag(e){var t=a.call(e,u),n=e[u];try{e[u]=void 0;var o=!0}catch(e){}var i=c.call(e);return o&&(t?e[u]=n:delete e[u]),i}e.exports=getRawTag},1500:function(e,t,n){var o=n(4822),i=n(2501),a=Object.prototype.propertyIsEnumerable,c=Object.getOwnPropertySymbols,u=c?function(e){return null==e?[]:o(c(e=Object(e)),function(t){return a.call(e,t)})}:i;e.exports=u},9e3:function(e,t,n){var o=n(2666),i=n(5956),a=n(5710),c=n(8921),u=n(3449),s=n(6714),l=n(8825),f="[object Map]",p="[object Promise]",d="[object Set]",y="[object WeakMap]",h="[object DataView]",g=l(o),b=l(i),m=l(a),_=l(c),x=l(u),S=s;(o&&S(new o(new ArrayBuffer(1)))!=h||i&&S(new i)!=f||a&&S(a.resolve())!=p||c&&S(new c)!=d||u&&S(new u)!=y)&&(S=function(e){var t=s(e),n="[object Object]"==t?e.constructor:void 0,o=n?l(n):"";if(o)switch(o){case g:return h;case b:return f;case m:return p;case _:return d;case x:return y}return t}),e.exports=S},7747:function(e){function getValue(e,t){return null==e?void 0:e[t]}e.exports=getValue},5355:function(e,t,n){var o=n(9649),i=n(514),a=n(2068),c=n(1197),u=n(4594),s=n(7990);function hasPath(e,t,n){t=o(t,e);for(var l=-1,f=t.length,p=!1;++l<f;){var d=s(t[l]);if(!(p=null!=e&&n(e,d)))break;e=e[d]}return p||++l!=f?p:!!(f=null==e?0:e.length)&&u(f)&&c(d,f)&&(a(e)||i(e))}e.exports=hasPath},7564:function(e){var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");function hasUnicode(e){return t.test(e)}e.exports=hasUnicode},2644:function(e,t,n){var o=n(8749);function hashClear(){this.__data__=o?o(null):{},this.size=0}e.exports=hashClear},9334:function(e){function hashDelete(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}e.exports=hashDelete},1696:function(e,t,n){var o=n(8749),i=Object.prototype.hasOwnProperty;function hashGet(e){var t=this.__data__;if(o){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return i.call(t,e)?t[e]:void 0}e.exports=hashGet},1553:function(e,t,n){var o=n(8749),i=Object.prototype.hasOwnProperty;function hashHas(e){var t=this.__data__;return o?void 0!==t[e]:i.call(t,e)}e.exports=hashHas},9789:function(e,t,n){var o=n(8749);function hashSet(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=o&&void 0===t?"__lodash_hash_undefined__":t,this}e.exports=hashSet},4936:function(e,t,n){var o=n(9029),i=n(514),a=n(2068),c=o?o.isConcatSpreadable:void 0;function isFlattenable(e){return a(e)||i(e)||!!(c&&e&&e[c])}e.exports=isFlattenable},1197:function(e){var t=/^(?:0|[1-9]\d*)$/;function isIndex(e,n){var o=typeof e;return!!(n=null==n?9007199254740991:n)&&("number"==o||"symbol"!=o&&t.test(e))&&e>-1&&e%1==0&&e<n}e.exports=isIndex},2664:function(e,t,n){var o=n(600),i=n(5635),a=n(1197),c=n(6905);function isIterateeCall(e,t,n){if(!c(n))return!1;var u=typeof t;return("number"==u?!!(i(n)&&a(t,n.length)):"string"==u&&t in n)&&o(n[t],e)}e.exports=isIterateeCall},1846:function(e,t,n){var o=n(2068),i=n(1087),a=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,c=/^\w*$/;function isKey(e,t){if(o(e))return!1;var n=typeof e;return!!("number"==n||"symbol"==n||"boolean"==n||null==e||i(e))||c.test(e)||!a.test(e)||null!=t&&e in Object(t)}e.exports=isKey},3946:function(e){function isKeyable(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}e.exports=isKeyable},2981:function(e,t,n){var o,i=n(1186),a=(o=/[^.]+$/.exec(i&&i.keys&&i.keys.IE_PROTO||""))?"Symbol(src)_1."+o:"";function isMasked(e){return!!a&&a in e}e.exports=isMasked},4097:function(e){var t=Object.prototype;function isPrototype(e){var n=e&&e.constructor;return e===("function"==typeof n&&n.prototype||t)}e.exports=isPrototype},8460:function(e,t,n){var o=n(6905);function isStrictComparable(e){return e==e&&!o(e)}e.exports=isStrictComparable},7767:function(e){function listCacheClear(){this.__data__=[],this.size=0}e.exports=listCacheClear},4805:function(e,t,n){var o=n(9345),i=Array.prototype.splice;function listCacheDelete(e){var t=this.__data__,n=o(t,e);return!(n<0)&&(n==t.length-1?t.pop():i.call(t,n,1),--this.size,!0)}e.exports=listCacheDelete},861:function(e,t,n){var o=n(9345);function listCacheGet(e){var t=this.__data__,n=o(t,e);return n<0?void 0:t[n][1]}e.exports=listCacheGet},5153:function(e,t,n){var o=n(9345);function listCacheHas(e){return o(this.__data__,e)>-1}e.exports=listCacheHas},4441:function(e,t,n){var o=n(9345);function listCacheSet(e,t){var n=this.__data__,i=o(n,e);return i<0?(++this.size,n.push([e,t])):n[i][1]=t,this}e.exports=listCacheSet},3310:function(e,t,n){var o=n(5464),i=n(2096),a=n(5956);function mapCacheClear(){this.size=0,this.__data__={hash:new o,map:new(a||i),string:new o}}e.exports=mapCacheClear},9530:function(e,t,n){var o=n(2367);function mapCacheDelete(e){var t=o(this,e).delete(e);return this.size-=t?1:0,t}e.exports=mapCacheDelete},4465:function(e,t,n){var o=n(2367);function mapCacheGet(e){return o(this,e).get(e)}e.exports=mapCacheGet},338:function(e,t,n){var o=n(2367);function mapCacheHas(e){return o(this,e).has(e)}e.exports=mapCacheHas},9610:function(e,t,n){var o=n(2367);function mapCacheSet(e,t){var n=o(this,e),i=n.size;return n.set(e,t),this.size+=n.size==i?0:1,this}e.exports=mapCacheSet},9883:function(e){function mapToArray(e){var t=-1,n=Array(e.size);return e.forEach(function(e,o){n[++t]=[o,e]}),n}e.exports=mapToArray},8069:function(e){function matchesStrictComparable(e,t){return function(n){return null!=n&&n[e]===t&&(void 0!==t||e in Object(n))}}e.exports=matchesStrictComparable},4670:function(e,t,n){var o=n(3023);function memoizeCapped(e){var t=o(e,function(e){return 500===n.size&&n.clear(),e}),n=t.cache;return t}e.exports=memoizeCapped},8749:function(e,t,n){var o=n(2603)(Object,"create");e.exports=o},9024:function(e,t,n){var o=n(2184)(Object.keys,Object);e.exports=o},3524:function(e,t,n){e=n.nmd(e);var o=n(6503),i=t&&!t.nodeType&&t,a=i&&e&&!e.nodeType&&e,c=a&&a.exports===i&&o.process,u=function(){try{var e=a&&a.require&&a.require("util").types;if(e)return e;return c&&c.binding&&c.binding("util")}catch(e){}}();e.exports=u},6276:function(e){var t=Object.prototype.toString;function objectToString(e){return t.call(e)}e.exports=objectToString},2184:function(e){function overArg(e,t){return function(n){return e(t(n))}}e.exports=overArg},5046:function(e,t,n){var o=n(2412),i=Math.max;function overRest(e,t,n){return t=i(void 0===t?e.length-1:t,0),function(){for(var a=arguments,c=-1,u=i(a.length-t,0),s=Array(u);++c<u;)s[c]=a[t+c];c=-1;for(var l=Array(t+1);++c<t;)l[c]=a[c];return l[t]=n(s),o(e,this,l)}}e.exports=overRest},2242:function(e,t,n){var o=n(6503),i="object"==typeof self&&self&&self.Object===Object&&self,a=o||i||Function("return this")();e.exports=a},566:function(e){function setCacheAdd(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}e.exports=setCacheAdd},2884:function(e){function setCacheHas(e){return this.__data__.has(e)}e.exports=setCacheHas},7782:function(e){function setToArray(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=e}),n}e.exports=setToArray},4017:function(e,t,n){var o=n(2811),i=n(8546)(o);e.exports=i},8546:function(e){var t=Date.now;function shortOut(e){var n=0,o=0;return function(){var i=t(),a=16-(i-o);if(o=i,a>0){if(++n>=800)return arguments[0]}else n=0;return e.apply(void 0,arguments)}}e.exports=shortOut},464:function(e,t,n){var o=n(2096);function stackClear(){this.__data__=new o,this.size=0}e.exports=stackClear},7473:function(e){function stackDelete(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}e.exports=stackDelete},5563:function(e){function stackGet(e){return this.__data__.get(e)}e.exports=stackGet},5518:function(e){function stackHas(e){return this.__data__.has(e)}e.exports=stackHas},5319:function(e,t,n){var o=n(2096),i=n(5956),a=n(9612);function stackSet(e,t){var n=this.__data__;if(n instanceof o){var c=n.__data__;if(!i||c.length<199)return c.push([e,t]),this.size=++n.size,this;n=this.__data__=new a(c)}return n.set(e,t),this.size=n.size,this}e.exports=stackSet},8665:function(e){function strictIndexOf(e,t,n){for(var o=n-1,i=e.length;++o<i;)if(e[o]===t)return o;return -1}e.exports=strictIndexOf},885:function(e,t,n){var o=n(696),i=n(7564),a=n(8358);function stringToArray(e){return i(e)?a(e):o(e)}e.exports=stringToArray},9450:function(e,t,n){var o=n(4670),i=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,a=/\\(\\)?/g,c=o(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(i,function(e,n,o,i){t.push(o?i.replace(a,"$1"):n||e)}),t});e.exports=c},7990:function(e,t,n){var o=n(1087),i=1/0;function toKey(e){if("string"==typeof e||o(e))return e;var t=e+"";return"0"==t&&1/e==-i?"-0":t}e.exports=toKey},8825:function(e){var t=Function.prototype.toString;function toSource(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}e.exports=toSource},121:function(e){var t=/\s/;function trimmedEndIndex(e){for(var n=e.length;n--&&t.test(e.charAt(n)););return n}e.exports=trimmedEndIndex},8358:function(e){var t="\ud800-\udfff",n="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",o="\ud83c[\udffb-\udfff]",i="[^"+t+"]",a="(?:\ud83c[\udde6-\uddff]){2}",c="[\ud800-\udbff][\udc00-\udfff]",u="(?:"+n+"|"+o+")?",s="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[i,a,c].join("|")+")"+s+u+")*",f=RegExp(o+"(?="+o+")|(?:"+[i+n+"?",n,a,c,"["+t+"]"].join("|")+")"+(s+u+l),"g");function unicodeToArray(e){return e.match(f)||[]}e.exports=unicodeToArray},2400:function(e){function constant(e){return function(){return e}}e.exports=constant},8143:function(e,t,n){var o=n(6905),i=n(4752),a=n(1573),c=Math.max,u=Math.min;function debounce(e,t,n){var s,l,f,p,d,y,h=0,g=!1,b=!1,m=!0;if("function"!=typeof e)throw TypeError("Expected a function");function invokeFunc(t){var n=s,o=l;return s=l=void 0,h=t,p=e.apply(o,n)}function leadingEdge(e){return h=e,d=setTimeout(timerExpired,t),g?invokeFunc(e):p}function remainingWait(e){var n=e-y,o=e-h,i=t-n;return b?u(i,f-o):i}function shouldInvoke(e){var n=e-y,o=e-h;return void 0===y||n>=t||n<0||b&&o>=f}function timerExpired(){var e=i();if(shouldInvoke(e))return trailingEdge(e);d=setTimeout(timerExpired,remainingWait(e))}function trailingEdge(e){return(d=void 0,m&&s)?invokeFunc(e):(s=l=void 0,p)}function cancel(){void 0!==d&&clearTimeout(d),h=0,s=y=l=d=void 0}function flush(){return void 0===d?p:trailingEdge(i())}function debounced(){var e=i(),n=shouldInvoke(e);if(s=arguments,l=this,y=e,n){if(void 0===d)return leadingEdge(y);if(b)return clearTimeout(d),d=setTimeout(timerExpired,t),invokeFunc(y)}return void 0===d&&(d=setTimeout(timerExpired,t)),p}return t=a(t)||0,o(n)&&(g=!!n.leading,f=(b="maxWait"in n)?c(a(n.maxWait)||0,t):f,m="trailing"in n?!!n.trailing:m),debounced.cancel=cancel,debounced.flush=flush,debounced}e.exports=debounce},600:function(e){function eq(e,t){return e===t||e!=e&&t!=t}e.exports=eq},8022:function(e,t,n){var o=n(8110),i=n(8912),a=n(7322),c=n(2068),u=n(2664);function every(e,t,n){var s=c(e)?o:i;return n&&u(e,t,n)&&(t=void 0),s(e,a(t,3))}e.exports=every},1330:function(e,t,n){var o=n(8872)(n(2076));e.exports=o},2076:function(e,t,n){var o=n(1787),i=n(7322),a=n(2919),c=Math.max;function findIndex(e,t,n){var u=null==e?0:e.length;if(!u)return -1;var s=null==n?0:a(n);return s<0&&(s=c(u+s,0)),o(e,i(t,3),s)}e.exports=findIndex},437:function(e,t,n){var o=n(1101),i=n(4853);function flatMap(e,t){return o(i(e,t),1)}e.exports=flatMap},8614:function(e,t,n){var o=n(1456);function get(e,t,n){var i=null==e?void 0:o(e,t);return void 0===i?n:i}e.exports=get},2556:function(e,t,n){var o=n(6457),i=n(5355);function hasIn(e,t){return null!=e&&i(e,t,o)}e.exports=hasIn},4646:function(e){function identity(e){return e}e.exports=identity},514:function(e,t,n){var o=n(2298),i=n(2387),a=Object.prototype,c=a.hasOwnProperty,u=a.propertyIsEnumerable,s=o(function(){return arguments}())?o:function(e){return i(e)&&c.call(e,"callee")&&!u.call(e,"callee")};e.exports=s},2068:function(e){var t=Array.isArray;e.exports=t},5635:function(e,t,n){var o=n(8293),i=n(4594);function isArrayLike(e){return null!=e&&i(e.length)&&!o(e)}e.exports=isArrayLike},2972:function(e,t,n){var o=n(6714),i=n(2387);function isBoolean(e){return!0===e||!1===e||i(e)&&"[object Boolean]"==o(e)}e.exports=isBoolean},5067:function(e,t,n){e=n.nmd(e);var o=n(2242),i=n(7644),a=t&&!t.nodeType&&t,c=a&&e&&!e.nodeType&&e,u=c&&c.exports===a?o.Buffer:void 0,s=u?u.isBuffer:void 0;e.exports=s||i},2077:function(e,t,n){var o=n(6425);function isEqual(e,t){return o(e,t)}e.exports=isEqual},8293:function(e,t,n){var o=n(6714),i=n(6905);function isFunction(e){if(!i(e))return!1;var t=o(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}e.exports=isFunction},4594:function(e){function isLength(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}e.exports=isLength},9027:function(e,t,n){var o=n(1881);function isNaN(e){return o(e)&&e!=+e}e.exports=isNaN},2727:function(e){function isNil(e){return null==e}e.exports=isNil},1881:function(e,t,n){var o=n(6714),i=n(2387);function isNumber(e){return"number"==typeof e||i(e)&&"[object Number]"==o(e)}e.exports=isNumber},6905:function(e){function isObject(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}e.exports=isObject},2387:function(e){function isObjectLike(e){return null!=e&&"object"==typeof e}e.exports=isObjectLike},4412:function(e,t,n){var o=n(6714),i=n(3362),a=n(2387),c=Object.prototype,u=Function.prototype.toString,s=c.hasOwnProperty,l=u.call(Object);function isPlainObject(e){if(!a(e)||"[object Object]"!=o(e))return!1;var t=i(e);if(null===t)return!0;var n=s.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&u.call(n)==l}e.exports=isPlainObject},3874:function(e,t,n){var o=n(6714),i=n(2068),a=n(2387);function isString(e){return"string"==typeof e||!i(e)&&a(e)&&"[object String]"==o(e)}e.exports=isString},1087:function(e,t,n){var o=n(6714),i=n(2387);function isSymbol(e){return"symbol"==typeof e||i(e)&&"[object Symbol]"==o(e)}e.exports=isSymbol},8160:function(e,t,n){var o=n(1571),i=n(287),a=n(3524),c=a&&a.isTypedArray,u=c?i(c):o;e.exports=u},5346:function(e,t,n){var o=n(6515),i=n(9605),a=n(5635);function keys(e){return a(e)?o(e):i(e)}e.exports=keys},4388:function(e){function last(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}e.exports=last},4853:function(e,t,n){var o=n(9121),i=n(7322),a=n(8829),c=n(2068);function map(e,t){return(c(e)?o:a)(e,i(t,3))}e.exports=map},1873:function(e,t,n){var o=n(173),i=n(3629),a=n(7322);function mapValues(e,t){var n={};return t=a(t,3),i(e,function(e,i,a){o(n,i,t(e,i,a))}),n}e.exports=mapValues},8412:function(e,t,n){var o=n(2042),i=n(218),a=n(4646);function max(e){return e&&e.length?o(e,a,i):void 0}e.exports=max},4573:function(e,t,n){var o=n(2042),i=n(218),a=n(7322);function maxBy(e,t){return e&&e.length?o(e,a(t,2),i):void 0}e.exports=maxBy},3023:function(e,t,n){var o=n(9612);function memoize(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var memoized=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=memoized.cache;if(i.has(o))return i.get(o);var a=e.apply(this,n);return memoized.cache=i.set(o,a)||i,a};return memoized.cache=new(memoize.Cache||o),memoized}memoize.Cache=o,e.exports=memoize},1843:function(e,t,n){var o=n(2042),i=n(101),a=n(4646);function min(e){return e&&e.length?o(e,a,i):void 0}e.exports=min},175:function(e,t,n){var o=n(2042),i=n(7322),a=n(101);function minBy(e,t){return e&&e.length?o(e,i(t,2),a):void 0}e.exports=minBy},8660:function(e){function noop(){}e.exports=noop},4752:function(e,t,n){var o=n(2242);e.exports=function(){return o.Date.now()}},8919:function(e,t,n){var o=n(1357),i=n(9266),a=n(1846),c=n(7990);function property(e){return a(e)?o(c(e)):i(e)}e.exports=property},4943:function(e,t,n){var o=n(9838)();e.exports=o},7874:function(e,t,n){var o=n(8795),i=n(7322),a=n(7339),c=n(2068),u=n(2664);function some(e,t,n){var s=c(e)?o:a;return n&&u(e,t,n)&&(t=void 0),s(e,i(t,3))}e.exports=some},1864:function(e,t,n){var o=n(1101),i=n(9651),a=n(7485),c=n(2664),u=a(function(e,t){if(null==e)return[];var n=t.length;return n>1&&c(e,t[0],t[1])?t=[]:n>2&&c(t[0],t[1],t[2])&&(t=[t[0]]),i(e,o(t,1),[])});e.exports=u},2501:function(e){function stubArray(){return[]}e.exports=stubArray},7644:function(e){function stubFalse(){return!1}e.exports=stubFalse},7269:function(e,t,n){var o=n(8143),i=n(6905);function throttle(e,t,n){var a=!0,c=!0;if("function"!=typeof e)throw TypeError("Expected a function");return i(n)&&(a="leading"in n?!!n.leading:a,c="trailing"in n?!!n.trailing:c),o(e,t,{leading:a,maxWait:t,trailing:c})}e.exports=throttle},8206:function(e,t,n){var o=n(1573),i=1/0;function toFinite(e){return e?(e=o(e))===i||e===-i?(e<0?-1:1)*17976931348623157e292:e==e?e:0:0===e?e:0}e.exports=toFinite},2919:function(e,t,n){var o=n(8206);function toInteger(e){var t=o(e),n=t%1;return t==t?n?t-n:t:0}e.exports=toInteger},1573:function(e,t,n){var o=n(3225),i=n(6905),a=n(1087),c=0/0,u=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,l=/^0o[0-7]+$/i,f=parseInt;function toNumber(e){if("number"==typeof e)return e;if(a(e))return c;if(i(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=i(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=o(e);var n=s.test(e);return n||l.test(e)?f(e.slice(2),n?2:8):u.test(e)?c:+e}e.exports=toNumber},665:function(e,t,n){var o=n(3511);function toString(e){return null==e?"":o(e)}e.exports=toString},3705:function(e,t,n){var o=n(7322),i=n(9824);function uniqBy(e,t){return e&&e.length?i(e,o(t,2)):[]}e.exports=uniqBy},1008:function(e,t,n){var o=n(2937)("toUpperCase");e.exports=o},2898:function(e,t,n){"use strict";n.d(t,{Z:function(){return createLucideIcon}});var o=n(2265),i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),createLucideIcon=(e,t)=>{let n=(0,o.forwardRef)(({color:n="currentColor",size:a=24,strokeWidth:c=2,absoluteStrokeWidth:u,className:s="",children:l,...f},p)=>(0,o.createElement)("svg",{ref:p,...i,width:a,height:a,stroke:n,strokeWidth:u?24*Number(c)/Number(a):c,className:["lucide",`lucide-${toKebabCase(e)}`,s].join(" "),...f},[...t.map(([e,t])=>(0,o.createElement)(e,t)),...Array.isArray(l)?l:[l]]));return n.displayName=`${e}`,n}},998:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},1981:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},2894:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2457:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3008:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6141:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},8734:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("File",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}]])},8956:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},6264:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1827:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},5790:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},1541:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},7972:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2104:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var o=n(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},622:function(e,t,n){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var o=n(2265),i=Symbol.for("react.element"),a=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),c=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,n){var o,s={},l=null,f=null;for(o in void 0!==n&&(l=""+n),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(f=t.ref),t)a.call(t,o)&&!u.hasOwnProperty(o)&&(s[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps)void 0===s[o]&&(s[o]=t[o]);return{$$typeof:i,type:e,key:l,ref:f,props:s,_owner:c.current}}t.jsx=q,t.jsxs=q},7437:function(e,t,n){"use strict";e.exports=n(622)},3018:function(e,t,n){"use strict";var o=n(1289);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,n,i,a,c){if(c!==o){var u=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},4275:function(e,t,n){e.exports=n(3018)()},1289:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},5010:function(e,t,n){"use strict";n.d(t,{ZP:function(){return S}});var o=n(2265),i=n(4275),a=n.n(i),c=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty;function combineComparators(e,t){return function(n,o,i){return e(n,o,i)&&t(n,o,i)}}function createIsCircular(e){return function(t,n,o){if(!t||!n||"object"!=typeof t||"object"!=typeof n)return e(t,n,o);var i=o.cache,a=i.get(t),c=i.get(n);if(a&&c)return a===n&&c===t;i.set(t,n),i.set(n,t);var u=e(t,n,o);return i.delete(t),i.delete(n),u}}function getStrictProperties(e){return c(e).concat(u(e))}var l=Object.hasOwn||function(e,t){return s.call(e,t)};function sameValueZeroEqual(e,t){return e===t||!e&&!t&&e!=e&&t!=t}var f=Object.getOwnPropertyDescriptor,p=Object.keys;function areArraysEqual(e,t,n){var o=e.length;if(t.length!==o)return!1;for(;o-- >0;)if(!n.equals(e[o],t[o],o,o,e,t,n))return!1;return!0}function areDatesEqual(e,t){return sameValueZeroEqual(e.getTime(),t.getTime())}function areErrorsEqual(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function areFunctionsEqual(e,t){return e===t}function areMapsEqual(e,t,n){var o,i,a=e.size;if(a!==t.size)return!1;if(!a)return!0;for(var c=Array(a),u=e.entries(),s=0;(o=u.next())&&!o.done;){for(var l=t.entries(),f=!1,p=0;(i=l.next())&&!i.done;){if(c[p]){p++;continue}var d=o.value,y=i.value;if(n.equals(d[0],y[0],s,p,e,t,n)&&n.equals(d[1],y[1],d[0],y[0],e,t,n)){f=c[p]=!0;break}p++}if(!f)return!1;s++}return!0}var d=sameValueZeroEqual;function areObjectsEqual(e,t,n){var o=p(e),i=o.length;if(p(t).length!==i)return!1;for(;i-- >0;)if(!isPropertyEqual(e,t,n,o[i]))return!1;return!0}function areObjectsEqualStrict(e,t,n){var o,i,a,c=getStrictProperties(e),u=c.length;if(getStrictProperties(t).length!==u)return!1;for(;u-- >0;)if(!isPropertyEqual(e,t,n,o=c[u])||(i=f(e,o),a=f(t,o),(i||a)&&(!i||!a||i.configurable!==a.configurable||i.enumerable!==a.enumerable||i.writable!==a.writable)))return!1;return!0}function arePrimitiveWrappersEqual(e,t){return sameValueZeroEqual(e.valueOf(),t.valueOf())}function areRegExpsEqual(e,t){return e.source===t.source&&e.flags===t.flags}function areSetsEqual(e,t,n){var o,i,a=e.size;if(a!==t.size)return!1;if(!a)return!0;for(var c=Array(a),u=e.values();(o=u.next())&&!o.done;){for(var s=t.values(),l=!1,f=0;(i=s.next())&&!i.done;){if(!c[f]&&n.equals(o.value,i.value,o.value,i.value,e,t,n)){l=c[f]=!0;break}f++}if(!l)return!1}return!0}function areTypedArraysEqual(e,t){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(e[n]!==t[n])return!1;return!0}function areUrlsEqual(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function isPropertyEqual(e,t,n,o){return("_owner"===o||"__o"===o||"__v"===o)&&(!!e.$$typeof||!!t.$$typeof)||l(t,o)&&n.equals(e[o],t[o],o,o,e,t,n)}var y=Array.isArray,h="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,g=Object.assign,b=Object.prototype.toString.call.bind(Object.prototype.toString);function createEqualityComparator(e){var t=e.areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,s=e.arePrimitiveWrappersEqual,l=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,d=e.areUrlsEqual;return function(e,g,m){if(e===g)return!0;if(null==e||null==g)return!1;var _=typeof e;if(_!==typeof g)return!1;if("object"!==_)return"number"===_?c(e,g,m):"function"===_&&i(e,g,m);var x=e.constructor;if(x!==g.constructor)return!1;if(x===Object)return u(e,g,m);if(y(e))return t(e,g,m);if(null!=h&&h(e))return p(e,g,m);if(x===Date)return n(e,g,m);if(x===RegExp)return l(e,g,m);if(x===Map)return a(e,g,m);if(x===Set)return f(e,g,m);var S=b(e);return"[object Date]"===S?n(e,g,m):"[object RegExp]"===S?l(e,g,m):"[object Map]"===S?a(e,g,m):"[object Set]"===S?f(e,g,m):"[object Object]"===S?"function"!=typeof e.then&&"function"!=typeof g.then&&u(e,g,m):"[object URL]"===S?d(e,g,m):"[object Error]"===S?o(e,g,m):"[object Arguments]"===S?u(e,g,m):("[object Boolean]"===S||"[object Number]"===S||"[object String]"===S)&&s(e,g,m)}}function createEqualityComparatorConfig(e){var t=e.circular,n=e.createCustomConfig,o=e.strict,i={areArraysEqual:o?areObjectsEqualStrict:areArraysEqual,areDatesEqual:areDatesEqual,areErrorsEqual:areErrorsEqual,areFunctionsEqual:areFunctionsEqual,areMapsEqual:o?combineComparators(areMapsEqual,areObjectsEqualStrict):areMapsEqual,areNumbersEqual:d,areObjectsEqual:o?areObjectsEqualStrict:areObjectsEqual,arePrimitiveWrappersEqual:arePrimitiveWrappersEqual,areRegExpsEqual:areRegExpsEqual,areSetsEqual:o?combineComparators(areSetsEqual,areObjectsEqualStrict):areSetsEqual,areTypedArraysEqual:o?areObjectsEqualStrict:areTypedArraysEqual,areUrlsEqual:areUrlsEqual};if(n&&(i=g({},i,n(i))),t){var a=createIsCircular(i.areArraysEqual),c=createIsCircular(i.areMapsEqual),u=createIsCircular(i.areObjectsEqual),s=createIsCircular(i.areSetsEqual);i=g({},i,{areArraysEqual:a,areMapsEqual:c,areObjectsEqual:u,areSetsEqual:s})}return i}function createInternalEqualityComparator(e){return function(t,n,o,i,a,c,u){return e(t,n,u)}}function createIsEqual(e){var t=e.circular,n=e.comparator,o=e.createState,i=e.equals,a=e.strict;if(o)return function(e,c){var u=o(),s=u.cache;return n(e,c,{cache:void 0===s?t?new WeakMap:void 0:s,equals:i,meta:u.meta,strict:a})};if(t)return function(e,t){return n(e,t,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var c={cache:void 0,equals:i,meta:void 0,strict:a};return function(e,t){return n(e,t,c)}}var m=createCustomEqual();function createCustomEqual(e){void 0===e&&(e={});var t=e.circular,n=e.createInternalComparator,o=e.createState,i=e.strict,a=createEqualityComparator(createEqualityComparatorConfig(e)),c=n?n(a):createInternalEqualityComparator(a);return createIsEqual({circular:void 0!==t&&t,comparator:a,createState:o,equals:c,strict:void 0!==i&&i})}function safeRequestAnimationFrame(e){"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(e)}function setRafTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=-1;requestAnimationFrame(function shouldUpdate(o){n<0&&(n=o),o-n>t?(e(o),n=-1):safeRequestAnimationFrame(shouldUpdate)})}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _toArray(e){return _arrayWithHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableRest()}function _nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithHoles(e){if(Array.isArray(e))return e}function createAnimateManager(){var handleChange=function(){return null},e=!1,setStyle=function setStyle(t){if(!e){if(Array.isArray(t)){if(!t.length)return;var n=_toArray(t),o=n[0],i=n.slice(1);if("number"==typeof o){setRafTimeout(setStyle.bind(null,i),o);return}setStyle(o),setRafTimeout(setStyle.bind(null,i));return}"object"===_typeof(t)&&handleChange(t),"function"==typeof t&&t()}};return{stop:function(){e=!0},start:function(t){e=!1,setStyle(t)},subscribe:function(e){return handleChange=e,function(){handleChange=function(){return null}}}}}function util_typeof(e){return(util_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"===util_typeof(t)?t:String(t)}function _toPrimitive(e,t){if("object"!==util_typeof(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==util_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}createCustomEqual({strict:!0}),createCustomEqual({circular:!0}),createCustomEqual({circular:!0,strict:!0}),createCustomEqual({createInternalComparator:function(){return sameValueZeroEqual}}),createCustomEqual({strict:!0,createInternalComparator:function(){return sameValueZeroEqual}}),createCustomEqual({circular:!0,createInternalComparator:function(){return sameValueZeroEqual}}),createCustomEqual({circular:!0,createInternalComparator:function(){return sameValueZeroEqual},strict:!0});var identity=function(e){return e},mapObject=function(e,t){return Object.keys(t).reduce(function(n,o){return _objectSpread(_objectSpread({},n),{},_defineProperty({},o,e(o,t[o])))},{})},getTransitionVal=function(e,t,n){return e.map(function(e){return"".concat(e.replace(/([A-Z])/g,function(e){return"-".concat(e.toLowerCase())})," ").concat(t,"ms ").concat(n)}).join(",")},warn=function(e,t,n,o,i,a,c,u){};function _slicedToArray(e,t){return easing_arrayWithHoles(e)||_iterableToArrayLimit(e,t)||easing_unsupportedIterableToArray(e,t)||easing_nonIterableRest()}function easing_nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,a,c,u=[],s=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(u.push(o.value),u.length!==t);s=!0);}catch(e){l=!0,i=e}finally{try{if(!s&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(l)throw i}}return u}}function easing_arrayWithHoles(e){if(Array.isArray(e))return e}function _toConsumableArray(e){return _arrayWithoutHoles(e)||easing_iterableToArray(e)||easing_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function easing_unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return easing_arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return easing_arrayLikeToArray(e,t)}}function easing_iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return easing_arrayLikeToArray(e)}function easing_arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}var cubicBezierFactor=function(e,t){return[0,3*e,3*t-6*e,3*e-3*t+1]},multyTime=function(e,t){return e.map(function(e,n){return e*Math.pow(t,n)}).reduce(function(e,t){return e+t})},cubicBezier=function(e,t){return function(n){return multyTime(cubicBezierFactor(e,t),n)}},configBezier=function(){for(var e,t,n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o[0],c=o[1],u=o[2],s=o[3];if(1===o.length)switch(o[0]){case"linear":a=0,c=0,u=1,s=1;break;case"ease":a=.25,c=.1,u=.25,s=1;break;case"ease-in":a=.42,c=0,u=1,s=1;break;case"ease-out":a=.42,c=0,u=.58,s=1;break;case"ease-in-out":a=0,c=0,u=.58,s=1;break;default:var l=o[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var f=_slicedToArray(l[1].split(")")[0].split(",").map(function(e){return parseFloat(e)}),4);a=f[0],c=f[1],u=f[2],s=f[3]}else warn(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",o)}warn([a,u,c,s].every(function(e){return"number"==typeof e&&e>=0&&e<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",o);var p=cubicBezier(a,u),d=cubicBezier(c,s),y=(e=a,t=u,function(n){return multyTime([].concat(_toConsumableArray(cubicBezierFactor(e,t).map(function(e,t){return e*t}).slice(1)),[0]),n)}),bezier=function(e){for(var t=e>1?1:e,n=t,o=0;o<8;++o){var i,a=p(n)-t,c=y(n);if(1e-4>Math.abs(a-t)||c<1e-4)break;n=(i=n-a/c)>1?1:i<0?0:i}return d(n)};return bezier.isStepper=!1,bezier},configSpring=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.stiff,n=void 0===t?100:t,o=e.damping,i=void 0===o?8:o,a=e.dt,c=void 0===a?17:a,stepper=function(e,t,o){var a=o+(-(e-t)*n-o*i)*c/1e3,u=o*c/1e3+e;return 1e-4>Math.abs(u-t)&&1e-4>Math.abs(a)?[t,0]:[u,a]};return stepper.isStepper=!0,stepper.dt=c,stepper},configEasing=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var o=t[0];if("string"==typeof o)switch(o){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return configBezier(o);case"spring":return configSpring();default:if("cubic-bezier"===o.split("(")[0])return configBezier(o);warn(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",t)}return"function"==typeof o?o:(warn(!1,"[configEasing]: first argument type should be function or string, instead received %s",t),null)};function configUpdate_typeof(e){return(configUpdate_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function configUpdate_toConsumableArray(e){return configUpdate_arrayWithoutHoles(e)||configUpdate_iterableToArray(e)||configUpdate_unsupportedIterableToArray(e)||configUpdate_nonIterableSpread()}function configUpdate_nonIterableSpread(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function configUpdate_iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function configUpdate_arrayWithoutHoles(e){if(Array.isArray(e))return configUpdate_arrayLikeToArray(e)}function configUpdate_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function configUpdate_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?configUpdate_ownKeys(Object(n),!0).forEach(function(t){configUpdate_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):configUpdate_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function configUpdate_defineProperty(e,t,n){return(t=configUpdate_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function configUpdate_toPropertyKey(e){var t=configUpdate_toPrimitive(e,"string");return"symbol"===configUpdate_typeof(t)?t:String(t)}function configUpdate_toPrimitive(e,t){if("object"!==configUpdate_typeof(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==configUpdate_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function configUpdate_slicedToArray(e,t){return configUpdate_arrayWithHoles(e)||configUpdate_iterableToArrayLimit(e,t)||configUpdate_unsupportedIterableToArray(e,t)||configUpdate_nonIterableRest()}function configUpdate_nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function configUpdate_unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return configUpdate_arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return configUpdate_arrayLikeToArray(e,t)}}function configUpdate_arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function configUpdate_iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,a,c,u=[],s=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(u.push(o.value),u.length!==t);s=!0);}catch(e){l=!0,i=e}finally{try{if(!s&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(l)throw i}}return u}}function configUpdate_arrayWithHoles(e){if(Array.isArray(e))return e}var alpha=function(e,t,n){return e+(t-e)*n},needContinue=function(e){return e.from!==e.to},calStepperVals=function calStepperVals(e,t,n){var o=mapObject(function(t,n){if(needContinue(n)){var o=configUpdate_slicedToArray(e(n.from,n.to,n.velocity),2),i=o[0],a=o[1];return configUpdate_objectSpread(configUpdate_objectSpread({},n),{},{from:i,velocity:a})}return n},t);return n<1?mapObject(function(e,t){return needContinue(t)?configUpdate_objectSpread(configUpdate_objectSpread({},t),{},{velocity:alpha(t.velocity,o[e].velocity,n),from:alpha(t.from,o[e].from,n)}):t},t):calStepperVals(e,o,n-1)},configUpdate=function(e,t,n,o,i){var a,c,u=[Object.keys(e),Object.keys(t)].reduce(function(e,t){return e.filter(function(e){return t.includes(e)})}),s=u.reduce(function(n,o){return configUpdate_objectSpread(configUpdate_objectSpread({},n),{},configUpdate_defineProperty({},o,[e[o],t[o]]))},{}),l=u.reduce(function(n,o){return configUpdate_objectSpread(configUpdate_objectSpread({},n),{},configUpdate_defineProperty({},o,{from:e[o],velocity:0,to:t[o]}))},{}),f=-1,update=function(){return null};return update=n.isStepper?function(o){a||(a=o);var c=(o-a)/n.dt;l=calStepperVals(n,l,c),i(configUpdate_objectSpread(configUpdate_objectSpread(configUpdate_objectSpread({},e),t),mapObject(function(e,t){return t.from},l))),a=o,Object.values(l).filter(needContinue).length&&(f=requestAnimationFrame(update))}:function(a){c||(c=a);var u=(a-c)/o,l=mapObject(function(e,t){return alpha.apply(void 0,configUpdate_toConsumableArray(t).concat([n(u)]))},s);if(i(configUpdate_objectSpread(configUpdate_objectSpread(configUpdate_objectSpread({},e),t),l)),u<1)f=requestAnimationFrame(update);else{var p=mapObject(function(e,t){return alpha.apply(void 0,configUpdate_toConsumableArray(t).concat([n(1)]))},s);i(configUpdate_objectSpread(configUpdate_objectSpread(configUpdate_objectSpread({},e),t),p))}},function(){return requestAnimationFrame(update),function(){cancelAnimationFrame(f)}}};function Animate_typeof(e){return(Animate_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var _=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n,o,i={},a=Object.keys(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||(i[n]=e[n]);return i}function Animate_toConsumableArray(e){return Animate_arrayWithoutHoles(e)||Animate_iterableToArray(e)||Animate_unsupportedIterableToArray(e)||Animate_nonIterableSpread()}function Animate_nonIterableSpread(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Animate_unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return Animate_arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Animate_arrayLikeToArray(e,t)}}function Animate_iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function Animate_arrayWithoutHoles(e){if(Array.isArray(e))return Animate_arrayLikeToArray(e)}function Animate_arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function Animate_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function Animate_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Animate_ownKeys(Object(n),!0).forEach(function(t){Animate_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Animate_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Animate_defineProperty(e,t,n){return(t=Animate_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Animate_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Animate_toPropertyKey(e){var t=Animate_toPrimitive(e,"string");return"symbol"===Animate_typeof(t)?t:String(t)}function Animate_toPrimitive(e,t){if("object"!==Animate_typeof(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==Animate_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _createSuper(e){var t=_isNativeReflectConstruct();return function(){var n,o=_getPrototypeOf(e);if(t){var i=_getPrototypeOf(this).constructor;n=Reflect.construct(o,arguments,i)}else n=o.apply(this,arguments);return _possibleConstructorReturn(this,n)}}function _possibleConstructorReturn(e,t){if(t&&("object"===Animate_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var x=function(e){_inherits(Animate,e);var t=_createSuper(Animate);function Animate(e,n){_classCallCheck(this,Animate);var o,i=(o=t.call(this,e,n)).props,a=i.isActive,c=i.attributeName,u=i.from,s=i.to,l=i.steps,f=i.children,p=i.duration;if(o.handleStyleChange=o.handleStyleChange.bind(_assertThisInitialized(o)),o.changeStyle=o.changeStyle.bind(_assertThisInitialized(o)),!a||p<=0)return o.state={style:{}},"function"==typeof f&&(o.state={style:s}),_possibleConstructorReturn(o);if(l&&l.length)o.state={style:l[0].style};else if(u){if("function"==typeof f)return o.state={style:u},_possibleConstructorReturn(o);o.state={style:c?Animate_defineProperty({},c,u):u}}else o.state={style:{}};return o}return _createClass(Animate,[{key:"componentDidMount",value:function(){var e=this.props,t=e.isActive,n=e.canBegin;this.mounted=!0,t&&n&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.isActive,o=t.canBegin,i=t.attributeName,a=t.shouldReAnimate,c=t.to,u=t.from,s=this.state.style;if(o){if(!n){var l={style:i?Animate_defineProperty({},i,c):c};this.state&&s&&(i&&s[i]!==c||!i&&s!==c)&&this.setState(l);return}if(!m(e.to,c)||!e.canBegin||!e.isActive){var f=!e.canBegin||!e.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var p=f||a?u:e.to;if(this.state&&s){var d={style:i?Animate_defineProperty({},i,p):p};(i&&s[i]!==p||!i&&s!==p)&&this.setState(d)}this.runAnimation(Animate_objectSpread(Animate_objectSpread({},this.props),{},{from:p,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var e=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}},{key:"handleStyleChange",value:function(e){this.changeStyle(e)}},{key:"changeStyle",value:function(e){this.mounted&&this.setState({style:e})}},{key:"runJSAnimation",value:function(e){var t=this,n=e.from,o=e.to,i=e.duration,a=e.easing,c=e.begin,u=e.onAnimationEnd,s=e.onAnimationStart,l=configUpdate(n,o,configEasing(a),i,this.changeStyle);this.manager.start([s,c,function(){t.stopJSAnimation=l()},i,u])}},{key:"runStepAnimation",value:function(e){var t=this,n=e.steps,o=e.begin,i=e.onAnimationStart,a=n[0],c=a.style,u=a.duration;return this.manager.start([i].concat(Animate_toConsumableArray(n.reduce(function(e,o,i){if(0===i)return e;var a=o.duration,c=o.easing,u=void 0===c?"ease":c,s=o.style,l=o.properties,f=o.onAnimationEnd,p=i>0?n[i-1]:o,d=l||Object.keys(s);if("function"==typeof u||"spring"===u)return[].concat(Animate_toConsumableArray(e),[t.runJSAnimation.bind(t,{from:p.style,to:s,duration:a,easing:u}),a]);var y=getTransitionVal(d,a,u),h=Animate_objectSpread(Animate_objectSpread(Animate_objectSpread({},p.style),s),{},{transition:y});return[].concat(Animate_toConsumableArray(e),[h,a,f]).filter(identity)},[c,Math.max(void 0===u?0:u,o)])),[e.onAnimationEnd]))}},{key:"runAnimation",value:function(e){this.manager||(this.manager=createAnimateManager());var t=e.begin,n=e.duration,o=e.attributeName,i=e.to,a=e.easing,c=e.onAnimationStart,u=e.onAnimationEnd,s=e.steps,l=e.children,f=this.manager;if(this.unSubscribe=f.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof l||"spring"===a){this.runJSAnimation(e);return}if(s.length>1){this.runStepAnimation(e);return}var p=o?Animate_defineProperty({},o,i):i,d=getTransitionVal(Object.keys(p),n,a);f.start([c,t,Animate_objectSpread(Animate_objectSpread({},p),{},{transition:d}),n,u])}},{key:"render",value:function(){var e=this.props,t=e.children,n=(e.begin,e.duration),i=(e.attributeName,e.easing,e.isActive),a=(e.steps,e.from,e.to,e.canBegin,e.onAnimationEnd,e.shouldReAnimate,e.onAnimationReStart,_objectWithoutProperties(e,_)),c=o.Children.count(t),u=this.state.style;if("function"==typeof t)return t(u);if(!i||0===c||n<=0)return t;var cloneContainer=function(e){var t=e.props,n=t.style,i=void 0===n?{}:n,c=t.className;return(0,o.cloneElement)(e,Animate_objectSpread(Animate_objectSpread({},a),{},{style:Animate_objectSpread(Animate_objectSpread({},i),u),className:c}))};return 1===c?cloneContainer(o.Children.only(t)):o.createElement("div",null,o.Children.map(t,function(e){return cloneContainer(e)}))}}]),Animate}(o.PureComponent);x.displayName="Animate",x.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},x.propTypes={from:a().oneOfType([a().object,a().string]),to:a().oneOfType([a().object,a().string]),attributeName:a().string,duration:a().number,begin:a().number,easing:a().oneOfType([a().string,a().func]),steps:a().arrayOf(a().shape({duration:a().number.isRequired,style:a().object.isRequired,easing:a().oneOfType([a().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),a().func]),properties:a().arrayOf("string"),onAnimationEnd:a().func})),children:a().oneOfType([a().node,a().func]),isActive:a().bool,canBegin:a().bool,onAnimationEnd:a().func,shouldReAnimate:a().bool,onAnimationStart:a().func,onAnimationReStart:a().func};var S=x},1346:function(e,t,n){"use strict";n.d(t,{$:function(){return j}});var o=n(2265),i=n(7042),a=n(5010),c=n(2077),u=n.n(c),s=n(2727),l=n.n(s),f=n(8357),p=n(2494),d=n(6612),y=n(561),h=n(7281),g=n(3843),b=n(9752),m=n(3249),_=n(2655),x=n(2130),S=n(7688),P=["x","y"];function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function typeguardBarRectangleProps(e,t){var n=e.x,o=e.y,i=_objectWithoutProperties(e,P),a=parseInt("".concat(n),10),c=parseInt("".concat(o),10),u=parseInt("".concat(t.height||i.height),10),s=parseInt("".concat(t.width||i.width),10);return _objectSpread(_objectSpread(_objectSpread(_objectSpread(_objectSpread({},t),i),a?{x:a}:{}),c?{y:c}:{}),{},{height:u,width:s,name:t.name,radius:t.radius})}function BarRectangle(e){return o.createElement(S.bn,_extends({shapeType:"rectangle",propTransformer:typeguardBarRectangleProps,activeClassName:"recharts-active-bar"},e))}var minPointSizeCallback=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(n,o){if("number"==typeof e)return e;var i=(0,h.hj)(n)||(0,h.Rw)(n);return i?e(n,o):(i||(0,x.Z)(!1),t)}},O=["value","background"];function Bar_typeof(e){return(Bar_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Bar_objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=Bar_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Bar_objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function Bar_extends(){return(Bar_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function Bar_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function Bar_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Bar_ownKeys(Object(n),!0).forEach(function(t){Bar_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Bar_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Bar_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _callSuper(e,t,n){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],_getPrototypeOf(e).constructor):t.apply(e,n))}function _possibleConstructorReturn(e,t){if(t&&("object"===Bar_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Bar_defineProperty(e,t,n){return(t=Bar_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Bar_toPropertyKey(e){var t=Bar_toPrimitive(e,"string");return"symbol"==Bar_typeof(t)?t:t+""}function Bar_toPrimitive(e,t){if("object"!=Bar_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Bar_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var j=function(e){function Bar(){var e;_classCallCheck(this,Bar);for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];return Bar_defineProperty(e=_callSuper(this,Bar,[].concat(n)),"state",{isAnimationFinished:!1}),Bar_defineProperty(e,"id",(0,h.EL)("recharts-bar-")),Bar_defineProperty(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),t&&t()}),Bar_defineProperty(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),t&&t()}),e}return _inherits(Bar,e),_createClass(Bar,[{key:"renderRectanglesStatically",value:function(e){var t=this,n=this.props,i=n.shape,a=n.dataKey,c=n.activeIndex,u=n.activeBar,s=(0,g.L6)(this.props,!1);return e&&e.map(function(e,n){var l=n===c,p=l?u:i,d=Bar_objectSpread(Bar_objectSpread(Bar_objectSpread({},s),e),{},{isActive:l,option:p,index:n,dataKey:a,onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd});return o.createElement(f.m,Bar_extends({className:"recharts-bar-rectangle"},(0,_.bw)(t.props,e,n),{key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(n)}),o.createElement(BarRectangle,d))})}},{key:"renderRectanglesWithAnimation",value:function(){var e=this,t=this.props,n=t.data,i=t.layout,c=t.isAnimationActive,u=t.animationBegin,s=t.animationDuration,l=t.animationEasing,p=t.animationId,d=this.state.prevData;return o.createElement(a.ZP,{begin:u,duration:s,isActive:c,easing:l,from:{t:0},to:{t:1},key:"bar-".concat(p),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(t){var a=t.t,c=n.map(function(e,t){var n=d&&d[t];if(n){var o=(0,h.k4)(n.x,e.x),c=(0,h.k4)(n.y,e.y),u=(0,h.k4)(n.width,e.width),s=(0,h.k4)(n.height,e.height);return Bar_objectSpread(Bar_objectSpread({},e),{},{x:o(a),y:c(a),width:u(a),height:s(a)})}if("horizontal"===i){var l=(0,h.k4)(0,e.height)(a);return Bar_objectSpread(Bar_objectSpread({},e),{},{y:e.y+e.height-l,height:l})}var f=(0,h.k4)(0,e.width)(a);return Bar_objectSpread(Bar_objectSpread({},e),{},{width:f})});return o.createElement(f.m,null,e.renderRectanglesStatically(c))})}},{key:"renderRectangles",value:function(){var e=this.props,t=e.data,n=e.isAnimationActive,o=this.state.prevData;return n&&t&&t.length&&(!o||!u()(o,t))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(t)}},{key:"renderBackground",value:function(){var e=this,t=this.props,n=t.data,i=t.dataKey,a=t.activeIndex,c=(0,g.L6)(this.props.background,!1);return n.map(function(t,n){t.value;var u=t.background,s=Bar_objectWithoutProperties(t,O);if(!u)return null;var l=Bar_objectSpread(Bar_objectSpread(Bar_objectSpread(Bar_objectSpread(Bar_objectSpread({},s),{},{fill:"#eee"},u),c),(0,_.bw)(e.props,t,n)),{},{onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd,dataKey:i,index:n,className:"recharts-bar-background-rectangle"});return o.createElement(BarRectangle,Bar_extends({key:"background-bar-".concat(n),option:e.props.background,isActive:n===a},l))})}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var n=this.props,i=n.data,a=n.xAxis,c=n.yAxis,u=n.layout,s=n.children,l=(0,g.NN)(s,p.W);if(!l)return null;var d="vertical"===u?i[0].height/2:i[0].width/2,dataPointFormatter=function(e,t){var n=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:n,errorVal:(0,m.F$)(e,t)}};return o.createElement(f.m,{clipPath:e?"url(#clipPath-".concat(t,")"):null},l.map(function(e){return o.cloneElement(e,{key:"error-bar-".concat(t,"-").concat(e.props.dataKey),data:i,xAxis:a,yAxis:c,layout:u,offset:d,dataPointFormatter:dataPointFormatter})}))}},{key:"render",value:function(){var e=this.props,t=e.hide,n=e.data,a=e.className,c=e.xAxis,u=e.yAxis,s=e.left,p=e.top,d=e.width,h=e.height,g=e.isAnimationActive,b=e.background,m=e.id;if(t||!n||!n.length)return null;var _=this.state.isAnimationFinished,x=(0,i.Z)("recharts-bar",a),S=c&&c.allowDataOverflow,P=u&&u.allowDataOverflow,O=S||P,j=l()(m)?this.id:m;return o.createElement(f.m,{className:x},S||P?o.createElement("defs",null,o.createElement("clipPath",{id:"clipPath-".concat(j)},o.createElement("rect",{x:S?s:s-d/2,y:P?p:p-h/2,width:S?d:2*d,height:P?h:2*h}))):null,o.createElement(f.m,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(j,")"):null},b?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,j),(!g||_)&&y.e.renderCallByParent(this.props,n))}}],[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curData:e.data,prevData:t.curData}:e.data!==t.curData?{curData:e.data}:null}}])}(o.PureComponent);Bar_defineProperty(j,"displayName","Bar"),Bar_defineProperty(j,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!b.x.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),Bar_defineProperty(j,"getComposedData",function(e){var t=e.props,n=e.item,o=e.barPosition,i=e.bandSize,a=e.xAxis,c=e.yAxis,u=e.xAxisTicks,s=e.yAxisTicks,l=e.stackedData,f=e.dataStartIndex,p=e.displayedData,y=e.offset,b=(0,m.Bu)(o,n);if(!b)return null;var _=t.layout,x=n.type.defaultProps,S=void 0!==x?Bar_objectSpread(Bar_objectSpread({},x),n.props):n.props,P=S.dataKey,O=S.children,w=S.minPointSize,C="horizontal"===_?c:a,A=l?C.scale.domain():null,T=(0,m.Yj)({numericAxis:C}),k=(0,g.NN)(O,d.b),E=p.map(function(e,t){l?p=(0,m.Vv)(l[f+t],A):Array.isArray(p=(0,m.F$)(e,P))||(p=[T,p]);var o=minPointSizeCallback(w,j.defaultProps.minPointSize)(p[1],t);if("horizontal"===_){var p,d,y,g,x,S,O,C=[c.scale(p[0]),c.scale(p[1])],E=C[0],M=C[1];d=(0,m.Fy)({axis:a,ticks:u,bandSize:i,offset:b.offset,entry:e,index:t}),y=null!==(O=null!=M?M:E)&&void 0!==O?O:void 0,g=b.size;var I=E-M;if(x=Number.isNaN(I)?0:I,S={x:d,y:c.y,width:g,height:c.height},Math.abs(o)>0&&Math.abs(x)<Math.abs(o)){var D=(0,h.uY)(x||o)*(Math.abs(o)-Math.abs(x));y-=D,x+=D}}else{var R=[a.scale(p[0]),a.scale(p[1])],L=R[0],N=R[1];if(d=L,y=(0,m.Fy)({axis:c,ticks:s,bandSize:i,offset:b.offset,entry:e,index:t}),g=N-L,x=b.size,S={x:a.x,y:y,width:a.width,height:x},Math.abs(o)>0&&Math.abs(g)<Math.abs(o)){var B=(0,h.uY)(g||o)*(Math.abs(o)-Math.abs(g));g+=B}}return Bar_objectSpread(Bar_objectSpread(Bar_objectSpread({},e),{},{x:d,y:y,width:g,height:x,value:l?p:p[1],payload:e,background:S},k&&k[t]&&k[t].props),{},{tooltipPayload:[(0,m.Qo)(n,e)],tooltipPosition:{x:d+g/2,y:y+x/2}})});return Bar_objectSpread({data:E,layout:_},y)})},4030:function(e,t,n){"use strict";n.d(t,{O:function(){return S}});var o=n(2265),i=n(8293),a=n.n(i),c=n(8614),u=n.n(c),s=n(7042),l=n(1374),f=n(8357),p=n(1224),d=n(3343),y=n(7281),h=n(2655),g=n(3843),b=n(7089),m=["viewBox"],_=["viewBox"],x=["ticks"];function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _callSuper(e,t,n){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],_getPrototypeOf(e).constructor):t.apply(e,n))}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var S=function(e){function CartesianAxis(e){var t;return _classCallCheck(this,CartesianAxis),(t=_callSuper(this,CartesianAxis,[e])).state={fontSize:"",letterSpacing:""},t}return _inherits(CartesianAxis,e),_createClass(CartesianAxis,[{key:"shouldComponentUpdate",value:function(e,t){var n=e.viewBox,o=_objectWithoutProperties(e,m),i=this.props,a=i.viewBox,c=_objectWithoutProperties(i,_);return!(0,l.w)(n,a)||!(0,l.w)(o,c)||!(0,l.w)(t,this.state)}},{key:"componentDidMount",value:function(){var e=this.layerReference;if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];t&&this.setState({fontSize:window.getComputedStyle(t).fontSize,letterSpacing:window.getComputedStyle(t).letterSpacing})}}},{key:"getTickLineCoord",value:function(e){var t,n,o,i,a,c,u=this.props,s=u.x,l=u.y,f=u.width,p=u.height,d=u.orientation,h=u.tickSize,g=u.mirror,b=u.tickMargin,m=g?-1:1,_=e.tickSize||h,x=(0,y.hj)(e.tickCoord)?e.tickCoord:e.coordinate;switch(d){case"top":t=n=e.coordinate,c=(o=(i=l+ +!g*p)-m*_)-m*b,a=x;break;case"left":o=i=e.coordinate,a=(t=(n=s+ +!g*f)-m*_)-m*b,c=x;break;case"right":o=i=e.coordinate,a=(t=(n=s+ +g*f)+m*_)+m*b,c=x;break;default:t=n=e.coordinate,c=(o=(i=l+ +g*p)+m*_)+m*b,a=x}return{line:{x1:t,y1:o,x2:n,y2:i},tick:{x:a,y:c}}}},{key:"getTickTextAnchor",value:function(){var e,t=this.props,n=t.orientation,o=t.mirror;switch(n){case"left":e=o?"start":"end";break;case"right":e=o?"end":"start";break;default:e="middle"}return e}},{key:"getTickVerticalAnchor",value:function(){var e=this.props,t=e.orientation,n=e.mirror,o="end";switch(t){case"left":case"right":o="middle";break;case"top":o=n?"start":"end";break;default:o=n?"end":"start"}return o}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.x,n=e.y,i=e.width,a=e.height,c=e.orientation,l=e.mirror,f=e.axisLine,p=_objectSpread(_objectSpread(_objectSpread({},(0,g.L6)(this.props,!1)),(0,g.L6)(f,!1)),{},{fill:"none"});if("top"===c||"bottom"===c){var d=+("top"===c&&!l||"bottom"===c&&l);p=_objectSpread(_objectSpread({},p),{},{x1:t,y1:n+d*a,x2:t+i,y2:n+d*a})}else{var y=+("left"===c&&!l||"right"===c&&l);p=_objectSpread(_objectSpread({},p),{},{x1:t+y*i,y1:n,x2:t+y*i,y2:n+a})}return o.createElement("line",_extends({},p,{className:(0,s.Z)("recharts-cartesian-axis-line",u()(f,"className"))}))}},{key:"renderTicks",value:function(e,t,n){var i=this,c=this.props,l=c.tickLine,p=c.stroke,d=c.tick,y=c.tickFormatter,m=c.unit,_=(0,b.f)(_objectSpread(_objectSpread({},this.props),{},{ticks:e}),t,n),x=this.getTickTextAnchor(),S=this.getTickVerticalAnchor(),P=(0,g.L6)(this.props,!1),O=(0,g.L6)(d,!1),j=_objectSpread(_objectSpread({},P),{},{fill:"none"},(0,g.L6)(l,!1)),w=_.map(function(e,t){var n=i.getTickLineCoord(e),c=n.line,g=n.tick,b=_objectSpread(_objectSpread(_objectSpread(_objectSpread({textAnchor:x,verticalAnchor:S},P),{},{stroke:"none",fill:p},O),g),{},{index:t,payload:e,visibleTicksCount:_.length,tickFormatter:y});return o.createElement(f.m,_extends({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,h.bw)(i.props,e,t)),l&&o.createElement("line",_extends({},j,c,{className:(0,s.Z)("recharts-cartesian-axis-tick-line",u()(l,"className"))})),d&&CartesianAxis.renderTickItem(d,b,"".concat(a()(y)?y(e.value,t):e.value).concat(m||"")))});return o.createElement("g",{className:"recharts-cartesian-axis-ticks"},w)}},{key:"render",value:function(){var e=this,t=this.props,n=t.axisLine,i=t.width,c=t.height,u=t.ticksGenerator,l=t.className;if(t.hide)return null;var p=this.props,y=p.ticks,h=_objectWithoutProperties(p,x),g=y;return(a()(u)&&(g=u(y&&y.length>0?this.props:h)),i<=0||c<=0||!g||!g.length)?null:o.createElement(f.m,{className:(0,s.Z)("recharts-cartesian-axis",l),ref:function(t){e.layerReference=t}},n&&this.renderAxisLine(),this.renderTicks(g,this.state.fontSize,this.state.letterSpacing),d._.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(e,t,n){var i=(0,s.Z)(t.className,"recharts-cartesian-axis-tick-value");return o.isValidElement(e)?o.cloneElement(e,_objectSpread(_objectSpread({},t),{},{className:i})):a()(e)?e(_objectSpread(_objectSpread({},t),{},{className:i})):o.createElement(p.x,_extends({},t,{className:"recharts-cartesian-axis-tick-value"}),n)}}])}(o.Component);_defineProperty(S,"displayName","CartesianAxis"),_defineProperty(S,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},6573:function(e,t,n){"use strict";n.d(t,{q:function(){return CartesianGrid}});var o=n(2265),i=n(8293),a=n.n(i),c=n(7105),u=n(7281),s=n(3843),l=n(3249),f=n(7089),p=n(4030),d=n(2794),y=["x1","y1","x2","y2","key"],h=["offset"];function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}var Background=function(e){var t=e.fill;if(!t||"none"===t)return null;var n=e.fillOpacity,i=e.x,a=e.y,c=e.width,u=e.height,s=e.ry;return o.createElement("rect",{x:i,y:a,ry:s,width:c,height:u,stroke:"none",fill:t,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function renderLineItem(e,t){var n;if(o.isValidElement(e))n=o.cloneElement(e,t);else if(a()(e))n=e(t);else{var i=t.x1,c=t.y1,u=t.x2,l=t.y2,f=t.key,p=_objectWithoutProperties(t,y),d=(0,s.L6)(p,!1),g=(d.offset,_objectWithoutProperties(d,h));n=o.createElement("line",_extends({},g,{x1:i,y1:c,x2:u,y2:l,fill:"none",key:f}))}return n}function HorizontalGridLines(e){var t=e.x,n=e.width,i=e.horizontal,a=void 0===i||i,c=e.horizontalPoints;if(!a||!c||!c.length)return null;var u=c.map(function(o,i){return renderLineItem(a,_objectSpread(_objectSpread({},e),{},{x1:t,y1:o,x2:t+n,y2:o,key:"line-".concat(i),index:i}))});return o.createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function VerticalGridLines(e){var t=e.y,n=e.height,i=e.vertical,a=void 0===i||i,c=e.verticalPoints;if(!a||!c||!c.length)return null;var u=c.map(function(o,i){return renderLineItem(a,_objectSpread(_objectSpread({},e),{},{x1:o,y1:t,x2:o,y2:t+n,key:"line-".concat(i),index:i}))});return o.createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function HorizontalStripes(e){var t=e.horizontalFill,n=e.fillOpacity,i=e.x,a=e.y,c=e.width,u=e.height,s=e.horizontalPoints,l=e.horizontal;if(!(void 0===l||l)||!t||!t.length)return null;var f=s.map(function(e){return Math.round(e+a-a)}).sort(function(e,t){return e-t});a!==f[0]&&f.unshift(0);var p=f.map(function(e,s){var l=f[s+1]?f[s+1]-e:a+u-e;if(l<=0)return null;var p=s%t.length;return o.createElement("rect",{key:"react-".concat(s),y:e,x:i,height:l,width:c,stroke:"none",fill:t[p],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return o.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},p)}function VerticalStripes(e){var t=e.vertical,n=e.verticalFill,i=e.fillOpacity,a=e.x,c=e.y,u=e.width,s=e.height,l=e.verticalPoints;if(!(void 0===t||t)||!n||!n.length)return null;var f=l.map(function(e){return Math.round(e+a-a)}).sort(function(e,t){return e-t});a!==f[0]&&f.unshift(0);var p=f.map(function(e,t){var l=f[t+1]?f[t+1]-e:a+u-e;if(l<=0)return null;var p=t%n.length;return o.createElement("rect",{key:"react-".concat(t),x:e,y:c,width:l,height:s,stroke:"none",fill:n[p],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return o.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},p)}var defaultVerticalCoordinatesGenerator=function(e,t){var n=e.xAxis,o=e.width,i=e.height,a=e.offset;return(0,l.Rf)((0,f.f)(_objectSpread(_objectSpread(_objectSpread({},p.O.defaultProps),n),{},{ticks:(0,l.uY)(n,!0),viewBox:{x:0,y:0,width:o,height:i}})),a.left,a.left+a.width,t)},defaultHorizontalCoordinatesGenerator=function(e,t){var n=e.yAxis,o=e.width,i=e.height,a=e.offset;return(0,l.Rf)((0,f.f)(_objectSpread(_objectSpread(_objectSpread({},p.O.defaultProps),n),{},{ticks:(0,l.uY)(n,!0),viewBox:{x:0,y:0,width:o,height:i}})),a.top,a.top+a.height,t)},g={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function CartesianGrid(e){var t,n,i,s,l,f,p=(0,d.zn)(),y=(0,d.Mw)(),h=(0,d.qD)(),b=_objectSpread(_objectSpread({},e),{},{stroke:null!==(t=e.stroke)&&void 0!==t?t:g.stroke,fill:null!==(n=e.fill)&&void 0!==n?n:g.fill,horizontal:null!==(i=e.horizontal)&&void 0!==i?i:g.horizontal,horizontalFill:null!==(s=e.horizontalFill)&&void 0!==s?s:g.horizontalFill,vertical:null!==(l=e.vertical)&&void 0!==l?l:g.vertical,verticalFill:null!==(f=e.verticalFill)&&void 0!==f?f:g.verticalFill,x:(0,u.hj)(e.x)?e.x:h.left,y:(0,u.hj)(e.y)?e.y:h.top,width:(0,u.hj)(e.width)?e.width:h.width,height:(0,u.hj)(e.height)?e.height:h.height}),m=b.x,_=b.y,x=b.width,S=b.height,P=b.syncWithTicks,O=b.horizontalValues,j=b.verticalValues,w=(0,d.CW)(),C=(0,d.Nf)();if(!(0,u.hj)(x)||x<=0||!(0,u.hj)(S)||S<=0||!(0,u.hj)(m)||m!==+m||!(0,u.hj)(_)||_!==+_)return null;var A=b.verticalCoordinatesGenerator||defaultVerticalCoordinatesGenerator,T=b.horizontalCoordinatesGenerator||defaultHorizontalCoordinatesGenerator,k=b.horizontalPoints,E=b.verticalPoints;if((!k||!k.length)&&a()(T)){var M=O&&O.length,I=T({yAxis:C?_objectSpread(_objectSpread({},C),{},{ticks:M?O:C.ticks}):void 0,width:p,height:y,offset:h},!!M||P);(0,c.Z)(Array.isArray(I),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(_typeof(I),"]")),Array.isArray(I)&&(k=I)}if((!E||!E.length)&&a()(A)){var D=j&&j.length,R=A({xAxis:w?_objectSpread(_objectSpread({},w),{},{ticks:D?j:w.ticks}):void 0,width:p,height:y,offset:h},!!D||P);(0,c.Z)(Array.isArray(R),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(_typeof(R),"]")),Array.isArray(R)&&(E=R)}return o.createElement("g",{className:"recharts-cartesian-grid"},o.createElement(Background,{fill:b.fill,fillOpacity:b.fillOpacity,x:b.x,y:b.y,width:b.width,height:b.height,ry:b.ry}),o.createElement(HorizontalGridLines,_extends({},b,{offset:h,horizontalPoints:k,xAxis:w,yAxis:C})),o.createElement(VerticalGridLines,_extends({},b,{offset:h,verticalPoints:E,xAxis:w,yAxis:C})),o.createElement(HorizontalStripes,_extends({},b,{horizontalPoints:k})),o.createElement(VerticalStripes,_extends({},b,{verticalPoints:E})))}CartesianGrid.displayName="CartesianGrid"},2494:function(e,t,n){"use strict";n.d(t,{W:function(){return s}});var o=n(2265),i=n(2130),a=n(8357),c=n(3843),u=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,a,c,u=[],s=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(u.push(o.value),u.length!==t);s=!0);}catch(e){l=!0,i=e}finally{try{if(!s&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(l)throw i}}return u}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _callSuper(e,t,n){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],_getPrototypeOf(e).constructor):t.apply(e,n))}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var s=function(e){function ErrorBar(){return _classCallCheck(this,ErrorBar),_callSuper(this,ErrorBar,arguments)}return _inherits(ErrorBar,e),_createClass(ErrorBar,[{key:"render",value:function(){var e=this.props,t=e.offset,n=e.layout,s=e.width,l=e.dataKey,f=e.data,p=e.dataPointFormatter,d=e.xAxis,y=e.yAxis,h=_objectWithoutProperties(e,u),g=(0,c.L6)(h,!1);"x"===this.props.direction&&"number"!==d.type&&(0,i.Z)(!1);var b=f.map(function(e){var i,c,u=p(e,l),f=u.x,h=u.y,b=u.value,m=u.errorVal;if(!m)return null;var _=[];if(Array.isArray(m)){var x=_slicedToArray(m,2);i=x[0],c=x[1]}else i=c=m;if("vertical"===n){var S=d.scale,P=h+t,O=P+s,j=P-s,w=S(b-i),C=S(b+c);_.push({x1:C,y1:O,x2:C,y2:j}),_.push({x1:w,y1:P,x2:C,y2:P}),_.push({x1:w,y1:O,x2:w,y2:j})}else if("horizontal"===n){var A=y.scale,T=f+t,k=T-s,E=T+s,M=A(b-i),I=A(b+c);_.push({x1:k,y1:I,x2:E,y2:I}),_.push({x1:T,y1:M,x2:T,y2:I}),_.push({x1:k,y1:M,x2:E,y2:M})}return o.createElement(a.m,_extends({className:"recharts-errorBar",key:"bar-".concat(_.map(function(e){return"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))},g),_.map(function(e){return o.createElement("line",_extends({},e,{key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))}))});return o.createElement(a.m,{className:"recharts-errorBars"},b)}}])}(o.Component);_defineProperty(s,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),_defineProperty(s,"displayName","ErrorBar")},9223:function(e,t,n){"use strict";n.d(t,{x:function(){return j}});var o=n(2265),i=n(5010),a=n(8293),c=n.n(a),u=n(2727),s=n.n(u),l=n(2077),f=n.n(l),p=n(7042),d=n(9431),y=n(4304),h=n(8357),g=n(561),b=n(2494),m=n(7281),_=n(3843),x=n(9752),S=n(3249),P=["type","layout","connectNulls","ref"],O=["key"];function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _callSuper(e,t,n){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],_getPrototypeOf(e).constructor):t.apply(e,n))}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var j=function(e){function Line(){var e;_classCallCheck(this,Line);for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];return _defineProperty(e=_callSuper(this,Line,[].concat(n)),"state",{isAnimationFinished:!0,totalLength:0}),_defineProperty(e,"generateSimpleStrokeDasharray",function(e,t){return"".concat(t,"px ").concat(e-t,"px")}),_defineProperty(e,"getStrokeDasharray",function(t,n,o){var i=o.reduce(function(e,t){return e+t});if(!i)return e.generateSimpleStrokeDasharray(n,t);for(var a=Math.floor(t/i),c=t%i,u=n-t,s=[],l=0,f=0;l<o.length;f+=o[l],++l)if(f+o[l]>c){s=[].concat(_toConsumableArray(o.slice(0,l)),[c-f]);break}var p=s.length%2==0?[0,u]:[u];return[].concat(_toConsumableArray(Line.repeat(o,a)),_toConsumableArray(s),p).map(function(e){return"".concat(e,"px")}).join(", ")}),_defineProperty(e,"id",(0,m.EL)("recharts-line-")),_defineProperty(e,"pathRef",function(t){e.mainCurve=t}),_defineProperty(e,"handleAnimationEnd",function(){e.setState({isAnimationFinished:!0}),e.props.onAnimationEnd&&e.props.onAnimationEnd()}),_defineProperty(e,"handleAnimationStart",function(){e.setState({isAnimationFinished:!1}),e.props.onAnimationStart&&e.props.onAnimationStart()}),e}return _inherits(Line,e),_createClass(Line,[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();this.setState({totalLength:e})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();e!==this.state.totalLength&&this.setState({totalLength:e})}}},{key:"getTotalLength",value:function(){var e=this.mainCurve;try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var n=this.props,i=n.points,a=n.xAxis,c=n.yAxis,u=n.layout,s=n.children,l=(0,_.NN)(s,b.W);if(!l)return null;var dataPointFormatter=function(e,t){return{x:e.x,y:e.y,value:e.value,errorVal:(0,S.F$)(e.payload,t)}};return o.createElement(h.m,{clipPath:e?"url(#clipPath-".concat(t,")"):null},l.map(function(e){return o.cloneElement(e,{key:"bar-".concat(e.props.dataKey),data:i,xAxis:a,yAxis:c,layout:u,dataPointFormatter:dataPointFormatter})}))}},{key:"renderDots",value:function(e,t,n){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,a=i.dot,c=i.points,u=i.dataKey,s=(0,_.L6)(this.props,!1),l=(0,_.L6)(a,!0),f=c.map(function(e,t){var n=_objectSpread(_objectSpread(_objectSpread({key:"dot-".concat(t),r:3},s),l),{},{index:t,cx:e.x,cy:e.y,value:e.value,dataKey:u,payload:e.payload,points:c});return Line.renderDotItem(a,n)}),p={clipPath:e?"url(#clipPath-".concat(t?"":"dots-").concat(n,")"):null};return o.createElement(h.m,_extends({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(e,t,n,i){var a=this.props,c=a.type,u=a.layout,s=a.connectNulls,l=(a.ref,_objectWithoutProperties(a,P)),f=_objectSpread(_objectSpread(_objectSpread({},(0,_.L6)(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:t?"url(#clipPath-".concat(n,")"):null,points:e},i),{},{type:c,layout:u,connectNulls:s});return o.createElement(d.H,_extends({},f,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(e,t){var n=this,a=this.props,c=a.points,u=a.strokeDasharray,s=a.isAnimationActive,l=a.animationBegin,f=a.animationDuration,p=a.animationEasing,d=a.animationId,y=a.animateNewValues,h=a.width,g=a.height,b=this.state,_=b.prevPoints,x=b.totalLength;return o.createElement(i.ZP,{begin:l,duration:f,isActive:s,easing:p,from:{t:0},to:{t:1},key:"line-".concat(d),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(o){var i,a=o.t;if(_){var s=_.length/c.length,l=c.map(function(e,t){var n=Math.floor(t*s);if(_[n]){var o=_[n],i=(0,m.k4)(o.x,e.x),c=(0,m.k4)(o.y,e.y);return _objectSpread(_objectSpread({},e),{},{x:i(a),y:c(a)})}if(y){var u=(0,m.k4)(2*h,e.x),l=(0,m.k4)(g/2,e.y);return _objectSpread(_objectSpread({},e),{},{x:u(a),y:l(a)})}return _objectSpread(_objectSpread({},e),{},{x:e.x,y:e.y})});return n.renderCurveStatically(l,e,t)}var f=(0,m.k4)(0,x)(a);if(u){var p="".concat(u).split(/[,\s]+/gim).map(function(e){return parseFloat(e)});i=n.getStrokeDasharray(f,x,p)}else i=n.generateSimpleStrokeDasharray(x,f);return n.renderCurveStatically(c,e,t,{strokeDasharray:i})})}},{key:"renderCurve",value:function(e,t){var n=this.props,o=n.points,i=n.isAnimationActive,a=this.state,c=a.prevPoints,u=a.totalLength;return i&&o&&o.length&&(!c&&u>0||!f()(c,o))?this.renderCurveWithAnimation(e,t):this.renderCurveStatically(o,e,t)}},{key:"render",value:function(){var e,t=this.props,n=t.hide,i=t.dot,a=t.points,c=t.className,u=t.xAxis,l=t.yAxis,f=t.top,d=t.left,y=t.width,b=t.height,m=t.isAnimationActive,x=t.id;if(n||!a||!a.length)return null;var S=this.state.isAnimationFinished,P=1===a.length,O=(0,p.Z)("recharts-line",c),j=u&&u.allowDataOverflow,w=l&&l.allowDataOverflow,C=j||w,A=s()(x)?this.id:x,T=null!==(e=(0,_.L6)(i,!1))&&void 0!==e?e:{r:3,strokeWidth:2},k=T.r,E=T.strokeWidth,M=((0,_.jf)(i)?i:{}).clipDot,I=void 0===M||M,D=2*(void 0===k?3:k)+(void 0===E?2:E);return o.createElement(h.m,{className:O},j||w?o.createElement("defs",null,o.createElement("clipPath",{id:"clipPath-".concat(A)},o.createElement("rect",{x:j?d:d-y/2,y:w?f:f-b/2,width:j?y:2*y,height:w?b:2*b})),!I&&o.createElement("clipPath",{id:"clipPath-dots-".concat(A)},o.createElement("rect",{x:d-D/2,y:f-D/2,width:y+D,height:b+D}))):null,!P&&this.renderCurve(C,A),this.renderErrorBar(C,A),(P||i)&&this.renderDots(C,I,A),(!m||S)&&g.e.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,prevPoints:t.curPoints}:e.points!==t.curPoints?{curPoints:e.points}:null}},{key:"repeat",value:function(e,t){for(var n=e.length%2!=0?[].concat(_toConsumableArray(e),[0]):e,o=[],i=0;i<t;++i)o=[].concat(_toConsumableArray(o),_toConsumableArray(n));return o}},{key:"renderDotItem",value:function(e,t){var n;if(o.isValidElement(e))n=o.cloneElement(e,t);else if(c()(e))n=e(t);else{var i=t.key,a=_objectWithoutProperties(t,O),u=(0,p.Z)("recharts-line-dot","boolean"!=typeof e?e.className:"");n=o.createElement(y.o,_extends({key:i},a,{className:u}))}return n}}])}(o.PureComponent);_defineProperty(j,"displayName","Line"),_defineProperty(j,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!x.x.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),_defineProperty(j,"getComposedData",function(e){var t=e.props,n=e.xAxis,o=e.yAxis,i=e.xAxisTicks,a=e.yAxisTicks,c=e.dataKey,u=e.bandSize,l=e.displayedData,f=e.offset,p=t.layout;return _objectSpread({points:l.map(function(e,t){var l=(0,S.F$)(e,c);return"horizontal"===p?{x:(0,S.Hv)({axis:n,ticks:i,bandSize:u,entry:e,index:t}),y:s()(l)?null:o.scale(l),value:l,payload:e}:{x:s()(l)?null:n.scale(l),y:(0,S.Hv)({axis:o,ticks:a,bandSize:u,entry:e,index:t}),value:l,payload:e}}),layout:p},f)})},4235:function(e,t,n){"use strict";n.d(t,{K:function(){return s}});var o=n(2265),i=n(7042),a=n(2794),c=n(4030),u=n(3249);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _callSuper(e,t,n){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],_getPrototypeOf(e).constructor):t.apply(e,n))}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function XAxisImpl(e){var t=e.xAxisId,n=(0,a.zn)(),s=(0,a.Mw)(),l=(0,a.bH)(t);return null==l?null:o.createElement(c.O,_extends({},l,{className:(0,i.Z)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:n,height:s},ticksGenerator:function(e){return(0,u.uY)(e,!0)}}))}var s=function(e){function XAxis(){return _classCallCheck(this,XAxis),_callSuper(this,XAxis,arguments)}return _inherits(XAxis,e),_createClass(XAxis,[{key:"render",value:function(){return o.createElement(XAxisImpl,this.props)}}])}(o.Component);_defineProperty(s,"displayName","XAxis"),_defineProperty(s,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0})},39:function(e,t,n){"use strict";n.d(t,{B:function(){return s}});var o=n(2265),i=n(7042),a=n(2794),c=n(4030),u=n(3249);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _callSuper(e,t,n){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],_getPrototypeOf(e).constructor):t.apply(e,n))}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}var YAxisImpl=function(e){var t=e.yAxisId,n=(0,a.zn)(),s=(0,a.Mw)(),l=(0,a.Ud)(t);return null==l?null:o.createElement(c.O,_extends({},l,{className:(0,i.Z)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:n,height:s},ticksGenerator:function(e){return(0,u.uY)(e,!0)}}))},s=function(e){function YAxis(){return _classCallCheck(this,YAxis),_callSuper(this,YAxis,arguments)}return _inherits(YAxis,e),_createClass(YAxis,[{key:"render",value:function(){return o.createElement(YAxisImpl,this.props)}}])}(o.Component);_defineProperty(s,"displayName","YAxis"),_defineProperty(s,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1})},7089:function(e,t,n){"use strict";n.d(t,{f:function(){return getTicks}});var o=n(8293),i=n.n(o),a=n(7281),c=n(4768),u=n(9752),s=n(9677);function getEveryNthWithCondition(e,t,n){if(t<1)return[];if(1===t&&void 0===n)return e;for(var o=[],i=0;i<e.length;i+=t){if(void 0!==n&&!0!==n(e[i]))return;o.push(e[i])}return o}function getAngledTickWidth(e,t,n){var o={width:e.width+t.width,height:e.height+t.height};return(0,s.xE)(o,n)}function getTickBoundaries(e,t,n){var o="width"===n,i=e.x,a=e.y,c=e.width,u=e.height;return 1===t?{start:o?i:a,end:o?i+c:a+u}:{start:o?i+c:a+u,end:o?i:a}}function isVisible(e,t,n,o,i){if(e*t<e*o||e*t>e*i)return!1;var a=n();return e*(t-e*a/2-o)>=0&&e*(t+e*a/2-i)<=0}function getNumberIntervalTicks(e,t){return getEveryNthWithCondition(e,t+1)}function getEquidistantTicks(e,t,n,o,i){for(var a,c=(o||[]).slice(),u=t.start,s=t.end,l=0,f=1,p=u;f<=c.length;)if(a=function(){var t,a=null==o?void 0:o[l];if(void 0===a)return{v:getEveryNthWithCondition(o,f)};var c=l,getSize=function(){return void 0===t&&(t=n(a,c)),t},d=a.coordinate,y=0===l||isVisible(e,d,getSize,p,s);y||(l=0,p=u,f+=1),y&&(p=d+e*(getSize()/2+i),l+=f)}())return a.v;return[]}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function getTicksEnd(e,t,n,o,i){for(var a=(o||[]).slice(),c=a.length,u=t.start,s=t.end,_loop=function(t){var o,l=a[t],getSize=function(){return void 0===o&&(o=n(l,t)),o};if(t===c-1){var f=e*(l.coordinate+e*getSize()/2-s);a[t]=l=_objectSpread(_objectSpread({},l),{},{tickCoord:f>0?l.coordinate-f*e:l.coordinate})}else a[t]=l=_objectSpread(_objectSpread({},l),{},{tickCoord:l.coordinate});isVisible(e,l.tickCoord,getSize,u,s)&&(s=l.tickCoord-e*(getSize()/2+i),a[t]=_objectSpread(_objectSpread({},l),{},{isShow:!0}))},l=c-1;l>=0;l--)_loop(l);return a}function getTicksStart(e,t,n,o,i,a){var c=(o||[]).slice(),u=c.length,s=t.start,l=t.end;if(a){var f=o[u-1],p=n(f,u-1),d=e*(f.coordinate+e*p/2-l);c[u-1]=f=_objectSpread(_objectSpread({},f),{},{tickCoord:d>0?f.coordinate-d*e:f.coordinate}),isVisible(e,f.tickCoord,function(){return p},s,l)&&(l=f.tickCoord-e*(p/2+i),c[u-1]=_objectSpread(_objectSpread({},f),{},{isShow:!0}))}for(var y=a?u-1:u,_loop2=function(t){var o,a=c[t],getSize=function(){return void 0===o&&(o=n(a,t)),o};if(0===t){var u=e*(a.coordinate-e*getSize()/2-s);c[t]=a=_objectSpread(_objectSpread({},a),{},{tickCoord:u<0?a.coordinate-u*e:a.coordinate})}else c[t]=a=_objectSpread(_objectSpread({},a),{},{tickCoord:a.coordinate});isVisible(e,a.tickCoord,getSize,s,l)&&(s=a.tickCoord+e*(getSize()/2+i),c[t]=_objectSpread(_objectSpread({},a),{},{isShow:!0}))},h=0;h<y;h++)_loop2(h);return c}function getTicks(e,t,n){var o=e.tick,s=e.ticks,l=e.viewBox,f=e.minTickGap,p=e.orientation,d=e.interval,y=e.tickFormatter,h=e.unit,g=e.angle;if(!s||!s.length||!o)return[];if((0,a.hj)(d)||u.x.isSsr)return getNumberIntervalTicks(s,"number"==typeof d&&(0,a.hj)(d)?d:0);var b="top"===p||"bottom"===p?"width":"height",m=h&&"width"===b?(0,c.xE)(h,{fontSize:t,letterSpacing:n}):{width:0,height:0},getTickSize=function(e,o){var a=i()(y)?y(e.value,o):e.value;return"width"===b?getAngledTickWidth((0,c.xE)(a,{fontSize:t,letterSpacing:n}),m,g):(0,c.xE)(a,{fontSize:t,letterSpacing:n})[b]},_=s.length>=2?(0,a.uY)(s[1].coordinate-s[0].coordinate):1,x=getTickBoundaries(l,_,b);return"equidistantPreserveStart"===d?getEquidistantTicks(_,x,getTickSize,s,f):("preserveStart"===d||"preserveStartEnd"===d?getTicksStart(_,x,getTickSize,s,f,"preserveStartEnd"===d):getTicksEnd(_,x,getTickSize,s,f)).filter(function(e){return e.isShow})}},4909:function(e,t,n){"use strict";n.d(t,{v:function(){return s}});var o=n(9205),i=n(1346),a=n(4235),c=n(39),u=n(9677),s=(0,o.z)({chartName:"BarChart",GraphicalChild:i.$,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:a.K},{axisType:"yAxis",AxisComp:c.B}],formatAxisMap:u.t9})},8227:function(e,t,n){"use strict";n.d(t,{w:function(){return s}});var o=n(9205),i=n(9223),a=n(4235),c=n(39),u=n(9677),s=(0,o.z)({chartName:"LineChart",GraphicalChild:i.x,axisComponents:[{axisType:"xAxis",AxisComp:a.K},{axisType:"yAxis",AxisComp:c.B}],formatAxisMap:u.t9})},7703:function(e,t,n){"use strict";n.d(t,{u:function(){return A}});var o=n(9205),i=n(2265),a=n(8293),c=n.n(a),u=n(7042),s=n(8357),l=n(4304),f=n(3843),p=["points","className","baseLinePoints","connectNulls"];function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}var isValidatePoint=function(e){return e&&e.x===+e.x&&e.y===+e.y},getParsedPoints=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach(function(e){isValidatePoint(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])}),isValidatePoint(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t},getSinglePolygonPath=function(e,t){var n=getParsedPoints(e);t&&(n=[n.reduce(function(e,t){return[].concat(_toConsumableArray(e),_toConsumableArray(t))},[])]);var o=n.map(function(e){return e.reduce(function(e,t,n){return"".concat(e).concat(0===n?"M":"L").concat(t.x,",").concat(t.y)},"")}).join("");return 1===n.length?"".concat(o,"Z"):o},getRanglePath=function(e,t,n){var o=getSinglePolygonPath(e,n);return"".concat("Z"===o.slice(-1)?o.slice(0,-1):o,"L").concat(getSinglePolygonPath(t.reverse(),n).slice(1))},Polygon=function(e){var t=e.points,n=e.className,o=e.baseLinePoints,a=e.connectNulls,c=_objectWithoutProperties(e,p);if(!t||!t.length)return null;var s=(0,u.Z)("recharts-polygon",n);if(o&&o.length){var l=c.stroke&&"none"!==c.stroke,d=getRanglePath(t,o,a);return i.createElement("g",{className:s},i.createElement("path",_extends({},(0,f.L6)(c,!0),{fill:"Z"===d.slice(-1)?c.fill:"none",stroke:"none",d:d})),l?i.createElement("path",_extends({},(0,f.L6)(c,!0),{fill:"none",d:getSinglePolygonPath(t,a)})):null,l?i.createElement("path",_extends({},(0,f.L6)(c,!0),{fill:"none",d:getSinglePolygonPath(o,a)})):null)}var y=getSinglePolygonPath(t,a);return i.createElement("path",_extends({},(0,f.L6)(c,!0),{fill:"Z"===y.slice(-1)?c.fill:"none",className:s,d:y}))},d=n(1224),y=n(2655),h=n(6120);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function PolarAngleAxis_extends(){return(PolarAngleAxis_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _callSuper(e,t,n){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],_getPrototypeOf(e).constructor):t.apply(e,n))}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var g=Math.PI/180,b=function(e){function PolarAngleAxis(){return _classCallCheck(this,PolarAngleAxis),_callSuper(this,PolarAngleAxis,arguments)}return _inherits(PolarAngleAxis,e),_createClass(PolarAngleAxis,[{key:"getTickLineCoord",value:function(e){var t=this.props,n=t.cx,o=t.cy,i=t.radius,a=t.orientation,c=t.tickSize||8,u=(0,h.op)(n,o,i,e.coordinate),s=(0,h.op)(n,o,i+("inner"===a?-1:1)*c,e.coordinate);return{x1:u.x,y1:u.y,x2:s.x,y2:s.y}}},{key:"getTickTextAnchor",value:function(e){var t=this.props.orientation,n=Math.cos(-e.coordinate*g);return n>1e-5?"outer"===t?"start":"end":n<-.00001?"outer"===t?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,n=e.cy,o=e.radius,a=e.axisLine,c=e.axisLineType,u=_objectSpread(_objectSpread({},(0,f.L6)(this.props,!1)),{},{fill:"none"},(0,f.L6)(a,!1));if("circle"===c)return i.createElement(l.o,PolarAngleAxis_extends({className:"recharts-polar-angle-axis-line"},u,{cx:t,cy:n,r:o}));var s=this.props.ticks.map(function(e){return(0,h.op)(t,n,o,e.coordinate)});return i.createElement(Polygon,PolarAngleAxis_extends({className:"recharts-polar-angle-axis-line"},u,{points:s}))}},{key:"renderTicks",value:function(){var e=this,t=this.props,n=t.ticks,o=t.tick,a=t.tickLine,c=t.tickFormatter,l=t.stroke,p=(0,f.L6)(this.props,!1),d=(0,f.L6)(o,!1),g=_objectSpread(_objectSpread({},p),{},{fill:"none"},(0,f.L6)(a,!1)),b=n.map(function(t,n){var f=e.getTickLineCoord(t),b=_objectSpread(_objectSpread(_objectSpread({textAnchor:e.getTickTextAnchor(t)},p),{},{stroke:"none",fill:l},d),{},{index:n,payload:t,x:f.x2,y:f.y2});return i.createElement(s.m,PolarAngleAxis_extends({className:(0,u.Z)("recharts-polar-angle-axis-tick",(0,h.$S)(o)),key:"tick-".concat(t.coordinate)},(0,y.bw)(e.props,t,n)),a&&i.createElement("line",PolarAngleAxis_extends({className:"recharts-polar-angle-axis-tick-line"},g,f)),o&&PolarAngleAxis.renderTickItem(o,b,c?c(t.value,n):t.value))});return i.createElement(s.m,{className:"recharts-polar-angle-axis-ticks"},b)}},{key:"render",value:function(){var e=this.props,t=e.ticks,n=e.radius,o=e.axisLine;return!(n<=0)&&t&&t.length?i.createElement(s.m,{className:(0,u.Z)("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks()):null}}],[{key:"renderTickItem",value:function(e,t,n){return i.isValidElement(e)?i.cloneElement(e,t):c()(e)?e(t):i.createElement(d.x,PolarAngleAxis_extends({},t,{className:"recharts-polar-angle-axis-tick-value"}),n)}}])}(i.PureComponent);_defineProperty(b,"displayName","PolarAngleAxis"),_defineProperty(b,"axisType","angleAxis"),_defineProperty(b,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var m=n(4573),_=n.n(m),x=n(175),S=n.n(x),P=n(3343),O=["cx","cy","angle","ticks","axisLine"],j=["ticks","tick","angle","tickFormatter","stroke"];function PolarRadiusAxis_typeof(e){return(PolarRadiusAxis_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function PolarRadiusAxis_extends(){return(PolarRadiusAxis_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function PolarRadiusAxis_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function PolarRadiusAxis_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?PolarRadiusAxis_ownKeys(Object(n),!0).forEach(function(t){PolarRadiusAxis_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):PolarRadiusAxis_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function PolarRadiusAxis_objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=PolarRadiusAxis_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function PolarRadiusAxis_objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function PolarRadiusAxis_classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function PolarRadiusAxis_defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,PolarRadiusAxis_toPropertyKey(o.key),o)}}function PolarRadiusAxis_createClass(e,t,n){return t&&PolarRadiusAxis_defineProperties(e.prototype,t),n&&PolarRadiusAxis_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function PolarRadiusAxis_callSuper(e,t,n){return t=PolarRadiusAxis_getPrototypeOf(t),PolarRadiusAxis_possibleConstructorReturn(e,PolarRadiusAxis_isNativeReflectConstruct()?Reflect.construct(t,n||[],PolarRadiusAxis_getPrototypeOf(e).constructor):t.apply(e,n))}function PolarRadiusAxis_possibleConstructorReturn(e,t){if(t&&("object"===PolarRadiusAxis_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return PolarRadiusAxis_assertThisInitialized(e)}function PolarRadiusAxis_assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function PolarRadiusAxis_isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(PolarRadiusAxis_isNativeReflectConstruct=function(){return!!e})()}function PolarRadiusAxis_getPrototypeOf(e){return(PolarRadiusAxis_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function PolarRadiusAxis_inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&PolarRadiusAxis_setPrototypeOf(e,t)}function PolarRadiusAxis_setPrototypeOf(e,t){return(PolarRadiusAxis_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function PolarRadiusAxis_defineProperty(e,t,n){return(t=PolarRadiusAxis_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function PolarRadiusAxis_toPropertyKey(e){var t=PolarRadiusAxis_toPrimitive(e,"string");return"symbol"==PolarRadiusAxis_typeof(t)?t:t+""}function PolarRadiusAxis_toPrimitive(e,t){if("object"!=PolarRadiusAxis_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=PolarRadiusAxis_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var w=function(e){function PolarRadiusAxis(){return PolarRadiusAxis_classCallCheck(this,PolarRadiusAxis),PolarRadiusAxis_callSuper(this,PolarRadiusAxis,arguments)}return PolarRadiusAxis_inherits(PolarRadiusAxis,e),PolarRadiusAxis_createClass(PolarRadiusAxis,[{key:"getTickValueCoord",value:function(e){var t=e.coordinate,n=this.props,o=n.angle,i=n.cx,a=n.cy;return(0,h.op)(i,a,t,o)}},{key:"getTickTextAnchor",value:function(){var e;switch(this.props.orientation){case"left":e="end";break;case"right":e="start";break;default:e="middle"}return e}},{key:"getViewBox",value:function(){var e=this.props,t=e.cx,n=e.cy,o=e.angle,i=e.ticks,a=_()(i,function(e){return e.coordinate||0});return{cx:t,cy:n,startAngle:o,endAngle:o,innerRadius:S()(i,function(e){return e.coordinate||0}).coordinate||0,outerRadius:a.coordinate||0}}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,n=e.cy,o=e.angle,a=e.ticks,c=e.axisLine,u=PolarRadiusAxis_objectWithoutProperties(e,O),s=a.reduce(function(e,t){return[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)]},[1/0,-1/0]),l=(0,h.op)(t,n,s[0],o),p=(0,h.op)(t,n,s[1],o),d=PolarRadiusAxis_objectSpread(PolarRadiusAxis_objectSpread(PolarRadiusAxis_objectSpread({},(0,f.L6)(u,!1)),{},{fill:"none"},(0,f.L6)(c,!1)),{},{x1:l.x,y1:l.y,x2:p.x,y2:p.y});return i.createElement("line",PolarRadiusAxis_extends({className:"recharts-polar-radius-axis-line"},d))}},{key:"renderTicks",value:function(){var e=this,t=this.props,n=t.ticks,o=t.tick,a=t.angle,c=t.tickFormatter,l=t.stroke,p=PolarRadiusAxis_objectWithoutProperties(t,j),d=this.getTickTextAnchor(),g=(0,f.L6)(p,!1),b=(0,f.L6)(o,!1),m=n.map(function(t,n){var f=e.getTickValueCoord(t),p=PolarRadiusAxis_objectSpread(PolarRadiusAxis_objectSpread(PolarRadiusAxis_objectSpread(PolarRadiusAxis_objectSpread({textAnchor:d,transform:"rotate(".concat(90-a,", ").concat(f.x,", ").concat(f.y,")")},g),{},{stroke:"none",fill:l},b),{},{index:n},f),{},{payload:t});return i.createElement(s.m,PolarRadiusAxis_extends({className:(0,u.Z)("recharts-polar-radius-axis-tick",(0,h.$S)(o)),key:"tick-".concat(t.coordinate)},(0,y.bw)(e.props,t,n)),PolarRadiusAxis.renderTickItem(o,p,c?c(t.value,n):t.value))});return i.createElement(s.m,{className:"recharts-polar-radius-axis-ticks"},m)}},{key:"render",value:function(){var e=this.props,t=e.ticks,n=e.axisLine,o=e.tick;return t&&t.length?i.createElement(s.m,{className:(0,u.Z)("recharts-polar-radius-axis",this.props.className)},n&&this.renderAxisLine(),o&&this.renderTicks(),P._.renderCallByParent(this.props,this.getViewBox())):null}}],[{key:"renderTickItem",value:function(e,t,n){return i.isValidElement(e)?i.cloneElement(e,t):c()(e)?e(t):i.createElement(d.x,PolarRadiusAxis_extends({},t,{className:"recharts-polar-radius-axis-tick-value"}),n)}}])}(i.PureComponent);PolarRadiusAxis_defineProperty(w,"displayName","PolarRadiusAxis"),PolarRadiusAxis_defineProperty(w,"axisType","radiusAxis"),PolarRadiusAxis_defineProperty(w,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var C=n(8485),A=(0,o.z)({chartName:"PieChart",GraphicalChild:C.b,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:b},{axisType:"radiusAxis",AxisComp:w}],formatAxisMap:h.t9,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}})},9205:function(e,t,n){"use strict";n.d(t,{z:function(){return generateCategoricalChart}});var o=n(2265),i=n(2727),a=n.n(i),c=n(8293),u=n.n(c),s=n(4943),l=n.n(s),f=n(8614),p=n.n(f),d=n(1864),y=n.n(d),h=n(7269),g=n.n(h),b=n(7042),m=n(2130),_=n(7434),x=n(8357),S=n(6812),P=n(9857),O=n(4304),j=n(6275),w=n(3843),C=n(5779),A=n(1224),T=n(3249),k=n(7281);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var E=["Webkit","Moz","O","ms"],generatePrefixStyle=function(e,t){if(!e)return null;var n=e.replace(/(\w)/,function(e){return e.toUpperCase()}),o=E.reduce(function(e,o){return _objectSpread(_objectSpread({},e),{},_defineProperty({},o+n,t))},{});return o[e]=t,o};function Brush_typeof(e){return(Brush_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function Brush_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function Brush_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Brush_ownKeys(Object(n),!0).forEach(function(t){Brush_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Brush_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Brush_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _callSuper(e,t,n){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],_getPrototypeOf(e).constructor):t.apply(e,n))}function _possibleConstructorReturn(e,t){if(t&&("object"===Brush_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Brush_defineProperty(e,t,n){return(t=Brush_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Brush_toPropertyKey(e){var t=Brush_toPrimitive(e,"string");return"symbol"==Brush_typeof(t)?t:t+""}function Brush_toPrimitive(e,t){if("object"!=Brush_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Brush_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var createScale=function(e){var t=e.data,n=e.startIndex,o=e.endIndex,i=e.x,a=e.width,c=e.travellerWidth;if(!t||!t.length)return{};var u=t.length,s=(0,C.x)().domain(l()(0,u)).range([i,i+a-c]),f=s.domain().map(function(e){return s(e)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(n),endX:s(o),scale:s,scaleValues:f}},isTouch=function(e){return e.changedTouches&&!!e.changedTouches.length},M=function(e){function Brush(e){var t;return _classCallCheck(this,Brush),Brush_defineProperty(t=_callSuper(this,Brush,[e]),"handleDrag",function(e){t.leaveTimer&&(clearTimeout(t.leaveTimer),t.leaveTimer=null),t.state.isTravellerMoving?t.handleTravellerMove(e):t.state.isSlideMoving&&t.handleSlideDrag(e)}),Brush_defineProperty(t,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&t.handleDrag(e.changedTouches[0])}),Brush_defineProperty(t,"handleDragEnd",function(){t.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var e=t.props,n=e.endIndex,o=e.onDragEnd,i=e.startIndex;null==o||o({endIndex:n,startIndex:i})}),t.detachDragEndListener()}),Brush_defineProperty(t,"handleLeaveWrapper",function(){(t.state.isTravellerMoving||t.state.isSlideMoving)&&(t.leaveTimer=window.setTimeout(t.handleDragEnd,t.props.leaveTimeOut))}),Brush_defineProperty(t,"handleEnterSlideOrTraveller",function(){t.setState({isTextActive:!0})}),Brush_defineProperty(t,"handleLeaveSlideOrTraveller",function(){t.setState({isTextActive:!1})}),Brush_defineProperty(t,"handleSlideDragStart",function(e){var n=isTouch(e)?e.changedTouches[0]:e;t.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:n.pageX}),t.attachDragEndListener()}),t.travellerDragStartHandlers={startX:t.handleTravellerDragStart.bind(t,"startX"),endX:t.handleTravellerDragStart.bind(t,"endX")},t.state={},t}return _inherits(Brush,e),_createClass(Brush,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(e){var t=e.startX,n=e.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Brush.getIndexInRange(o,Math.min(t,n)),s=Brush.getIndexInRange(o,Math.max(t,n));return{startIndex:u-u%a,endIndex:s===c?c:s-s%a}}},{key:"getTextOfTick",value:function(e){var t=this.props,n=t.data,o=t.tickFormatter,i=t.dataKey,a=(0,T.F$)(n[e],i,e);return u()(o)?o(a,e):a}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(e){var t=this.state,n=t.slideMoveStartX,o=t.startX,i=t.endX,a=this.props,c=a.x,u=a.width,s=a.travellerWidth,l=a.startIndex,f=a.endIndex,p=a.onChange,d=e.pageX-n;d>0?d=Math.min(d,c+u-s-i,c+u-s-o):d<0&&(d=Math.max(d,c-o,c-i));var y=this.getIndex({startX:o+d,endX:i+d});(y.startIndex!==l||y.endIndex!==f)&&p&&p(y),this.setState({startX:o+d,endX:i+d,slideMoveStartX:e.pageX})}},{key:"handleTravellerDragStart",value:function(e,t){var n=isTouch(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:n.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(e){var t=this.state,n=t.brushMoveStartX,o=t.movingTravellerId,i=t.endX,a=t.startX,c=this.state[o],u=this.props,s=u.x,l=u.width,f=u.travellerWidth,p=u.onChange,d=u.gap,y=u.data,h={startX:this.state.startX,endX:this.state.endX},g=e.pageX-n;g>0?g=Math.min(g,s+l-f-c):g<0&&(g=Math.max(g,s-c)),h[o]=c+g;var b=this.getIndex(h),m=b.startIndex,_=b.endIndex,isFullGap=function(){var e=y.length-1;return"startX"===o&&(i>a?m%d==0:_%d==0)||i<a&&_===e||"endX"===o&&(i>a?_%d==0:m%d==0)||i>a&&_===e};this.setState(Brush_defineProperty(Brush_defineProperty({},o,c+g),"brushMoveStartX",e.pageX),function(){p&&isFullGap()&&p(b)})}},{key:"handleTravellerMoveKeyboard",value:function(e,t){var n=this,o=this.state,i=o.scaleValues,a=o.startX,c=o.endX,u=this.state[t],s=i.indexOf(u);if(-1!==s){var l=s+e;if(-1!==l&&!(l>=i.length)){var f=i[l];"startX"===t&&f>=c||"endX"===t&&f<=a||this.setState(Brush_defineProperty({},t,f),function(){n.props.onChange(n.getIndex({startX:n.state.startX,endX:n.state.endX}))})}}}},{key:"renderBackground",value:function(){var e=this.props,t=e.x,n=e.y,i=e.width,a=e.height,c=e.fill,u=e.stroke;return o.createElement("rect",{stroke:u,fill:c,x:t,y:n,width:i,height:a})}},{key:"renderPanorama",value:function(){var e=this.props,t=e.x,n=e.y,i=e.width,a=e.height,c=e.data,u=e.children,s=e.padding,l=o.Children.only(u);return l?o.cloneElement(l,{x:t,y:n,width:i,height:a,margin:s,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(e,t){var n,i,a=this,c=this.props,u=c.y,s=c.travellerWidth,l=c.height,f=c.traveller,p=c.ariaLabel,d=c.data,y=c.startIndex,h=c.endIndex,g=Math.max(e,this.props.x),b=Brush_objectSpread(Brush_objectSpread({},(0,w.L6)(this.props,!1)),{},{x:g,y:u,width:s,height:l}),m=p||"Min value: ".concat(null===(n=d[y])||void 0===n?void 0:n.name,", Max value: ").concat(null===(i=d[h])||void 0===i?void 0:i.name);return o.createElement(x.m,{tabIndex:0,role:"slider","aria-label":m,"aria-valuenow":e,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[t],onTouchStart:this.travellerDragStartHandlers[t],onKeyDown:function(e){["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===e.key?1:-1,t))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},Brush.renderTraveller(f,b))}},{key:"renderSlide",value:function(e,t){var n=this.props,i=n.y,a=n.height,c=n.stroke,u=n.travellerWidth;return o.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:Math.min(e,t)+u,y:i,width:Math.max(Math.abs(t-e)-u,0),height:a})}},{key:"renderText",value:function(){var e=this.props,t=e.startIndex,n=e.endIndex,i=e.y,a=e.height,c=e.travellerWidth,u=e.stroke,s=this.state,l=s.startX,f=s.endX,p={pointerEvents:"none",fill:u};return o.createElement(x.m,{className:"recharts-brush-texts"},o.createElement(A.x,_extends({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,f)-5,y:i+a/2},p),this.getTextOfTick(t)),o.createElement(A.x,_extends({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,f)+c+5,y:i+a/2},p),this.getTextOfTick(n)))}},{key:"render",value:function(){var e=this.props,t=e.data,n=e.className,i=e.children,a=e.x,c=e.y,u=e.width,s=e.height,l=e.alwaysShowText,f=this.state,p=f.startX,d=f.endX,y=f.isTextActive,h=f.isSlideMoving,g=f.isTravellerMoving,m=f.isTravellerFocused;if(!t||!t.length||!(0,k.hj)(a)||!(0,k.hj)(c)||!(0,k.hj)(u)||!(0,k.hj)(s)||u<=0||s<=0)return null;var _=(0,b.Z)("recharts-brush",n),S=1===o.Children.count(i),P=generatePrefixStyle("userSelect","none");return o.createElement(x.m,{className:_,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:P},this.renderBackground(),S&&this.renderPanorama(),this.renderSlide(p,d),this.renderTravellerLayer(p,"startX"),this.renderTravellerLayer(d,"endX"),(y||h||g||m||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(e){var t=e.x,n=e.y,i=e.width,a=e.height,c=e.stroke,u=Math.floor(n+a/2)-1;return o.createElement(o.Fragment,null,o.createElement("rect",{x:t,y:n,width:i,height:a,fill:c,stroke:"none"}),o.createElement("line",{x1:t+1,y1:u,x2:t+i-1,y2:u,fill:"none",stroke:"#fff"}),o.createElement("line",{x1:t+1,y1:u+2,x2:t+i-1,y2:u+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(e,t){return o.isValidElement(e)?o.cloneElement(e,t):u()(e)?e(t):Brush.renderDefaultTraveller(t)}},{key:"getDerivedStateFromProps",value:function(e,t){var n=e.data,o=e.width,i=e.x,a=e.travellerWidth,c=e.updateId,u=e.startIndex,s=e.endIndex;if(n!==t.prevData||c!==t.prevUpdateId)return Brush_objectSpread({prevData:n,prevTravellerWidth:a,prevUpdateId:c,prevX:i,prevWidth:o},n&&n.length?createScale({data:n,width:o,x:i,travellerWidth:a,startIndex:u,endIndex:s}):{scale:null,scaleValues:null});if(t.scale&&(o!==t.prevWidth||i!==t.prevX||a!==t.prevTravellerWidth)){t.scale.range([i,i+o-a]);var l=t.scale.domain().map(function(e){return t.scale(e)});return{prevData:n,prevTravellerWidth:a,prevUpdateId:c,prevX:i,prevWidth:o,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(e,t){for(var n=e.length,o=0,i=n-1;i-o>1;){var a=Math.floor((o+i)/2);e[a]>t?i=a:o=a}return t>=e[i]?i:o}}])}(o.PureComponent);Brush_defineProperty(M,"displayName","Brush"),Brush_defineProperty(M,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var I=n(4768),D=n(9776),R=n(3343),ifOverflowMatches=function(e,t){var n=e.alwaysShow,o=e.ifOverflow;return n&&(o="extendDomain"),o===t},L=n(9677),N=n(7105);function ReferenceDot_extends(){return(ReferenceDot_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function ReferenceDot_typeof(e){return(ReferenceDot_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ReferenceDot_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function ReferenceDot_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ReferenceDot_ownKeys(Object(n),!0).forEach(function(t){ReferenceDot_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ReferenceDot_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function ReferenceDot_classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function ReferenceDot_defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,ReferenceDot_toPropertyKey(o.key),o)}}function ReferenceDot_createClass(e,t,n){return t&&ReferenceDot_defineProperties(e.prototype,t),n&&ReferenceDot_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function ReferenceDot_callSuper(e,t,n){return t=ReferenceDot_getPrototypeOf(t),ReferenceDot_possibleConstructorReturn(e,ReferenceDot_isNativeReflectConstruct()?Reflect.construct(t,n||[],ReferenceDot_getPrototypeOf(e).constructor):t.apply(e,n))}function ReferenceDot_possibleConstructorReturn(e,t){if(t&&("object"===ReferenceDot_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return ReferenceDot_assertThisInitialized(e)}function ReferenceDot_assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ReferenceDot_isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ReferenceDot_isNativeReflectConstruct=function(){return!!e})()}function ReferenceDot_getPrototypeOf(e){return(ReferenceDot_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ReferenceDot_inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ReferenceDot_setPrototypeOf(e,t)}function ReferenceDot_setPrototypeOf(e,t){return(ReferenceDot_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ReferenceDot_defineProperty(e,t,n){return(t=ReferenceDot_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ReferenceDot_toPropertyKey(e){var t=ReferenceDot_toPrimitive(e,"string");return"symbol"==ReferenceDot_typeof(t)?t:t+""}function ReferenceDot_toPrimitive(e,t){if("object"!=ReferenceDot_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=ReferenceDot_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var getCoordinate=function(e){var t=e.x,n=e.y,o=e.xAxis,i=e.yAxis,a=(0,L.Ky)({x:o.scale,y:i.scale}),c=a.apply({x:t,y:n},{bandAware:!0});return ifOverflowMatches(e,"discard")&&!a.isInRange(c)?null:c},B=function(e){function ReferenceDot(){return ReferenceDot_classCallCheck(this,ReferenceDot),ReferenceDot_callSuper(this,ReferenceDot,arguments)}return ReferenceDot_inherits(ReferenceDot,e),ReferenceDot_createClass(ReferenceDot,[{key:"render",value:function(){var e=this.props,t=e.x,n=e.y,i=e.r,a=e.alwaysShow,c=e.clipPathId,u=(0,k.P2)(t),s=(0,k.P2)(n);if((0,N.Z)(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!s)return null;var l=getCoordinate(this.props);if(!l)return null;var f=l.x,p=l.y,d=this.props,y=d.shape,h=d.className,g=ReferenceDot_objectSpread(ReferenceDot_objectSpread({clipPath:ifOverflowMatches(this.props,"hidden")?"url(#".concat(c,")"):void 0},(0,w.L6)(this.props,!0)),{},{cx:f,cy:p});return o.createElement(x.m,{className:(0,b.Z)("recharts-reference-dot",h)},ReferenceDot.renderDot(y,g),R._.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}])}(o.Component);ReferenceDot_defineProperty(B,"displayName","ReferenceDot"),ReferenceDot_defineProperty(B,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),ReferenceDot_defineProperty(B,"renderDot",function(e,t){return o.isValidElement(e)?o.cloneElement(e,t):u()(e)?e(t):o.createElement(O.o,ReferenceDot_extends({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"}))});var W=n(7874),U=n.n(W),z=n(2794);function ReferenceLine_typeof(e){return(ReferenceLine_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ReferenceLine_classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function ReferenceLine_defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,ReferenceLine_toPropertyKey(o.key),o)}}function ReferenceLine_createClass(e,t,n){return t&&ReferenceLine_defineProperties(e.prototype,t),n&&ReferenceLine_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function ReferenceLine_callSuper(e,t,n){return t=ReferenceLine_getPrototypeOf(t),ReferenceLine_possibleConstructorReturn(e,ReferenceLine_isNativeReflectConstruct()?Reflect.construct(t,n||[],ReferenceLine_getPrototypeOf(e).constructor):t.apply(e,n))}function ReferenceLine_possibleConstructorReturn(e,t){if(t&&("object"===ReferenceLine_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return ReferenceLine_assertThisInitialized(e)}function ReferenceLine_assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ReferenceLine_isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ReferenceLine_isNativeReflectConstruct=function(){return!!e})()}function ReferenceLine_getPrototypeOf(e){return(ReferenceLine_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ReferenceLine_inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ReferenceLine_setPrototypeOf(e,t)}function ReferenceLine_setPrototypeOf(e,t){return(ReferenceLine_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ReferenceLine_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function ReferenceLine_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ReferenceLine_ownKeys(Object(n),!0).forEach(function(t){ReferenceLine_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ReferenceLine_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function ReferenceLine_defineProperty(e,t,n){return(t=ReferenceLine_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ReferenceLine_toPropertyKey(e){var t=ReferenceLine_toPrimitive(e,"string");return"symbol"==ReferenceLine_typeof(t)?t:t+""}function ReferenceLine_toPrimitive(e,t){if("object"!=ReferenceLine_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=ReferenceLine_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,a,c,u=[],s=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(u.push(o.value),u.length!==t);s=!0);}catch(e){l=!0,i=e}finally{try{if(!s&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(l)throw i}}return u}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function ReferenceLine_extends(){return(ReferenceLine_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}var getEndPoints=function(e,t,n,o,i,a,c,u,s){var l=i.x,f=i.y,p=i.width,d=i.height;if(n){var y=s.y,h=e.y.apply(y,{position:a});if(ifOverflowMatches(s,"discard")&&!e.y.isInRange(h))return null;var g=[{x:l+p,y:h},{x:l,y:h}];return"left"===u?g.reverse():g}if(t){var b=s.x,m=e.x.apply(b,{position:a});if(ifOverflowMatches(s,"discard")&&!e.x.isInRange(m))return null;var _=[{x:m,y:f+d},{x:m,y:f}];return"top"===c?_.reverse():_}if(o){var x=s.segment.map(function(t){return e.apply(t,{position:a})});return ifOverflowMatches(s,"discard")&&U()(x,function(t){return!e.isInRange(t)})?null:x}return null};function ReferenceLineImpl(e){var t,n,i=e.x,a=e.y,c=e.segment,s=e.xAxisId,l=e.yAxisId,f=e.shape,p=e.className,d=e.alwaysShow,y=(0,z.sp)(),h=(0,z.bH)(s),g=(0,z.Ud)(l),m=(0,z.d2)();if(!y||!m)return null;(0,N.Z)(void 0===d,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var _=getEndPoints((0,L.Ky)({x:h.scale,y:g.scale}),(0,k.P2)(i),(0,k.P2)(a),c&&2===c.length,m,e.position,h.orientation,g.orientation,e);if(!_)return null;var S=_slicedToArray(_,2),P=S[0],O=P.x,j=P.y,C=S[1],A=C.x,T=C.y,E=ReferenceLine_objectSpread(ReferenceLine_objectSpread({clipPath:ifOverflowMatches(e,"hidden")?"url(#".concat(y,")"):void 0},(0,w.L6)(e,!0)),{},{x1:O,y1:j,x2:A,y2:T});return o.createElement(x.m,{className:(0,b.Z)("recharts-reference-line",p)},(t=f,n=E,o.isValidElement(t)?o.cloneElement(t,n):u()(t)?t(n):o.createElement("line",ReferenceLine_extends({},n,{className:"recharts-reference-line-line"}))),R._.renderCallByParent(e,(0,L._b)({x1:O,y1:j,x2:A,y2:T})))}var K=function(e){function ReferenceLine(){return ReferenceLine_classCallCheck(this,ReferenceLine),ReferenceLine_callSuper(this,ReferenceLine,arguments)}return ReferenceLine_inherits(ReferenceLine,e),ReferenceLine_createClass(ReferenceLine,[{key:"render",value:function(){return o.createElement(ReferenceLineImpl,this.props)}}])}(o.Component);function ReferenceArea_extends(){return(ReferenceArea_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function ReferenceArea_typeof(e){return(ReferenceArea_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ReferenceArea_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function ReferenceArea_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ReferenceArea_ownKeys(Object(n),!0).forEach(function(t){ReferenceArea_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ReferenceArea_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function ReferenceArea_classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function ReferenceArea_defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,ReferenceArea_toPropertyKey(o.key),o)}}function ReferenceArea_createClass(e,t,n){return t&&ReferenceArea_defineProperties(e.prototype,t),n&&ReferenceArea_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function ReferenceArea_callSuper(e,t,n){return t=ReferenceArea_getPrototypeOf(t),ReferenceArea_possibleConstructorReturn(e,ReferenceArea_isNativeReflectConstruct()?Reflect.construct(t,n||[],ReferenceArea_getPrototypeOf(e).constructor):t.apply(e,n))}function ReferenceArea_possibleConstructorReturn(e,t){if(t&&("object"===ReferenceArea_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return ReferenceArea_assertThisInitialized(e)}function ReferenceArea_assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ReferenceArea_isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ReferenceArea_isNativeReflectConstruct=function(){return!!e})()}function ReferenceArea_getPrototypeOf(e){return(ReferenceArea_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ReferenceArea_inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ReferenceArea_setPrototypeOf(e,t)}function ReferenceArea_setPrototypeOf(e,t){return(ReferenceArea_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ReferenceArea_defineProperty(e,t,n){return(t=ReferenceArea_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ReferenceArea_toPropertyKey(e){var t=ReferenceArea_toPrimitive(e,"string");return"symbol"==ReferenceArea_typeof(t)?t:t+""}function ReferenceArea_toPrimitive(e,t){if("object"!=ReferenceArea_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=ReferenceArea_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}ReferenceLine_defineProperty(K,"displayName","ReferenceLine"),ReferenceLine_defineProperty(K,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var getRect=function(e,t,n,o,i){var a=i.x1,c=i.x2,u=i.y1,s=i.y2,l=i.xAxis,f=i.yAxis;if(!l||!f)return null;var p=(0,L.Ky)({x:l.scale,y:f.scale}),d={x:e?p.x.apply(a,{position:"start"}):p.x.rangeMin,y:n?p.y.apply(u,{position:"start"}):p.y.rangeMin},y={x:t?p.x.apply(c,{position:"end"}):p.x.rangeMax,y:o?p.y.apply(s,{position:"end"}):p.y.rangeMax};return!ifOverflowMatches(i,"discard")||p.isInRange(d)&&p.isInRange(y)?(0,L.O1)(d,y):null},F=function(e){function ReferenceArea(){return ReferenceArea_classCallCheck(this,ReferenceArea),ReferenceArea_callSuper(this,ReferenceArea,arguments)}return ReferenceArea_inherits(ReferenceArea,e),ReferenceArea_createClass(ReferenceArea,[{key:"render",value:function(){var e=this.props,t=e.x1,n=e.x2,i=e.y1,a=e.y2,c=e.className,u=e.alwaysShow,s=e.clipPathId;(0,N.Z)(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=(0,k.P2)(t),f=(0,k.P2)(n),p=(0,k.P2)(i),d=(0,k.P2)(a),y=this.props.shape;if(!l&&!f&&!p&&!d&&!y)return null;var h=getRect(l,f,p,d,this.props);if(!h&&!y)return null;var g=ifOverflowMatches(this.props,"hidden")?"url(#".concat(s,")"):void 0;return o.createElement(x.m,{className:(0,b.Z)("recharts-reference-area",c)},ReferenceArea.renderRect(y,ReferenceArea_objectSpread(ReferenceArea_objectSpread({clipPath:g},(0,w.L6)(this.props,!0)),h)),R._.renderCallByParent(this.props,h))}}])}(o.Component);function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||DetectReferenceElementsDomain_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function DetectReferenceElementsDomain_unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return DetectReferenceElementsDomain_arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return DetectReferenceElementsDomain_arrayLikeToArray(e,t)}}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return DetectReferenceElementsDomain_arrayLikeToArray(e)}function DetectReferenceElementsDomain_arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}ReferenceArea_defineProperty(F,"displayName","ReferenceArea"),ReferenceArea_defineProperty(F,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),ReferenceArea_defineProperty(F,"renderRect",function(e,t){return o.isValidElement(e)?o.cloneElement(e,t):u()(e)?e(t):o.createElement(j.A,ReferenceArea_extends({},t,{className:"recharts-reference-area-rect"}))});var detectReferenceElementsDomain=function(e,t,n,o,i){var a=(0,w.NN)(e,K),c=(0,w.NN)(e,B),u=[].concat(_toConsumableArray(a),_toConsumableArray(c)),s=(0,w.NN)(e,F),l="".concat(o,"Id"),f=o[0],p=t;if(u.length&&(p=u.reduce(function(e,t){if(t.props[l]===n&&ifOverflowMatches(t.props,"extendDomain")&&(0,k.hj)(t.props[f])){var o=t.props[f];return[Math.min(e[0],o),Math.max(e[1],o)]}return e},p)),s.length){var d="".concat(f,"1"),y="".concat(f,"2");p=s.reduce(function(e,t){if(t.props[l]===n&&ifOverflowMatches(t.props,"extendDomain")&&(0,k.hj)(t.props[d])&&(0,k.hj)(t.props[y])){var o=t.props[d],i=t.props[y];return[Math.min(e[0],o,i),Math.max(e[1],o,i)]}return e},p)}return i&&i.length&&(p=i.reduce(function(e,t){return(0,k.hj)(t)?[Math.min(e[0],t),Math.max(e[1],t)]:e},p)),p},H=n(6120),Z=n(1374),$=n(8729),V=new(n.n($)()),Y="recharts.syncMouseEvents",G=n(2655);function AccessibilityManager_typeof(e){return(AccessibilityManager_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function AccessibilityManager_classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function AccessibilityManager_defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,AccessibilityManager_toPropertyKey(o.key),o)}}function AccessibilityManager_createClass(e,t,n){return t&&AccessibilityManager_defineProperties(e.prototype,t),n&&AccessibilityManager_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function AccessibilityManager_defineProperty(e,t,n){return(t=AccessibilityManager_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function AccessibilityManager_toPropertyKey(e){var t=AccessibilityManager_toPrimitive(e,"string");return"symbol"==AccessibilityManager_typeof(t)?t:t+""}function AccessibilityManager_toPrimitive(e,t){if("object"!=AccessibilityManager_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=AccessibilityManager_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var X=function(){function AccessibilityManager(){AccessibilityManager_classCallCheck(this,AccessibilityManager),AccessibilityManager_defineProperty(this,"activeIndex",0),AccessibilityManager_defineProperty(this,"coordinateList",[]),AccessibilityManager_defineProperty(this,"layout","horizontal")}return AccessibilityManager_createClass(AccessibilityManager,[{key:"setDetails",value:function(e){var t,n=e.coordinateList,o=void 0===n?null:n,i=e.container,a=void 0===i?null:i,c=e.layout,u=void 0===c?null:c,s=e.offset,l=void 0===s?null:s,f=e.mouseHandlerCallback,p=void 0===f?null:f;this.coordinateList=null!==(t=null!=o?o:this.coordinateList)&&void 0!==t?t:[],this.container=null!=a?a:this.container,this.layout=null!=u?u:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=p?p:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(e){if(0!==this.coordinateList.length)switch(e.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(e){this.activeIndex=e}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var e,t,n=this.container.getBoundingClientRect(),o=n.x,i=n.y,a=n.height,c=this.coordinateList[this.activeIndex].coordinate,u=(null===(e=window)||void 0===e?void 0:e.scrollX)||0,s=(null===(t=window)||void 0===t?void 0:t.scrollY)||0,l=i+this.offset.top+a/2+s;this.mouseHandlerCallback({pageX:o+c+u,pageY:l})}}}])}();function isDomainSpecifiedByUser(e,t,n){if("number"===n&&!0===t&&Array.isArray(e)){var o=null==e?void 0:e[0],i=null==e?void 0:e[1];if(o&&i&&(0,k.hj)(o)&&(0,k.hj)(i))return!0}return!1}var Q=n(7688),J=n(9431);function Cross_typeof(e){return(Cross_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var ee=["x","y","top","left","width","height","className"];function Cross_extends(){return(Cross_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function Cross_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function Cross_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Cross_ownKeys(Object(n),!0).forEach(function(t){Cross_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Cross_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Cross_defineProperty(e,t,n){return(t=Cross_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Cross_toPropertyKey(e){var t=Cross_toPrimitive(e,"string");return"symbol"==Cross_typeof(t)?t:t+""}function Cross_toPrimitive(e,t){if("object"!=Cross_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Cross_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}var Cross=function(e){var t=e.x,n=void 0===t?0:t,i=e.y,a=void 0===i?0:i,c=e.top,u=void 0===c?0:c,s=e.left,l=void 0===s?0:s,f=e.width,p=void 0===f?0:f,d=e.height,y=void 0===d?0:d,h=e.className,g=Cross_objectSpread({x:n,y:a,top:u,left:l,width:p,height:y},_objectWithoutProperties(e,ee));return(0,k.hj)(n)&&(0,k.hj)(a)&&(0,k.hj)(p)&&(0,k.hj)(y)&&(0,k.hj)(u)&&(0,k.hj)(l)?o.createElement("path",Cross_extends({},(0,w.L6)(g,!0),{className:(0,b.Z)("recharts-cross",h),d:"M".concat(n,",").concat(u,"v").concat(y,"M").concat(l,",").concat(a,"h").concat(p)})):null};function getCursorRectangle(e,t,n,o){var i=o/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===e?t.x-i:n.left+.5,y:"horizontal"===e?n.top+.5:t.y-i,width:"horizontal"===e?o:n.width-1,height:"horizontal"===e?n.height-1:o}}function getRadialCursorPoints(e){var t=e.cx,n=e.cy,o=e.radius,i=e.startAngle,a=e.endAngle;return{points:[(0,H.op)(t,n,o,i),(0,H.op)(t,n,o,a)],cx:t,cy:n,radius:o,startAngle:i,endAngle:a}}var et=n(7795);function getCursorPoints(e,t,n){var o,i,a,c;if("horizontal"===e)a=o=t.x,i=n.top,c=n.top+n.height;else if("vertical"===e)c=i=t.y,o=n.left,a=n.left+n.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return getRadialCursorPoints(t);var u=t.cx,s=t.cy,l=t.innerRadius,f=t.outerRadius,p=t.angle,d=(0,H.op)(u,s,l,p),y=(0,H.op)(u,s,f,p);o=d.x,i=d.y,a=y.x,c=y.y}return[{x:o,y:i},{x:a,y:c}]}function Cursor_typeof(e){return(Cursor_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Cursor_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function Cursor_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Cursor_ownKeys(Object(n),!0).forEach(function(t){Cursor_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Cursor_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Cursor_defineProperty(e,t,n){return(t=Cursor_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Cursor_toPropertyKey(e){var t=Cursor_toPrimitive(e,"string");return"symbol"==Cursor_typeof(t)?t:t+""}function Cursor_toPrimitive(e,t){if("object"!=Cursor_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Cursor_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function Cursor(e){var t,n,i,a=e.element,c=e.tooltipEventType,u=e.isActive,s=e.activeCoordinate,l=e.activePayload,f=e.offset,p=e.activeTooltipIndex,d=e.tooltipAxisBandSize,y=e.layout,h=e.chartName,g=null!==(t=a.props.cursor)&&void 0!==t?t:null===(n=a.type.defaultProps)||void 0===n?void 0:n.cursor;if(!a||!g||!u||!s||"ScatterChart"!==h&&"axis"!==c)return null;var m=J.H;if("ScatterChart"===h)i=s,m=Cross;else if("BarChart"===h)i=getCursorRectangle(y,s,f,d),m=j.A;else if("radial"===y){var _=getRadialCursorPoints(s),x=_.cx,S=_.cy,P=_.radius;i={cx:x,cy:S,startAngle:_.startAngle,endAngle:_.endAngle,innerRadius:P,outerRadius:P},m=et.L}else i={points:getCursorPoints(y,s,f)},m=J.H;var O=Cursor_objectSpread(Cursor_objectSpread(Cursor_objectSpread(Cursor_objectSpread({stroke:"#ccc",pointerEvents:"none"},f),i),(0,w.L6)(g,!1)),{},{payload:l,payloadIndex:p,className:(0,b.Z)("recharts-tooltip-cursor",g.className)});return(0,o.isValidElement)(g)?(0,o.cloneElement)(g,O):(0,o.createElement)(m,O)}var er=["item"],en=["children","className","width","height","style","compact","title","desc"];function generateCategoricalChart_typeof(e){return(generateCategoricalChart_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function generateCategoricalChart_extends(){return(generateCategoricalChart_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function generateCategoricalChart_slicedToArray(e,t){return generateCategoricalChart_arrayWithHoles(e)||generateCategoricalChart_iterableToArrayLimit(e,t)||generateCategoricalChart_unsupportedIterableToArray(e,t)||generateCategoricalChart_nonIterableRest()}function generateCategoricalChart_nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function generateCategoricalChart_iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,a,c,u=[],s=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(u.push(o.value),u.length!==t);s=!0);}catch(e){l=!0,i=e}finally{try{if(!s&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(l)throw i}}return u}}function generateCategoricalChart_arrayWithHoles(e){if(Array.isArray(e))return e}function generateCategoricalChart_objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=generateCategoricalChart_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function generateCategoricalChart_objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function generateCategoricalChart_classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function generateCategoricalChart_defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,generateCategoricalChart_toPropertyKey(o.key),o)}}function generateCategoricalChart_createClass(e,t,n){return t&&generateCategoricalChart_defineProperties(e.prototype,t),n&&generateCategoricalChart_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function generateCategoricalChart_callSuper(e,t,n){return t=generateCategoricalChart_getPrototypeOf(t),generateCategoricalChart_possibleConstructorReturn(e,generateCategoricalChart_isNativeReflectConstruct()?Reflect.construct(t,n||[],generateCategoricalChart_getPrototypeOf(e).constructor):t.apply(e,n))}function generateCategoricalChart_possibleConstructorReturn(e,t){if(t&&("object"===generateCategoricalChart_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return generateCategoricalChart_assertThisInitialized(e)}function generateCategoricalChart_assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function generateCategoricalChart_isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(generateCategoricalChart_isNativeReflectConstruct=function(){return!!e})()}function generateCategoricalChart_getPrototypeOf(e){return(generateCategoricalChart_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function generateCategoricalChart_inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&generateCategoricalChart_setPrototypeOf(e,t)}function generateCategoricalChart_setPrototypeOf(e,t){return(generateCategoricalChart_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function generateCategoricalChart_toConsumableArray(e){return generateCategoricalChart_arrayWithoutHoles(e)||generateCategoricalChart_iterableToArray(e)||generateCategoricalChart_unsupportedIterableToArray(e)||generateCategoricalChart_nonIterableSpread()}function generateCategoricalChart_nonIterableSpread(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function generateCategoricalChart_unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return generateCategoricalChart_arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return generateCategoricalChart_arrayLikeToArray(e,t)}}function generateCategoricalChart_iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function generateCategoricalChart_arrayWithoutHoles(e){if(Array.isArray(e))return generateCategoricalChart_arrayLikeToArray(e)}function generateCategoricalChart_arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function generateCategoricalChart_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function generateCategoricalChart_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?generateCategoricalChart_ownKeys(Object(n),!0).forEach(function(t){generateCategoricalChart_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):generateCategoricalChart_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function generateCategoricalChart_defineProperty(e,t,n){return(t=generateCategoricalChart_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function generateCategoricalChart_toPropertyKey(e){var t=generateCategoricalChart_toPrimitive(e,"string");return"symbol"==generateCategoricalChart_typeof(t)?t:t+""}function generateCategoricalChart_toPrimitive(e,t){if("object"!=generateCategoricalChart_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=generateCategoricalChart_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var eo={xAxis:["bottom","top"],yAxis:["left","right"]},ei={width:"100%",height:"100%"},ea={x:0,y:0};function renderAsIs(e){return e}var getActiveCoordinate=function(e,t,n,o){var i=t.find(function(e){return e&&e.index===n});if(i){if("horizontal"===e)return{x:i.coordinate,y:o.y};if("vertical"===e)return{x:o.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,c=o.radius;return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},o),(0,H.op)(o.cx,o.cy,c,a)),{},{angle:a,radius:c})}var u=i.coordinate,s=o.angle;return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},o),(0,H.op)(o.cx,o.cy,u,s)),{},{angle:s,radius:u})}return ea},getDisplayedData=function(e,t){var n=t.graphicalItems,o=t.dataStartIndex,i=t.dataEndIndex,a=(null!=n?n:[]).reduce(function(e,t){var n=t.props.data;return n&&n.length?[].concat(generateCategoricalChart_toConsumableArray(e),generateCategoricalChart_toConsumableArray(n)):e},[]);return a.length>0?a:e&&e.length&&(0,k.hj)(o)&&(0,k.hj)(i)?e.slice(o,i+1):[]};function getDefaultDomainByAxisType(e){return"number"===e?[0,"auto"]:void 0}var getTooltipContent=function(e,t,n,o){var i=e.graphicalItems,a=e.tooltipAxis,c=getDisplayedData(t,e);return n<0||!i||!i.length||n>=c.length?null:i.reduce(function(i,u){var s,l,f=null!==(s=u.props.data)&&void 0!==s?s:t;if(f&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=n&&(f=f.slice(e.dataStartIndex,e.dataEndIndex+1)),a.dataKey&&!a.allowDuplicatedCategory){var p=void 0===f?c:f;l=(0,k.Ap)(p,a.dataKey,o)}else l=f&&f[n]||c[n];return l?[].concat(generateCategoricalChart_toConsumableArray(i),[(0,T.Qo)(u,l)]):i},[])},getTooltipData=function(e,t,n,o){var i=o||{x:e.chartX,y:e.chartY},a="horizontal"===n?i.x:"vertical"===n?i.y:"centric"===n?i.angle:i.radius,c=e.orderedTooltipTicks,u=e.tooltipAxis,s=e.tooltipTicks,l=(0,T.VO)(a,c,s,u);if(l>=0&&s){var f=s[l]&&s[l].value,p=getTooltipContent(e,t,l,f),d=getActiveCoordinate(n,c,l,i);return{activeTooltipIndex:l,activeLabel:f,activePayload:p,activeCoordinate:d}}return null},getAxisMapByAxes=function(e,t){var n=t.axes,o=t.graphicalItems,i=t.axisType,c=t.axisIdKey,u=t.stackGroups,s=t.dataStartIndex,f=t.dataEndIndex,p=e.layout,d=e.children,y=e.stackOffset,h=(0,T.NA)(p,i);return n.reduce(function(t,n){var g=void 0!==n.type.defaultProps?generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},n.type.defaultProps),n.props):n.props,b=g.type,m=g.dataKey,_=g.allowDataOverflow,x=g.allowDuplicatedCategory,S=g.scale,P=g.ticks,O=g.includeHidden,j=g[c];if(t[j])return t;var w=getDisplayedData(e.data,{graphicalItems:o.filter(function(e){var t;return(c in e.props?e.props[c]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[c])===j}),dataStartIndex:s,dataEndIndex:f}),C=w.length;isDomainSpecifiedByUser(g.domain,_,b)&&(M=(0,T.LG)(g.domain,null,_),h&&("number"===b||"auto"!==S)&&(D=(0,T.gF)(w,m,"category")));var A=getDefaultDomainByAxisType(b);if(!M||0===M.length){var E,M,I,D,R,L=null!==(R=g.domain)&&void 0!==R?R:A;if(m){if(M=(0,T.gF)(w,m,b),"category"===b&&h){var N=(0,k.bv)(M);x&&N?(I=M,M=l()(0,C)):x||(M=(0,T.ko)(L,M,n).reduce(function(e,t){return e.indexOf(t)>=0?e:[].concat(generateCategoricalChart_toConsumableArray(e),[t])},[]))}else if("category"===b)M=x?M.filter(function(e){return""!==e&&!a()(e)}):(0,T.ko)(L,M,n).reduce(function(e,t){return e.indexOf(t)>=0||""===t||a()(t)?e:[].concat(generateCategoricalChart_toConsumableArray(e),[t])},[]);else if("number"===b){var B=(0,T.ZI)(w,o.filter(function(e){var t,n,o=c in e.props?e.props[c]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[c],i="hide"in e.props?e.props.hide:null===(n=e.type.defaultProps)||void 0===n?void 0:n.hide;return o===j&&(O||!i)}),m,i,p);B&&(M=B)}h&&("number"===b||"auto"!==S)&&(D=(0,T.gF)(w,m,"category"))}else M=h?l()(0,C):u&&u[j]&&u[j].hasStack&&"number"===b?"expand"===y?[0,1]:(0,T.EB)(u[j].stackGroups,s,f):(0,T.s6)(w,o.filter(function(e){var t=c in e.props?e.props[c]:e.type.defaultProps[c],n="hide"in e.props?e.props.hide:e.type.defaultProps.hide;return t===j&&(O||!n)}),b,p,!0);"number"===b?(M=detectReferenceElementsDomain(d,M,j,i,P),L&&(M=(0,T.LG)(L,M,_))):"category"===b&&L&&M.every(function(e){return L.indexOf(e)>=0})&&(M=L)}return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},t),{},generateCategoricalChart_defineProperty({},j,generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},g),{},{axisType:i,domain:M,categoricalDomain:D,duplicateDomain:I,originalDomain:null!==(E=g.domain)&&void 0!==E?E:A,isCategorical:h,layout:p})))},{})},getAxisMapByItems=function(e,t){var n=t.graphicalItems,o=t.Axis,i=t.axisType,a=t.axisIdKey,c=t.stackGroups,u=t.dataStartIndex,s=t.dataEndIndex,f=e.layout,d=e.children,y=getDisplayedData(e.data,{graphicalItems:n,dataStartIndex:u,dataEndIndex:s}),h=y.length,g=(0,T.NA)(f,i),b=-1;return n.reduce(function(e,t){var m,_=(void 0!==t.type.defaultProps?generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},t.type.defaultProps),t.props):t.props)[a],x=getDefaultDomainByAxisType("number");return e[_]?e:(b++,m=g?l()(0,h):c&&c[_]&&c[_].hasStack?detectReferenceElementsDomain(d,m=(0,T.EB)(c[_].stackGroups,u,s),_,i):detectReferenceElementsDomain(d,m=(0,T.LG)(x,(0,T.s6)(y,n.filter(function(e){var t,n,o=a in e.props?e.props[a]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[a],i="hide"in e.props?e.props.hide:null===(n=e.type.defaultProps)||void 0===n?void 0:n.hide;return o===_&&!i}),"number",f),o.defaultProps.allowDataOverflow),_,i),generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},e),{},generateCategoricalChart_defineProperty({},_,generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({axisType:i},o.defaultProps),{},{hide:!0,orientation:p()(eo,"".concat(i,".").concat(b%2),null),domain:m,originalDomain:x,isCategorical:g,layout:f}))))},{})},getAxisMap=function(e,t){var n=t.axisType,o=void 0===n?"xAxis":n,i=t.AxisComp,a=t.graphicalItems,c=t.stackGroups,u=t.dataStartIndex,s=t.dataEndIndex,l=e.children,f="".concat(o,"Id"),p=(0,w.NN)(l,i),d={};return p&&p.length?d=getAxisMapByAxes(e,{axes:p,graphicalItems:a,axisType:o,axisIdKey:f,stackGroups:c,dataStartIndex:u,dataEndIndex:s}):a&&a.length&&(d=getAxisMapByItems(e,{Axis:i,graphicalItems:a,axisType:o,axisIdKey:f,stackGroups:c,dataStartIndex:u,dataEndIndex:s})),d},tooltipTicksGenerator=function(e){var t=(0,k.Kt)(e),n=(0,T.uY)(t,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:y()(n,function(e){return e.coordinate}),tooltipAxis:t,tooltipAxisBandSize:(0,T.zT)(t,n)}},createDefaultState=function(e){var t=e.children,n=e.defaultShowTooltip,o=(0,w.sP)(t,M),i=0,a=0;return e.data&&0!==e.data.length&&(a=e.data.length-1),o&&o.props&&(o.props.startIndex>=0&&(i=o.props.startIndex),o.props.endIndex>=0&&(a=o.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:i,dataEndIndex:a,activeTooltipIndex:-1,isTooltipActive:!!n}},getAxisNameByLayout=function(e){return"horizontal"===e?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===e?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===e?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},calculateOffset=function(e,t){var n=e.props,o=e.graphicalItems,i=e.xAxisMap,a=void 0===i?{}:i,c=e.yAxisMap,u=void 0===c?{}:c,s=n.width,l=n.height,f=n.children,d=n.margin||{},y=(0,w.sP)(f,M),h=(0,w.sP)(f,P.D),g=Object.keys(u).reduce(function(e,t){var n=u[t],o=n.orientation;return n.mirror||n.hide?e:generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},e),{},generateCategoricalChart_defineProperty({},o,e[o]+n.width))},{left:d.left||0,right:d.right||0}),b=Object.keys(a).reduce(function(e,t){var n=a[t],o=n.orientation;return n.mirror||n.hide?e:generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},e),{},generateCategoricalChart_defineProperty({},o,p()(e,"".concat(o))+n.height))},{top:d.top||0,bottom:d.bottom||0}),m=generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},b),g),_=m.bottom;y&&(m.bottom+=y.props.height||M.defaultProps.height),h&&t&&(m=(0,T.By)(m,o,n,t));var x=s-m.left-m.right,S=l-m.top-m.bottom;return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({brushBottom:_},m),{},{width:Math.max(x,0),height:Math.max(S,0)})},generateCategoricalChart=function(e){var t=e.chartName,n=e.GraphicalChild,i=e.defaultTooltipEventType,c=void 0===i?"axis":i,s=e.validateTooltipEventTypes,l=void 0===s?["axis"]:s,f=e.axisComponents,d=e.legendContent,y=e.formatAxisMap,h=e.defaultProps,getFormatItems=function(e,t){var n=t.graphicalItems,o=t.stackGroups,i=t.offset,c=t.updateId,u=t.dataStartIndex,s=t.dataEndIndex,l=e.barSize,p=e.layout,d=e.barGap,y=e.barCategoryGap,h=e.maxBarSize,g=getAxisNameByLayout(p),b=g.numericAxisName,_=g.cateAxisName,x=!!n&&!!n.length&&n.some(function(e){var t=(0,w.Gf)(e&&e.type);return t&&t.indexOf("Bar")>=0}),S=[];return n.forEach(function(n,g){var P=getDisplayedData(e.data,{graphicalItems:[n],dataStartIndex:u,dataEndIndex:s}),O=void 0!==n.type.defaultProps?generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},n.type.defaultProps),n.props):n.props,j=O.dataKey,C=O.maxBarSize,A=O["".concat(b,"Id")],k=O["".concat(_,"Id")],E=f.reduce(function(e,n){var o=t["".concat(n.axisType,"Map")],i=O["".concat(n.axisType,"Id")];o&&o[i]||"zAxis"===n.axisType||(0,m.Z)(!1);var a=o[i];return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},e),{},generateCategoricalChart_defineProperty(generateCategoricalChart_defineProperty({},n.axisType,a),"".concat(n.axisType,"Ticks"),(0,T.uY)(a)))},{}),M=E[_],I=E["".concat(_,"Ticks")],D=o&&o[A]&&o[A].hasStack&&(0,T.O3)(n,o[A].stackGroups),R=(0,w.Gf)(n.type).indexOf("Bar")>=0,L=(0,T.zT)(M,I),N=[],B=x&&(0,T.pt)({barSize:l,stackGroups:o,totalSize:"xAxis"===_?E[_].width:"yAxis"===_?E[_].height:void 0});if(R){var W,U,z=a()(C)?h:C,K=null!==(W=null!==(U=(0,T.zT)(M,I,!0))&&void 0!==U?U:z)&&void 0!==W?W:0;N=(0,T.qz)({barGap:d,barCategoryGap:y,bandSize:K!==L?K:L,sizeList:B[k],maxBarSize:z}),K!==L&&(N=N.map(function(e){return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},e),{},{position:generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},e.position),{},{offset:e.position.offset-K/2})})}))}var F=n&&n.type&&n.type.getComposedData;F&&S.push({props:generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},F(generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},E),{},{displayedData:P,props:e,dataKey:j,item:n,bandSize:L,barPosition:N,offset:i,stackedData:D,layout:p,dataStartIndex:u,dataEndIndex:s}))),{},generateCategoricalChart_defineProperty(generateCategoricalChart_defineProperty(generateCategoricalChart_defineProperty({key:n.key||"item-".concat(g)},b,E[b]),_,E[_]),"animationId",c)),childIndex:(0,w.$R)(n,e.children),item:n})}),S},updateStateOfAxisMapsOffsetAndStackGroups=function(e,o){var i=e.props,a=e.dataStartIndex,c=e.dataEndIndex,u=e.updateId;if(!(0,w.TT)({props:i}))return null;var s=i.children,l=i.layout,p=i.stackOffset,d=i.data,h=i.reverseStackOrder,g=getAxisNameByLayout(l),b=g.numericAxisName,m=g.cateAxisName,_=(0,w.NN)(s,n),x=(0,T.wh)(d,_,"".concat(b,"Id"),"".concat(m,"Id"),p,h),S=f.reduce(function(e,t){var n="".concat(t.axisType,"Map");return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},e),{},generateCategoricalChart_defineProperty({},n,getAxisMap(i,generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},t),{},{graphicalItems:_,stackGroups:t.axisType===b&&x,dataStartIndex:a,dataEndIndex:c}))))},{}),P=calculateOffset(generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},S),{},{props:i,graphicalItems:_}),null==o?void 0:o.legendBBox);Object.keys(S).forEach(function(e){S[e]=y(i,S[e],P,e.replace("Map",""),t)});var O=tooltipTicksGenerator(S["".concat(m,"Map")]),j=getFormatItems(i,generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},S),{},{dataStartIndex:a,dataEndIndex:c,updateId:u,graphicalItems:_,stackGroups:x,offset:P}));return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({formattedGraphicalItems:j,graphicalItems:_,offset:P,stackGroups:x},O),S)},P=function(e){function CategoricalChartWrapper(e){var n,i,c;return generateCategoricalChart_classCallCheck(this,CategoricalChartWrapper),generateCategoricalChart_defineProperty(c=generateCategoricalChart_callSuper(this,CategoricalChartWrapper,[e]),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),generateCategoricalChart_defineProperty(c,"accessibilityManager",new X),generateCategoricalChart_defineProperty(c,"handleLegendBBoxUpdate",function(e){if(e){var t=c.state,n=t.dataStartIndex,o=t.dataEndIndex,i=t.updateId;c.setState(generateCategoricalChart_objectSpread({legendBBox:e},updateStateOfAxisMapsOffsetAndStackGroups({props:c.props,dataStartIndex:n,dataEndIndex:o,updateId:i},generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},c.state),{},{legendBBox:e}))))}}),generateCategoricalChart_defineProperty(c,"handleReceiveSyncEvent",function(e,t,n){c.props.syncId===e&&(n!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(t)}),generateCategoricalChart_defineProperty(c,"handleBrushChange",function(e){var t=e.startIndex,n=e.endIndex;if(t!==c.state.dataStartIndex||n!==c.state.dataEndIndex){var o=c.state.updateId;c.setState(function(){return generateCategoricalChart_objectSpread({dataStartIndex:t,dataEndIndex:n},updateStateOfAxisMapsOffsetAndStackGroups({props:c.props,dataStartIndex:t,dataEndIndex:n,updateId:o},c.state))}),c.triggerSyncEvent({dataStartIndex:t,dataEndIndex:n})}}),generateCategoricalChart_defineProperty(c,"handleMouseEnter",function(e){var t=c.getMouseInfo(e);if(t){var n=generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},t),{},{isTooltipActive:!0});c.setState(n),c.triggerSyncEvent(n);var o=c.props.onMouseEnter;u()(o)&&o(n,e)}}),generateCategoricalChart_defineProperty(c,"triggeredAfterMouseMove",function(e){var t=c.getMouseInfo(e),n=t?generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},t),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(n),c.triggerSyncEvent(n);var o=c.props.onMouseMove;u()(o)&&o(n,e)}),generateCategoricalChart_defineProperty(c,"handleItemMouseEnter",function(e){c.setState(function(){return{isTooltipActive:!0,activeItem:e,activePayload:e.tooltipPayload,activeCoordinate:e.tooltipPosition||{x:e.cx,y:e.cy}}})}),generateCategoricalChart_defineProperty(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),generateCategoricalChart_defineProperty(c,"handleMouseMove",function(e){e.persist(),c.throttleTriggeredAfterMouseMove(e)}),generateCategoricalChart_defineProperty(c,"handleMouseLeave",function(e){c.throttleTriggeredAfterMouseMove.cancel();var t={isTooltipActive:!1};c.setState(t),c.triggerSyncEvent(t);var n=c.props.onMouseLeave;u()(n)&&n(t,e)}),generateCategoricalChart_defineProperty(c,"handleOuterEvent",function(e){var t,n=(0,w.Bh)(e),o=p()(c.props,"".concat(n));n&&u()(o)&&o(null!==(t=/.*touch.*/i.test(n)?c.getMouseInfo(e.changedTouches[0]):c.getMouseInfo(e))&&void 0!==t?t:{},e)}),generateCategoricalChart_defineProperty(c,"handleClick",function(e){var t=c.getMouseInfo(e);if(t){var n=generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},t),{},{isTooltipActive:!0});c.setState(n),c.triggerSyncEvent(n);var o=c.props.onClick;u()(o)&&o(n,e)}}),generateCategoricalChart_defineProperty(c,"handleMouseDown",function(e){var t=c.props.onMouseDown;u()(t)&&t(c.getMouseInfo(e),e)}),generateCategoricalChart_defineProperty(c,"handleMouseUp",function(e){var t=c.props.onMouseUp;u()(t)&&t(c.getMouseInfo(e),e)}),generateCategoricalChart_defineProperty(c,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(e.changedTouches[0])}),generateCategoricalChart_defineProperty(c,"handleTouchStart",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&c.handleMouseDown(e.changedTouches[0])}),generateCategoricalChart_defineProperty(c,"handleTouchEnd",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&c.handleMouseUp(e.changedTouches[0])}),generateCategoricalChart_defineProperty(c,"handleDoubleClick",function(e){var t=c.props.onDoubleClick;u()(t)&&t(c.getMouseInfo(e),e)}),generateCategoricalChart_defineProperty(c,"handleContextMenu",function(e){var t=c.props.onContextMenu;u()(t)&&t(c.getMouseInfo(e),e)}),generateCategoricalChart_defineProperty(c,"triggerSyncEvent",function(e){void 0!==c.props.syncId&&V.emit(Y,c.props.syncId,e,c.eventEmitterSymbol)}),generateCategoricalChart_defineProperty(c,"applySyncEvent",function(e){var t=c.props,n=t.layout,o=t.syncMethod,i=c.state.updateId,a=e.dataStartIndex,u=e.dataEndIndex;if(void 0!==e.dataStartIndex||void 0!==e.dataEndIndex)c.setState(generateCategoricalChart_objectSpread({dataStartIndex:a,dataEndIndex:u},updateStateOfAxisMapsOffsetAndStackGroups({props:c.props,dataStartIndex:a,dataEndIndex:u,updateId:i},c.state)));else if(void 0!==e.activeTooltipIndex){var s=e.chartX,l=e.chartY,f=e.activeTooltipIndex,p=c.state,d=p.offset,y=p.tooltipTicks;if(!d)return;if("function"==typeof o)f=o(y,e);else if("value"===o){f=-1;for(var h=0;h<y.length;h++)if(y[h].value===e.activeLabel){f=h;break}}var g=generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},d),{},{x:d.left,y:d.top}),b=Math.min(s,g.x+g.width),m=Math.min(l,g.y+g.height),_=y[f]&&y[f].value,x=getTooltipContent(c.state,c.props.data,f),S=y[f]?{x:"horizontal"===n?y[f].coordinate:b,y:"horizontal"===n?m:y[f].coordinate}:ea;c.setState(generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},e),{},{activeLabel:_,activeCoordinate:S,activePayload:x,activeTooltipIndex:f}))}else c.setState(e)}),generateCategoricalChart_defineProperty(c,"renderCursor",function(e){var n,i=c.state,a=i.isTooltipActive,u=i.activeCoordinate,s=i.activePayload,l=i.offset,f=i.activeTooltipIndex,p=i.tooltipAxisBandSize,d=c.getTooltipEventType(),y=null!==(n=e.props.active)&&void 0!==n?n:a,h=c.props.layout,g=e.key||"_recharts-cursor";return o.createElement(Cursor,{key:g,activeCoordinate:u,activePayload:s,activeTooltipIndex:f,chartName:t,element:e,isActive:y,layout:h,offset:l,tooltipAxisBandSize:p,tooltipEventType:d})}),generateCategoricalChart_defineProperty(c,"renderPolarAxis",function(e,t,n){var i=p()(e,"type.axisType"),a=p()(c.state,"".concat(i,"Map")),u=e.type.defaultProps,s=void 0!==u?generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},u),e.props):e.props,l=a&&a[s["".concat(i,"Id")]];return(0,o.cloneElement)(e,generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},l),{},{className:(0,b.Z)(i,l.className),key:e.key||"".concat(t,"-").concat(n),ticks:(0,T.uY)(l,!0)}))}),generateCategoricalChart_defineProperty(c,"renderPolarGrid",function(e){var t=e.props,n=t.radialLines,i=t.polarAngles,a=t.polarRadius,u=c.state,s=u.radiusAxisMap,l=u.angleAxisMap,f=(0,k.Kt)(s),p=(0,k.Kt)(l),d=p.cx,y=p.cy,h=p.innerRadius,g=p.outerRadius;return(0,o.cloneElement)(e,{polarAngles:Array.isArray(i)?i:(0,T.uY)(p,!0).map(function(e){return e.coordinate}),polarRadius:Array.isArray(a)?a:(0,T.uY)(f,!0).map(function(e){return e.coordinate}),cx:d,cy:y,innerRadius:h,outerRadius:g,key:e.key||"polar-grid",radialLines:n})}),generateCategoricalChart_defineProperty(c,"renderLegend",function(){var e=c.state.formattedGraphicalItems,t=c.props,n=t.children,i=t.width,a=t.height,u=c.props.margin||{},s=i-(u.left||0)-(u.right||0),l=(0,D.z)({children:n,formattedGraphicalItems:e,legendWidth:s,legendContent:d});if(!l)return null;var f=l.item,p=generateCategoricalChart_objectWithoutProperties(l,er);return(0,o.cloneElement)(f,generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},p),{},{chartWidth:i,chartHeight:a,margin:u,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),generateCategoricalChart_defineProperty(c,"renderTooltip",function(){var e,t=c.props,n=t.children,i=t.accessibilityLayer,a=(0,w.sP)(n,S.u);if(!a)return null;var u=c.state,s=u.isTooltipActive,l=u.activeCoordinate,f=u.activePayload,p=u.activeLabel,d=u.offset,y=null!==(e=a.props.active)&&void 0!==e?e:s;return(0,o.cloneElement)(a,{viewBox:generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},d),{},{x:d.left,y:d.top}),active:y,label:p,payload:y?f:[],coordinate:l,accessibilityLayer:i})}),generateCategoricalChart_defineProperty(c,"renderBrush",function(e){var t=c.props,n=t.margin,i=t.data,a=c.state,u=a.offset,s=a.dataStartIndex,l=a.dataEndIndex,f=a.updateId;return(0,o.cloneElement)(e,{key:e.key||"_recharts-brush",onChange:(0,T.DO)(c.handleBrushChange,e.props.onChange),data:i,x:(0,k.hj)(e.props.x)?e.props.x:u.left,y:(0,k.hj)(e.props.y)?e.props.y:u.top+u.height+u.brushBottom-(n.bottom||0),width:(0,k.hj)(e.props.width)?e.props.width:u.width,startIndex:s,endIndex:l,updateId:"brush-".concat(f)})}),generateCategoricalChart_defineProperty(c,"renderReferenceElement",function(e,t,n){if(!e)return null;var i=c.clipPathId,a=c.state,u=a.xAxisMap,s=a.yAxisMap,l=a.offset,f=e.type.defaultProps||{},p=e.props,d=p.xAxisId,y=void 0===d?f.xAxisId:d,h=p.yAxisId,g=void 0===h?f.yAxisId:h;return(0,o.cloneElement)(e,{key:e.key||"".concat(t,"-").concat(n),xAxis:u[y],yAxis:s[g],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:i})}),generateCategoricalChart_defineProperty(c,"renderActivePoints",function(e){var t=e.item,n=e.activePoint,o=e.basePoint,i=e.childIndex,a=e.isRange,c=[],u=t.props.key,s=void 0!==t.item.type.defaultProps?generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},t.item.type.defaultProps),t.item.props):t.item.props,l=s.activeDot,f=generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({index:i,dataKey:s.dataKey,cx:n.x,cy:n.y,r:4,fill:(0,T.fk)(t.item),strokeWidth:2,stroke:"#fff",payload:n.payload,value:n.value},(0,w.L6)(l,!1)),(0,G.Ym)(l));return c.push(CategoricalChartWrapper.renderActiveDot(l,f,"".concat(u,"-activePoint-").concat(i))),o?c.push(CategoricalChartWrapper.renderActiveDot(l,generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},f),{},{cx:o.x,cy:o.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),generateCategoricalChart_defineProperty(c,"renderGraphicChild",function(e,t,n){var i=c.filterFormatItem(e,t,n);if(!i)return null;var u=c.getTooltipEventType(),s=c.state,l=s.isTooltipActive,f=s.tooltipAxis,p=s.activeTooltipIndex,d=s.activeLabel,y=c.props.children,h=(0,w.sP)(y,S.u),g=i.props,b=g.points,m=g.isRange,_=g.baseLine,x=void 0!==i.item.type.defaultProps?generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},i.item.type.defaultProps),i.item.props):i.item.props,P=x.activeDot,O=x.hide,j=x.activeBar,C=x.activeShape,A={};"axis"!==u&&h&&"click"===h.props.trigger?A={onClick:(0,T.DO)(c.handleItemMouseEnter,e.props.onClick)}:"axis"!==u&&(A={onMouseLeave:(0,T.DO)(c.handleItemMouseLeave,e.props.onMouseLeave),onMouseEnter:(0,T.DO)(c.handleItemMouseEnter,e.props.onMouseEnter)});var E=(0,o.cloneElement)(e,generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},i.props),A));function findWithPayload(e){return"function"==typeof f.dataKey?f.dataKey(e.payload):null}if(!O&&l&&h&&(P||j||C)){if(p>=0){if(f.dataKey&&!f.allowDuplicatedCategory){var M="function"==typeof f.dataKey?findWithPayload:"payload.".concat(f.dataKey.toString());D=(0,k.Ap)(b,M,d),R=m&&_&&(0,k.Ap)(_,M,d)}else D=null==b?void 0:b[p],R=m&&_&&_[p];if(C||j){var I=void 0!==e.props.activeIndex?e.props.activeIndex:p;return[(0,o.cloneElement)(e,generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},i.props),A),{},{activeIndex:I})),null,null]}if(!a()(D))return[E].concat(generateCategoricalChart_toConsumableArray(c.renderActivePoints({item:i,activePoint:D,basePoint:R,childIndex:p,isRange:m})))}else{var D,R,L,N=(null!==(L=c.getItemByXY(c.state.activeCoordinate))&&void 0!==L?L:{graphicalItem:E}).graphicalItem,B=N.item,W=void 0===B?e:B,U=N.childIndex,z=generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},i.props),A),{},{activeIndex:U});return[(0,o.cloneElement)(W,z),null,null]}}return m?[E,null,null]:[E,null]}),generateCategoricalChart_defineProperty(c,"renderCustomized",function(e,t,n){return(0,o.cloneElement)(e,generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({key:"recharts-customized-".concat(n)},c.props),c.state))}),generateCategoricalChart_defineProperty(c,"renderMap",{CartesianGrid:{handler:renderAsIs,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:renderAsIs},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:renderAsIs},YAxis:{handler:renderAsIs},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!==(n=e.id)&&void 0!==n?n:(0,k.EL)("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=g()(c.triggeredAfterMouseMove,null!==(i=e.throttleDelay)&&void 0!==i?i:1e3/60),c.state={},c}return generateCategoricalChart_inherits(CategoricalChartWrapper,e),generateCategoricalChart_createClass(CategoricalChartWrapper,[{key:"componentDidMount",value:function(){var e,t;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(e=this.props.margin.left)&&void 0!==e?e:0,top:null!==(t=this.props.margin.top)&&void 0!==t?t:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var e=this.props,t=e.children,n=e.data,o=e.height,i=e.layout,a=(0,w.sP)(t,S.u);if(a){var c=a.props.defaultIndex;if("number"==typeof c&&!(c<0)&&!(c>this.state.tooltipTicks.length-1)){var u=this.state.tooltipTicks[c]&&this.state.tooltipTicks[c].value,s=getTooltipContent(this.state,n,c,u),l=this.state.tooltipTicks[c].coordinate,f=(this.state.offset.top+o)/2,p="horizontal"===i?{x:l,y:f}:{y:l,x:f},d=this.state.formattedGraphicalItems.find(function(e){return"Scatter"===e.item.type.name});d&&(p=generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},p),d.props.points[c].tooltipPosition),s=d.props.points[c].tooltipPayload);var y={activeTooltipIndex:c,isTooltipActive:!0,activeLabel:u,activePayload:s,activeCoordinate:p};this.setState(y),this.renderCursor(a),this.accessibilityManager.setIndex(c)}}}},{key:"getSnapshotBeforeUpdate",value:function(e,t){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==t.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==e.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==e.margin){var n,o;this.accessibilityManager.setDetails({offset:{left:null!==(n=this.props.margin.left)&&void 0!==n?n:0,top:null!==(o=this.props.margin.top)&&void 0!==o?o:0}})}return null}},{key:"componentDidUpdate",value:function(e){(0,w.rL)([(0,w.sP)(e.children,S.u)],[(0,w.sP)(this.props.children,S.u)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var e=(0,w.sP)(this.props.children,S.u);if(e&&"boolean"==typeof e.props.shared){var t=e.props.shared?"axis":"item";return l.indexOf(t)>=0?t:c}return c}},{key:"getMouseInfo",value:function(e){if(!this.container)return null;var t=this.container,n=t.getBoundingClientRect(),o=(0,I.os)(n),i={chartX:Math.round(e.pageX-o.left),chartY:Math.round(e.pageY-o.top)},a=n.width/t.offsetWidth||1,c=this.inRange(i.chartX,i.chartY,a);if(!c)return null;var u=this.state,s=u.xAxisMap,l=u.yAxisMap,f=this.getTooltipEventType(),p=getTooltipData(this.state,this.props.data,this.props.layout,c);if("axis"!==f&&s&&l){var d=(0,k.Kt)(s).scale,y=(0,k.Kt)(l).scale,h=d&&d.invert?d.invert(i.chartX):null,g=y&&y.invert?y.invert(i.chartY):null;return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},i),{},{xValue:h,yValue:g},p)}return p?generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},i),p):null}},{key:"inRange",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,o=this.props.layout,i=e/n,a=t/n;if("horizontal"===o||"vertical"===o){var c=this.state.offset;return i>=c.left&&i<=c.left+c.width&&a>=c.top&&a<=c.top+c.height?{x:i,y:a}:null}var u=this.state,s=u.angleAxisMap,l=u.radiusAxisMap;if(s&&l){var f=(0,k.Kt)(s);return(0,H.z3)({x:i,y:a},f)}return null}},{key:"parseEventsOfWrapper",value:function(){var e=this.props.children,t=this.getTooltipEventType(),n=(0,w.sP)(e,S.u),o={};return n&&"axis"===t&&(o="click"===n.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},(0,G.Ym)(this.props,this.handleOuterEvent)),o)}},{key:"addListener",value:function(){V.on(Y,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){V.removeListener(Y,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(e,t,n){for(var o=this.state.formattedGraphicalItems,i=0,a=o.length;i<a;i++){var c=o[i];if(c.item===e||c.props.key===e.key||t===(0,w.Gf)(c.item.type)&&n===c.childIndex)return c}return null}},{key:"renderClipPath",value:function(){var e=this.clipPathId,t=this.state.offset,n=t.left,i=t.top,a=t.height,c=t.width;return o.createElement("defs",null,o.createElement("clipPath",{id:e},o.createElement("rect",{x:n,y:i,height:a,width:c})))}},{key:"getXScales",value:function(){var e=this.state.xAxisMap;return e?Object.entries(e).reduce(function(e,t){var n=generateCategoricalChart_slicedToArray(t,2),o=n[0],i=n[1];return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},e),{},generateCategoricalChart_defineProperty({},o,i.scale))},{}):null}},{key:"getYScales",value:function(){var e=this.state.yAxisMap;return e?Object.entries(e).reduce(function(e,t){var n=generateCategoricalChart_slicedToArray(t,2),o=n[0],i=n[1];return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},e),{},generateCategoricalChart_defineProperty({},o,i.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(e){var t;return null===(t=this.state.xAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getYScaleByAxisId",value:function(e){var t;return null===(t=this.state.yAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getItemByXY",value:function(e){var t=this.state,n=t.formattedGraphicalItems,o=t.activeItem;if(n&&n.length)for(var i=0,a=n.length;i<a;i++){var c=n[i],u=c.props,s=c.item,l=void 0!==s.type.defaultProps?generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},s.type.defaultProps),s.props):s.props,f=(0,w.Gf)(s.type);if("Bar"===f){var p=(u.data||[]).find(function(t){return(0,j.X)(e,t)});if(p)return{graphicalItem:c,payload:p}}else if("RadialBar"===f){var d=(u.data||[]).find(function(t){return(0,H.z3)(e,t)});if(d)return{graphicalItem:c,payload:d}}else if((0,Q.lT)(c,o)||(0,Q.V$)(c,o)||(0,Q.w7)(c,o)){var y=(0,Q.a3)({graphicalItem:c,activeTooltipItem:o,itemData:l.data}),h=void 0===l.activeIndex?y:l.activeIndex;return{graphicalItem:generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},c),{},{childIndex:h}),payload:(0,Q.w7)(c,o)?l.data[y]:c.props.data[y]}}}return null}},{key:"render",value:function(){var e,t,n=this;if(!(0,w.TT)(this))return null;var i=this.props,a=i.children,c=i.className,u=i.width,s=i.height,l=i.style,f=i.compact,p=i.title,d=i.desc,y=generateCategoricalChart_objectWithoutProperties(i,en),h=(0,w.L6)(y,!1);if(f)return o.createElement(z.br,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},o.createElement(_.T,generateCategoricalChart_extends({},h,{width:u,height:s,title:p,desc:d}),this.renderClipPath(),(0,w.eu)(a,this.renderMap)));this.props.accessibilityLayer&&(h.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,h.role=null!==(t=this.props.role)&&void 0!==t?t:"application",h.onKeyDown=function(e){n.accessibilityManager.keyboardEvent(e)},h.onFocus=function(){n.accessibilityManager.focus()});var g=this.parseEventsOfWrapper();return o.createElement(z.br,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},o.createElement("div",generateCategoricalChart_extends({className:(0,b.Z)("recharts-wrapper",c),style:generateCategoricalChart_objectSpread({position:"relative",cursor:"default",width:u,height:s},l)},g,{ref:function(e){n.container=e}}),o.createElement(_.T,generateCategoricalChart_extends({},h,{width:u,height:s,title:p,desc:d,style:ei}),this.renderClipPath(),(0,w.eu)(a,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(o.Component);generateCategoricalChart_defineProperty(P,"displayName",t),generateCategoricalChart_defineProperty(P,"defaultProps",generateCategoricalChart_objectSpread({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},h)),generateCategoricalChart_defineProperty(P,"getDerivedStateFromProps",function(e,t){var n=e.dataKey,o=e.data,i=e.children,c=e.width,u=e.height,s=e.layout,l=e.stackOffset,f=e.margin,p=t.dataStartIndex,d=t.dataEndIndex;if(void 0===t.updateId){var y=createDefaultState(e);return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},y),{},{updateId:0},updateStateOfAxisMapsOffsetAndStackGroups(generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({props:e},y),{},{updateId:0}),t)),{},{prevDataKey:n,prevData:o,prevWidth:c,prevHeight:u,prevLayout:s,prevStackOffset:l,prevMargin:f,prevChildren:i})}if(n!==t.prevDataKey||o!==t.prevData||c!==t.prevWidth||u!==t.prevHeight||s!==t.prevLayout||l!==t.prevStackOffset||!(0,Z.w)(f,t.prevMargin)){var h=createDefaultState(e),g={chartX:t.chartX,chartY:t.chartY,isTooltipActive:t.isTooltipActive},b=generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},getTooltipData(t,o,s)),{},{updateId:t.updateId+1}),m=generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},h),g),b);return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({},m),updateStateOfAxisMapsOffsetAndStackGroups(generateCategoricalChart_objectSpread({props:e},m),t)),{},{prevDataKey:n,prevData:o,prevWidth:c,prevHeight:u,prevLayout:s,prevStackOffset:l,prevMargin:f,prevChildren:i})}if(!(0,w.rL)(i,t.prevChildren)){var _,x,S,P,O=(0,w.sP)(i,M),j=O&&null!==(_=null===(x=O.props)||void 0===x?void 0:x.startIndex)&&void 0!==_?_:p,C=O&&null!==(S=null===(P=O.props)||void 0===P?void 0:P.endIndex)&&void 0!==S?S:d,A=a()(o)||j!==p||C!==d?t.updateId+1:t.updateId;return generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({updateId:A},updateStateOfAxisMapsOffsetAndStackGroups(generateCategoricalChart_objectSpread(generateCategoricalChart_objectSpread({props:e},t),{},{updateId:A,dataStartIndex:j,dataEndIndex:C}),t)),{},{prevChildren:i,dataStartIndex:j,dataEndIndex:C})}return null}),generateCategoricalChart_defineProperty(P,"renderActiveDot",function(e,t,n){var i;return i=(0,o.isValidElement)(e)?(0,o.cloneElement)(e,t):u()(e)?e(t):o.createElement(O.o,t),o.createElement(x.m,{className:"recharts-active-dot",key:n},i)});var C=(0,o.forwardRef)(function(e,t){return o.createElement(P,generateCategoricalChart_extends({},e,{ref:t}))});return C.displayName=P.displayName,C}},6612:function(e,t,n){"use strict";n.d(t,{b:function(){return Cell}});var Cell=function(e){return null};Cell.displayName="Cell"},3343:function(e,t,n){"use strict";n.d(t,{_:function(){return Label}});var o=n(2265),i=n(2727),a=n.n(i),c=n(8293),u=n.n(c),s=n(6905),l=n.n(s),f=n(7042),p=n(1224),d=n(3843),y=n(7281),h=n(6120);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var g=["offset"];function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}var getLabel=function(e){var t=e.value,n=e.formatter,o=a()(e.children)?t:e.children;return u()(n)?n(o):o},renderRadialLabel=function(e,t,n){var i,c,u=e.position,s=e.viewBox,l=e.offset,p=e.className,d=s.cx,g=s.cy,b=s.innerRadius,m=s.outerRadius,_=s.startAngle,x=s.endAngle,S=s.clockWise,P=(b+m)/2,O=(0,y.uY)(x-_)*Math.min(Math.abs(x-_),360),j=O>=0?1:-1;"insideStart"===u?(i=_+j*l,c=S):"insideEnd"===u?(i=x-j*l,c=!S):"end"===u&&(i=x+j*l,c=S),c=O<=0?c:!c;var w=(0,h.op)(d,g,P,i),C=(0,h.op)(d,g,P,i+(c?1:-1)*359),A="M".concat(w.x,",").concat(w.y,"\n    A").concat(P,",").concat(P,",0,1,").concat(c?0:1,",\n    ").concat(C.x,",").concat(C.y),T=a()(e.id)?(0,y.EL)("recharts-radial-line-"):e.id;return o.createElement("text",_extends({},n,{dominantBaseline:"central",className:(0,f.Z)("recharts-radial-bar-label",p)}),o.createElement("defs",null,o.createElement("path",{id:T,d:A})),o.createElement("textPath",{xlinkHref:"#".concat(T)},t))},getAttrsOfPolarLabel=function(e){var t=e.viewBox,n=e.offset,o=e.position,i=t.cx,a=t.cy,c=t.innerRadius,u=t.outerRadius,s=(t.startAngle+t.endAngle)/2;if("outside"===o){var l=(0,h.op)(i,a,u+n,s),f=l.x;return{x:f,y:l.y,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===o)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===o)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===o)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var p=(c+u)/2,d=(0,h.op)(i,a,p,s);return{x:d.x,y:d.y,textAnchor:"middle",verticalAnchor:"middle"}},getAttrsOfCartesianLabel=function(e){var t=e.viewBox,n=e.parentViewBox,o=e.offset,i=e.position,a=t.x,c=t.y,u=t.width,s=t.height,f=s>=0?1:-1,p=f*o,d=f>0?"end":"start",h=f>0?"start":"end",g=u>=0?1:-1,b=g*o,m=g>0?"end":"start",_=g>0?"start":"end";if("top"===i)return _objectSpread(_objectSpread({},{x:a+u/2,y:c-f*o,textAnchor:"middle",verticalAnchor:d}),n?{height:Math.max(c-n.y,0),width:u}:{});if("bottom"===i)return _objectSpread(_objectSpread({},{x:a+u/2,y:c+s+p,textAnchor:"middle",verticalAnchor:h}),n?{height:Math.max(n.y+n.height-(c+s),0),width:u}:{});if("left"===i){var x={x:a-b,y:c+s/2,textAnchor:m,verticalAnchor:"middle"};return _objectSpread(_objectSpread({},x),n?{width:Math.max(x.x-n.x,0),height:s}:{})}if("right"===i){var S={x:a+u+b,y:c+s/2,textAnchor:_,verticalAnchor:"middle"};return _objectSpread(_objectSpread({},S),n?{width:Math.max(n.x+n.width-S.x,0),height:s}:{})}var P=n?{width:u,height:s}:{};return"insideLeft"===i?_objectSpread({x:a+b,y:c+s/2,textAnchor:_,verticalAnchor:"middle"},P):"insideRight"===i?_objectSpread({x:a+u-b,y:c+s/2,textAnchor:m,verticalAnchor:"middle"},P):"insideTop"===i?_objectSpread({x:a+u/2,y:c+p,textAnchor:"middle",verticalAnchor:h},P):"insideBottom"===i?_objectSpread({x:a+u/2,y:c+s-p,textAnchor:"middle",verticalAnchor:d},P):"insideTopLeft"===i?_objectSpread({x:a+b,y:c+p,textAnchor:_,verticalAnchor:h},P):"insideTopRight"===i?_objectSpread({x:a+u-b,y:c+p,textAnchor:m,verticalAnchor:h},P):"insideBottomLeft"===i?_objectSpread({x:a+b,y:c+s-p,textAnchor:_,verticalAnchor:d},P):"insideBottomRight"===i?_objectSpread({x:a+u-b,y:c+s-p,textAnchor:m,verticalAnchor:d},P):l()(i)&&((0,y.hj)(i.x)||(0,y.hU)(i.x))&&((0,y.hj)(i.y)||(0,y.hU)(i.y))?_objectSpread({x:a+(0,y.h1)(i.x,u),y:c+(0,y.h1)(i.y,s),textAnchor:"end",verticalAnchor:"end"},P):_objectSpread({x:a+u/2,y:c+s/2,textAnchor:"middle",verticalAnchor:"middle"},P)};function Label(e){var t,n=e.offset,i=_objectSpread({offset:void 0===n?5:n},_objectWithoutProperties(e,g)),c=i.viewBox,s=i.position,l=i.value,h=i.children,b=i.content,m=i.className,_=void 0===m?"":m,x=i.textBreakAll;if(!c||a()(l)&&a()(h)&&!(0,o.isValidElement)(b)&&!u()(b))return null;if((0,o.isValidElement)(b))return(0,o.cloneElement)(b,i);if(u()(b)){if(t=(0,o.createElement)(b,i),(0,o.isValidElement)(t))return t}else t=getLabel(i);var S="cx"in c&&(0,y.hj)(c.cx),P=(0,d.L6)(i,!0);if(S&&("insideStart"===s||"insideEnd"===s||"end"===s))return renderRadialLabel(i,t,P);var O=S?getAttrsOfPolarLabel(i):getAttrsOfCartesianLabel(i);return o.createElement(p.x,_extends({className:(0,f.Z)("recharts-label",_)},P,O,{breakAll:x}),t)}Label.displayName="Label";var parseViewBox=function(e){var t=e.cx,n=e.cy,o=e.angle,i=e.startAngle,a=e.endAngle,c=e.r,u=e.radius,s=e.innerRadius,l=e.outerRadius,f=e.x,p=e.y,d=e.top,h=e.left,g=e.width,b=e.height,m=e.clockWise,_=e.labelViewBox;if(_)return _;if((0,y.hj)(g)&&(0,y.hj)(b)){if((0,y.hj)(f)&&(0,y.hj)(p))return{x:f,y:p,width:g,height:b};if((0,y.hj)(d)&&(0,y.hj)(h))return{x:d,y:h,width:g,height:b}}return(0,y.hj)(f)&&(0,y.hj)(p)?{x:f,y:p,width:0,height:0}:(0,y.hj)(t)&&(0,y.hj)(n)?{cx:t,cy:n,startAngle:i||o||0,endAngle:a||o||0,innerRadius:s||0,outerRadius:l||u||c||0,clockWise:m}:e.viewBox?e.viewBox:{}};Label.parseViewBox=parseViewBox,Label.renderCallByParent=function(e,t){var n,i,a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&a&&!e.label)return null;var c=e.children,s=parseViewBox(e),f=(0,d.NN)(c,Label).map(function(e,n){return(0,o.cloneElement)(e,{viewBox:t||s,key:"label-".concat(n)})});return a?[(n=e.label,i=t||s,n?!0===n?o.createElement(Label,{key:"label-implicit",viewBox:i}):(0,y.P2)(n)?o.createElement(Label,{key:"label-implicit",viewBox:i,value:n}):(0,o.isValidElement)(n)?n.type===Label?(0,o.cloneElement)(n,{key:"label-implicit",viewBox:i}):o.createElement(Label,{key:"label-implicit",content:n,viewBox:i}):u()(n)?o.createElement(Label,{key:"label-implicit",content:n,viewBox:i}):l()(n)?o.createElement(Label,_extends({viewBox:i},n,{key:"label-implicit"})):null:null)].concat(_toConsumableArray(f)):f}},561:function(e,t,n){"use strict";n.d(t,{e:function(){return LabelList}});var o=n(2265),i=n(2727),a=n.n(i),c=n(6905),u=n.n(c),s=n(8293),l=n.n(s),f=n(4388),p=n.n(f),d=n(3343),y=n(8357),h=n(3843),g=n(3249);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var b=["valueAccessor"],m=["data","dataKey","clockWise","id","textBreakAll"];function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}var defaultAccessor=function(e){return Array.isArray(e.value)?p()(e.value):e.value};function LabelList(e){var t=e.valueAccessor,n=void 0===t?defaultAccessor:t,i=_objectWithoutProperties(e,b),c=i.data,u=i.dataKey,s=i.clockWise,l=i.id,f=i.textBreakAll,p=_objectWithoutProperties(i,m);return c&&c.length?o.createElement(y.m,{className:"recharts-label-list"},c.map(function(e,t){var i=a()(u)?n(e,t):(0,g.F$)(e&&e.payload,u),c=a()(l)?{}:{id:"".concat(l,"-").concat(t)};return o.createElement(d._,_extends({},(0,h.L6)(e,!0),p,c,{parentViewBox:e.parentViewBox,value:i,textBreakAll:f,viewBox:d._.parseViewBox(a()(s)?e:_objectSpread(_objectSpread({},e),{},{clockWise:s})),key:"label-".concat(t),index:t}))})):null}function parseLabelList(e,t){return e?!0===e?o.createElement(LabelList,{key:"labelList-implicit",data:t}):o.isValidElement(e)||l()(e)?o.createElement(LabelList,{key:"labelList-implicit",data:t,content:e}):u()(e)?o.createElement(LabelList,_extends({data:t},e,{key:"labelList-implicit"})):null:null}function renderCallByParent(e,t){var n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var i=e.children,a=(0,h.NN)(i,LabelList).map(function(e,n){return(0,o.cloneElement)(e,{data:t,key:"labelList-".concat(n)})});return n?[parseLabelList(e.label,t)].concat(_toConsumableArray(a)):a}LabelList.displayName="LabelList",LabelList.renderCallByParent=renderCallByParent},9857:function(e,t,n){"use strict";n.d(t,{D:function(){return g}});var o=n(2265),i=n(8293),a=n.n(i),c=n(7042),u=n(7105),s=n(7434),l=n(2245),f=n(2655);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _callSuper(e,t,n){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],_getPrototypeOf(e).constructor):t.apply(e,n))}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var p=function(e){function DefaultLegendContent(){return _classCallCheck(this,DefaultLegendContent),_callSuper(this,DefaultLegendContent,arguments)}return _inherits(DefaultLegendContent,e),_createClass(DefaultLegendContent,[{key:"renderIcon",value:function(e){var t=this.props.inactiveColor,n=32/6,i=32/3,a=e.inactive?t:e.color;if("plainline"===e.type)return o.createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===e.type)return o.createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(16,"h").concat(i,"\n            A").concat(n,",").concat(n,",0,1,1,").concat(2*i,",").concat(16,"\n            H").concat(32,"M").concat(2*i,",").concat(16,"\n            A").concat(n,",").concat(n,",0,1,1,").concat(i,",").concat(16),className:"recharts-legend-icon"});if("rect"===e.type)return o.createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(o.isValidElement(e.legendIcon)){var c=_objectSpread({},e);return delete c.legendIcon,o.cloneElement(e.legendIcon,c)}return o.createElement(l.v,{fill:a,cx:16,cy:16,size:32,sizeType:"diameter",type:e.type})}},{key:"renderItems",value:function(){var e=this,t=this.props,n=t.payload,i=t.iconSize,l=t.layout,p=t.formatter,d=t.inactiveColor,y={x:0,y:0,width:32,height:32},h={display:"horizontal"===l?"inline-block":"block",marginRight:10},g={display:"inline-block",verticalAlign:"middle",marginRight:4};return n.map(function(t,n){var l=t.formatter||p,b=(0,c.Z)(_defineProperty(_defineProperty({"recharts-legend-item":!0},"legend-item-".concat(n),!0),"inactive",t.inactive));if("none"===t.type)return null;var m=a()(t.value)?null:t.value;(0,u.Z)(!a()(t.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var _=t.inactive?d:t.color;return o.createElement("li",_extends({className:b,style:h,key:"legend-item-".concat(n)},(0,f.bw)(e.props,t,n)),o.createElement(s.T,{width:i,height:i,viewBox:y,style:g},e.renderIcon(t)),o.createElement("span",{className:"recharts-legend-item-text",style:{color:_}},l?l(m,t,n):m))})}},{key:"render",value:function(){var e=this.props,t=e.payload,n=e.layout,i=e.align;return t&&t.length?o.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===n?i:"left"}},this.renderItems()):null}}])}(o.PureComponent);_defineProperty(p,"displayName","Legend"),_defineProperty(p,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var d=n(7281),y=n(200);function Legend_typeof(e){return(Legend_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var h=["ref"];function Legend_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function Legend_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Legend_ownKeys(Object(n),!0).forEach(function(t){Legend_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Legend_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Legend_classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function Legend_defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Legend_toPropertyKey(o.key),o)}}function Legend_createClass(e,t,n){return t&&Legend_defineProperties(e.prototype,t),n&&Legend_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Legend_callSuper(e,t,n){return t=Legend_getPrototypeOf(t),Legend_possibleConstructorReturn(e,Legend_isNativeReflectConstruct()?Reflect.construct(t,n||[],Legend_getPrototypeOf(e).constructor):t.apply(e,n))}function Legend_possibleConstructorReturn(e,t){if(t&&("object"===Legend_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return Legend_assertThisInitialized(e)}function Legend_assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Legend_isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Legend_isNativeReflectConstruct=function(){return!!e})()}function Legend_getPrototypeOf(e){return(Legend_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Legend_inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Legend_setPrototypeOf(e,t)}function Legend_setPrototypeOf(e,t){return(Legend_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Legend_defineProperty(e,t,n){return(t=Legend_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Legend_toPropertyKey(e){var t=Legend_toPrimitive(e,"string");return"symbol"==Legend_typeof(t)?t:t+""}function Legend_toPrimitive(e,t){if("object"!=Legend_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Legend_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function defaultUniqBy(e){return e.value}function renderContent(e,t){if(o.isValidElement(e))return o.cloneElement(e,t);if("function"==typeof e)return o.createElement(e,t);t.ref;var n=_objectWithoutProperties(t,h);return o.createElement(p,n)}var g=function(e){function Legend(){var e;Legend_classCallCheck(this,Legend);for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];return Legend_defineProperty(e=Legend_callSuper(this,Legend,[].concat(n)),"lastBoundingBox",{width:-1,height:-1}),e}return Legend_inherits(Legend,e),Legend_createClass(Legend,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();return e.height=this.wrapperNode.offsetHeight,e.width=this.wrapperNode.offsetWidth,e}return null}},{key:"updateBBox",value:function(){var e=this.props.onBBoxUpdate,t=this.getBBox();t?(Math.abs(t.width-this.lastBoundingBox.width)>1||Math.abs(t.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=t.width,this.lastBoundingBox.height=t.height,e&&e(t)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,e&&e(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?Legend_objectSpread({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(e){var t,n,o=this.props,i=o.layout,a=o.align,c=o.verticalAlign,u=o.margin,s=o.chartWidth,l=o.chartHeight;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(t="center"===a&&"vertical"===i?{left:((s||0)-this.getBBoxSnapshot().width)/2}:"right"===a?{right:u&&u.right||0}:{left:u&&u.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(n="middle"===c?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===c?{bottom:u&&u.bottom||0}:{top:u&&u.top||0}),Legend_objectSpread(Legend_objectSpread({},t),n)}},{key:"render",value:function(){var e=this,t=this.props,n=t.content,i=t.width,a=t.height,c=t.wrapperStyle,u=t.payloadUniqBy,s=t.payload,l=Legend_objectSpread(Legend_objectSpread({position:"absolute",width:i||"auto",height:a||"auto"},this.getDefaultPosition(c)),c);return o.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(t){e.wrapperNode=t}},renderContent(n,Legend_objectSpread(Legend_objectSpread({},this.props),{},{payload:(0,y.z)(s,u,defaultUniqBy)})))}}],[{key:"getWithHeight",value:function(e,t){var n=Legend_objectSpread(Legend_objectSpread({},this.defaultProps),e.props).layout;return"vertical"===n&&(0,d.hj)(e.props.height)?{height:e.props.height}:"horizontal"===n?{width:e.props.width||t}:null}}])}(o.PureComponent);Legend_defineProperty(g,"displayName","Legend"),Legend_defineProperty(g,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},5253:function(e,t,n){"use strict";n.d(t,{h:function(){return f}});var o=n(7042),i=n(2265),a=n(7269),c=n.n(a),u=n(7281),s=n(7105),l=n(3843);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,a,c,u=[],s=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(u.push(o.value),u.length!==t);s=!0);}catch(e){l=!0,i=e}finally{try{if(!s&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(l)throw i}}return u}}function _arrayWithHoles(e){if(Array.isArray(e))return e}var f=(0,i.forwardRef)(function(e,t){var n=e.aspect,a=e.initialDimension,f=void 0===a?{width:-1,height:-1}:a,p=e.width,d=void 0===p?"100%":p,y=e.height,h=void 0===y?"100%":y,g=e.minWidth,b=void 0===g?0:g,m=e.minHeight,_=e.maxHeight,x=e.children,S=e.debounce,P=void 0===S?0:S,O=e.id,j=e.className,w=e.onResize,C=e.style,A=void 0===C?{}:C,T=(0,i.useRef)(null),k=(0,i.useRef)();k.current=w,(0,i.useImperativeHandle)(t,function(){return Object.defineProperty(T.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),T.current},configurable:!0})});var E=_slicedToArray((0,i.useState)({containerWidth:f.width,containerHeight:f.height}),2),M=E[0],I=E[1],D=(0,i.useCallback)(function(e,t){I(function(n){var o=Math.round(e),i=Math.round(t);return n.containerWidth===o&&n.containerHeight===i?n:{containerWidth:o,containerHeight:i}})},[]);(0,i.useEffect)(function(){var callback=function(e){var t,n=e[0].contentRect,o=n.width,i=n.height;D(o,i),null===(t=k.current)||void 0===t||t.call(k,o,i)};P>0&&(callback=c()(callback,P,{trailing:!0,leading:!1}));var e=new ResizeObserver(callback),t=T.current.getBoundingClientRect();return D(t.width,t.height),e.observe(T.current),function(){e.disconnect()}},[D,P]);var R=(0,i.useMemo)(function(){var e=M.containerWidth,t=M.containerHeight;if(e<0||t<0)return null;(0,s.Z)((0,u.hU)(d)||(0,u.hU)(h),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",d,h),(0,s.Z)(!n||n>0,"The aspect(%s) must be greater than zero.",n);var o=(0,u.hU)(d)?e:d,a=(0,u.hU)(h)?t:h;n&&n>0&&(o?a=o/n:a&&(o=a*n),_&&a>_&&(a=_)),(0,s.Z)(o>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",o,a,d,h,b,m,n);var c=!Array.isArray(x)&&(0,l.Gf)(x.type).endsWith("Chart");return i.Children.map(x,function(e){return i.isValidElement(e)?(0,i.cloneElement)(e,_objectSpread({width:o,height:a},c?{style:_objectSpread({height:"100%",width:"100%",maxHeight:a,maxWidth:o},e.props.style)}:{})):e})},[n,x,h,_,m,b,M,d]);return i.createElement("div",{id:O?"".concat(O):void 0,className:(0,o.Z)("recharts-responsive-container",j),style:_objectSpread(_objectSpread({},A),{},{width:d,height:h,minWidth:b,minHeight:m,maxHeight:_}),ref:T},R)})},1224:function(e,t,n){"use strict";n.d(t,{x:function(){return Text}});var o=n(2265),i=n(2727),a=n.n(i),c=n(7042),u=n(7281),s=n(9752),l=n(3843),f=n(4768);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,a,c,u=[],s=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(u.push(o.value),u.length!==t);s=!0);}catch(e){l=!0,i=e}finally{try{if(!s&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(l)throw i}}return u}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var p=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,d=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,y=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,h=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,g={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},b=Object.keys(g);function convertToPx(e,t){return e*g[t]}var m=function(){function DecimalCSS(e,t){_classCallCheck(this,DecimalCSS),this.num=e,this.unit=t,this.num=e,this.unit=t,Number.isNaN(e)&&(this.unit=""),""===t||y.test(t)||(this.num=NaN,this.unit=""),b.includes(t)&&(this.num=convertToPx(e,t),this.unit="px")}return _createClass(DecimalCSS,[{key:"add",value:function(e){return this.unit!==e.unit?new DecimalCSS(NaN,""):new DecimalCSS(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new DecimalCSS(NaN,""):new DecimalCSS(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new DecimalCSS(NaN,""):new DecimalCSS(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new DecimalCSS(NaN,""):new DecimalCSS(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(e){var t,n=_slicedToArray(null!==(t=h.exec(e))&&void 0!==t?t:[],3),o=n[1],i=n[2];return new DecimalCSS(parseFloat(o),null!=i?i:"")}}])}();function calculateArithmetic(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var n,o=_slicedToArray(null!==(n=p.exec(t))&&void 0!==n?n:[],4),i=o[1],a=o[2],c=o[3],u=m.parse(null!=i?i:""),s=m.parse(null!=c?c:""),l="*"===a?u.multiply(s):u.divide(s);if(l.isNaN())return"NaN";t=t.replace(p,l.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var f,y=_slicedToArray(null!==(f=d.exec(t))&&void 0!==f?f:[],4),h=y[1],g=y[2],b=y[3],_=m.parse(null!=h?h:""),x=m.parse(null!=b?b:""),S="+"===g?_.add(x):_.subtract(x);if(S.isNaN())return"NaN";t=t.replace(d,S.toString())}return t}var _=/\(([^()]*)\)/;function calculateParentheses(e){for(var t=e;t.includes("(");){var n=_slicedToArray(_.exec(t),2)[1];t=t.replace(_,calculateArithmetic(n))}return t}function evaluateExpression(e){var t=e.replace(/\s+/g,"");return calculateArithmetic(t=calculateParentheses(t))}function safeEvaluateExpression(e){try{return evaluateExpression(e)}catch(e){return"NaN"}}function reduceCSSCalc(e){var t=safeEvaluateExpression(e.slice(5,-1));return"NaN"===t?"":t}var x=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],S=["dx","dy","angle","className","breakAll"];function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function Text_slicedToArray(e,t){return Text_arrayWithHoles(e)||Text_iterableToArrayLimit(e,t)||Text_unsupportedIterableToArray(e,t)||Text_nonIterableRest()}function Text_nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Text_unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return Text_arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Text_arrayLikeToArray(e,t)}}function Text_arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function Text_iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,a,c,u=[],s=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(u.push(o.value),u.length!==t);s=!0);}catch(e){l=!0,i=e}finally{try{if(!s&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(l)throw i}}return u}}function Text_arrayWithHoles(e){if(Array.isArray(e))return e}var P=/[ \f\n\r\t\v\u2028\u2029]+/,calculateWordWidths=function(e){var t=e.children,n=e.breakAll,o=e.style;try{var i=[];a()(t)||(i=n?t.toString().split(""):t.toString().split(P));var c=i.map(function(e){return{word:e,width:(0,f.xE)(e,o).width}}),u=n?0:(0,f.xE)("\xa0",o).width;return{wordsWithComputedWidth:c,spaceWidth:u}}catch(e){return null}},calculateWordsByLines=function(e,t,n,o,i){var a,c=e.maxLines,s=e.children,l=e.style,f=e.breakAll,p=(0,u.hj)(c),calculate=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce(function(e,t){var a=t.word,c=t.width,u=e[e.length-1];return u&&(null==o||i||u.width+c+n<Number(o))?(u.words.push(a),u.width+=c+n):e.push({words:[a],width:c}),e},[])},d=calculate(t);if(!p)return d;for(var checkOverflow=function(e){var t=calculate(calculateWordWidths({breakAll:f,style:l,children:s.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>c||t.reduce(function(e,t){return e.width>t.width?e:t}).width>Number(o),t]},y=0,h=s.length-1,g=0;y<=h&&g<=s.length-1;){var b=Math.floor((y+h)/2),m=Text_slicedToArray(checkOverflow(b-1),2),_=m[0],x=m[1],S=Text_slicedToArray(checkOverflow(b),1)[0];if(_||S||(y=b+1),_&&S&&(h=b-1),!_&&S){a=x;break}g++}return a||d},getWordsWithoutCalculate=function(e){return[{words:a()(e)?[]:e.toString().split(P)}]},getWordsByLines=function(e){var t=e.width,n=e.scaleToFit,o=e.children,i=e.style,a=e.breakAll,c=e.maxLines;if((t||n)&&!s.x.isSsr){var u=calculateWordWidths({breakAll:a,children:o,style:i});return u?calculateWordsByLines({breakAll:a,children:o,maxLines:c,style:i},u.wordsWithComputedWidth,u.spaceWidth,t,n):getWordsWithoutCalculate(o)}return getWordsWithoutCalculate(o)},O="#808080",Text=function(e){var t,n=e.x,i=void 0===n?0:n,a=e.y,s=void 0===a?0:a,f=e.lineHeight,p=void 0===f?"1em":f,d=e.capHeight,y=void 0===d?"0.71em":d,h=e.scaleToFit,g=void 0!==h&&h,b=e.textAnchor,m=e.verticalAnchor,_=e.fill,P=void 0===_?O:_,j=_objectWithoutProperties(e,x),w=(0,o.useMemo)(function(){return getWordsByLines({breakAll:j.breakAll,children:j.children,maxLines:j.maxLines,scaleToFit:g,style:j.style,width:j.width})},[j.breakAll,j.children,j.maxLines,g,j.style,j.width]),C=j.dx,A=j.dy,T=j.angle,k=j.className,E=j.breakAll,M=_objectWithoutProperties(j,S);if(!(0,u.P2)(i)||!(0,u.P2)(s))return null;var I=i+((0,u.hj)(C)?C:0),D=s+((0,u.hj)(A)?A:0);switch(void 0===m?"end":m){case"start":t=reduceCSSCalc("calc(".concat(y,")"));break;case"middle":t=reduceCSSCalc("calc(".concat((w.length-1)/2," * -").concat(p," + (").concat(y," / 2))"));break;default:t=reduceCSSCalc("calc(".concat(w.length-1," * -").concat(p,")"))}var R=[];if(g){var L=w[0].width,N=j.width;R.push("scale(".concat(((0,u.hj)(N)?N/L:1)/L,")"))}return T&&R.push("rotate(".concat(T,", ").concat(I,", ").concat(D,")")),R.length&&(M.transform=R.join(" ")),o.createElement("text",_extends({},(0,l.L6)(M,!0),{x:I,y:D,className:(0,c.Z)("recharts-text",k),textAnchor:void 0===b?"start":b,fill:P.includes("url")?O:P}),w.map(function(e,n){var i=e.words.join(E?"":" ");return o.createElement("tspan",{x:I,dy:0===n?t:p,key:"".concat(i,"-").concat(n)},i)}))}},6812:function(e,t,n){"use strict";n.d(t,{u:function(){return g}});var o=n(2265),i=n(1864),a=n.n(i),c=n(2727),u=n.n(c),s=n(7042),l=n(7281);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,a,c,u=[],s=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(u.push(o.value),u.length!==t);s=!0);}catch(e){l=!0,i=e}finally{try{if(!s&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(l)throw i}}return u}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function defaultFormatter(e){return Array.isArray(e)&&(0,l.P2)(e[0])&&(0,l.P2)(e[1])?e.join(" ~ "):e}var DefaultTooltipContent=function(e){var t=e.separator,n=void 0===t?" : ":t,i=e.contentStyle,c=e.itemStyle,f=void 0===c?{}:c,p=e.labelStyle,d=e.payload,y=e.formatter,h=e.itemSorter,g=e.wrapperClassName,b=e.labelClassName,m=e.label,_=e.labelFormatter,x=e.accessibilityLayer,S=_objectSpread({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===i?{}:i),P=_objectSpread({margin:0},void 0===p?{}:p),O=!u()(m),j=O?m:"",w=(0,s.Z)("recharts-default-tooltip",g),C=(0,s.Z)("recharts-tooltip-label",b);return O&&_&&null!=d&&(j=_(m,d)),o.createElement("div",_extends({className:w,style:S},void 0!==x&&x?{role:"status","aria-live":"assertive"}:{}),o.createElement("p",{className:C,style:P},o.isValidElement(j)?j:"".concat(j)),function(){if(d&&d.length){var e=(h?a()(d,h):d).map(function(e,t){if("none"===e.type)return null;var i=_objectSpread({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},f),a=e.formatter||y||defaultFormatter,c=e.value,u=e.name,s=c,p=u;if(a&&null!=s&&null!=p){var h=a(c,u,e,t,d);if(Array.isArray(h)){var g=_slicedToArray(h,2);s=g[0],p=g[1]}else s=h}return o.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(t),style:i},(0,l.P2)(p)?o.createElement("span",{className:"recharts-tooltip-item-name"},p):null,(0,l.P2)(p)?o.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,o.createElement("span",{className:"recharts-tooltip-item-value"},s),o.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return o.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null}())};function translate_typeof(e){return(translate_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function translate_defineProperty(e,t,n){return(t=translate_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function translate_toPropertyKey(e){var t=translate_toPrimitive(e,"string");return"symbol"==translate_typeof(t)?t:t+""}function translate_toPrimitive(e,t){if("object"!=translate_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=translate_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var f="recharts-tooltip-wrapper",p={visibility:"hidden"};function getTooltipCSSClassName(e){var t=e.coordinate,n=e.translateX,o=e.translateY;return(0,s.Z)(f,translate_defineProperty(translate_defineProperty(translate_defineProperty(translate_defineProperty({},"".concat(f,"-right"),(0,l.hj)(n)&&t&&(0,l.hj)(t.x)&&n>=t.x),"".concat(f,"-left"),(0,l.hj)(n)&&t&&(0,l.hj)(t.x)&&n<t.x),"".concat(f,"-bottom"),(0,l.hj)(o)&&t&&(0,l.hj)(t.y)&&o>=t.y),"".concat(f,"-top"),(0,l.hj)(o)&&t&&(0,l.hj)(t.y)&&o<t.y))}function getTooltipTranslateXY(e){var t=e.allowEscapeViewBox,n=e.coordinate,o=e.key,i=e.offsetTopLeft,a=e.position,c=e.reverseDirection,u=e.tooltipDimension,s=e.viewBox,f=e.viewBoxDimension;if(a&&(0,l.hj)(a[o]))return a[o];var p=n[o]-u-i,d=n[o]+i;return t[o]?c[o]?p:d:c[o]?p<s[o]?Math.max(d,s[o]):Math.max(p,s[o]):d+u>s[o]+f?Math.max(p,s[o]):Math.max(d,s[o])}function getTransformStyle(e){var t=e.translateX,n=e.translateY;return{transform:e.useTranslate3d?"translate3d(".concat(t,"px, ").concat(n,"px, 0)"):"translate(".concat(t,"px, ").concat(n,"px)")}}function getTooltipTranslate(e){var t,n,o,i=e.allowEscapeViewBox,a=e.coordinate,c=e.offsetTopLeft,u=e.position,s=e.reverseDirection,l=e.tooltipBox,f=e.useTranslate3d,d=e.viewBox;return{cssProperties:l.height>0&&l.width>0&&a?getTransformStyle({translateX:n=getTooltipTranslateXY({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:c,position:u,reverseDirection:s,tooltipDimension:l.width,viewBox:d,viewBoxDimension:d.width}),translateY:o=getTooltipTranslateXY({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:c,position:u,reverseDirection:s,tooltipDimension:l.height,viewBox:d,viewBoxDimension:d.height}),useTranslate3d:f}):p,cssClasses:getTooltipCSSClassName({translateX:n,translateY:o,coordinate:a})}}function TooltipBoundingBox_typeof(e){return(TooltipBoundingBox_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function TooltipBoundingBox_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function TooltipBoundingBox_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?TooltipBoundingBox_ownKeys(Object(n),!0).forEach(function(t){TooltipBoundingBox_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):TooltipBoundingBox_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,TooltipBoundingBox_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _callSuper(e,t,n){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],_getPrototypeOf(e).constructor):t.apply(e,n))}function _possibleConstructorReturn(e,t){if(t&&("object"===TooltipBoundingBox_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function TooltipBoundingBox_defineProperty(e,t,n){return(t=TooltipBoundingBox_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function TooltipBoundingBox_toPropertyKey(e){var t=TooltipBoundingBox_toPrimitive(e,"string");return"symbol"==TooltipBoundingBox_typeof(t)?t:t+""}function TooltipBoundingBox_toPrimitive(e,t){if("object"!=TooltipBoundingBox_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=TooltipBoundingBox_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var d=function(e){function TooltipBoundingBox(){var e;_classCallCheck(this,TooltipBoundingBox);for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];return TooltipBoundingBox_defineProperty(e=_callSuper(this,TooltipBoundingBox,[].concat(n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),TooltipBoundingBox_defineProperty(e,"handleKeyDown",function(t){if("Escape"===t.key){var n,o,i,a;e.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(n=null===(o=e.props.coordinate)||void 0===o?void 0:o.x)&&void 0!==n?n:0,y:null!==(i=null===(a=e.props.coordinate)||void 0===a?void 0:a.y)&&void 0!==i?i:0}})}}),e}return _inherits(TooltipBoundingBox,e),_createClass(TooltipBoundingBox,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();(Math.abs(e.width-this.state.lastBoundingBox.width)>1||Math.abs(e.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:e.width,height:e.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var e,t;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var e=this,t=this.props,n=t.active,i=t.allowEscapeViewBox,a=t.animationDuration,c=t.animationEasing,u=t.children,s=t.coordinate,l=t.hasPayload,f=t.isAnimationActive,p=t.offset,d=t.position,y=t.reverseDirection,h=t.useTranslate3d,g=t.viewBox,b=t.wrapperStyle,m=getTooltipTranslate({allowEscapeViewBox:i,coordinate:s,offsetTopLeft:p,position:d,reverseDirection:y,tooltipBox:this.state.lastBoundingBox,useTranslate3d:h,viewBox:g}),_=m.cssClasses,x=m.cssProperties,S=TooltipBoundingBox_objectSpread(TooltipBoundingBox_objectSpread({transition:f&&n?"transform ".concat(a,"ms ").concat(c):void 0},x),{},{pointerEvents:"none",visibility:!this.state.dismissed&&n&&l?"visible":"hidden",position:"absolute",top:0,left:0},b);return o.createElement("div",{tabIndex:-1,className:_,style:S,ref:function(t){e.wrapperNode=t}},u)}}])}(o.PureComponent),y=n(9752),h=n(200);function Tooltip_typeof(e){return(Tooltip_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Tooltip_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function Tooltip_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Tooltip_ownKeys(Object(n),!0).forEach(function(t){Tooltip_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Tooltip_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Tooltip_classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function Tooltip_defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,Tooltip_toPropertyKey(o.key),o)}}function Tooltip_createClass(e,t,n){return t&&Tooltip_defineProperties(e.prototype,t),n&&Tooltip_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Tooltip_callSuper(e,t,n){return t=Tooltip_getPrototypeOf(t),Tooltip_possibleConstructorReturn(e,Tooltip_isNativeReflectConstruct()?Reflect.construct(t,n||[],Tooltip_getPrototypeOf(e).constructor):t.apply(e,n))}function Tooltip_possibleConstructorReturn(e,t){if(t&&("object"===Tooltip_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return Tooltip_assertThisInitialized(e)}function Tooltip_assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Tooltip_isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Tooltip_isNativeReflectConstruct=function(){return!!e})()}function Tooltip_getPrototypeOf(e){return(Tooltip_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Tooltip_inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Tooltip_setPrototypeOf(e,t)}function Tooltip_setPrototypeOf(e,t){return(Tooltip_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function Tooltip_defineProperty(e,t,n){return(t=Tooltip_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Tooltip_toPropertyKey(e){var t=Tooltip_toPrimitive(e,"string");return"symbol"==Tooltip_typeof(t)?t:t+""}function Tooltip_toPrimitive(e,t){if("object"!=Tooltip_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=Tooltip_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function defaultUniqBy(e){return e.dataKey}function renderContent(e,t){return o.isValidElement(e)?o.cloneElement(e,t):"function"==typeof e?o.createElement(e,t):o.createElement(DefaultTooltipContent,t)}var g=function(e){function Tooltip(){return Tooltip_classCallCheck(this,Tooltip),Tooltip_callSuper(this,Tooltip,arguments)}return Tooltip_inherits(Tooltip,e),Tooltip_createClass(Tooltip,[{key:"render",value:function(){var e=this,t=this.props,n=t.active,i=t.allowEscapeViewBox,a=t.animationDuration,c=t.animationEasing,u=t.content,s=t.coordinate,l=t.filterNull,f=t.isAnimationActive,p=t.offset,y=t.payload,g=t.payloadUniqBy,b=t.position,m=t.reverseDirection,_=t.useTranslate3d,x=t.viewBox,S=t.wrapperStyle,P=null!=y?y:[];l&&P.length&&(P=(0,h.z)(y.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),g,defaultUniqBy));var O=P.length>0;return o.createElement(d,{allowEscapeViewBox:i,animationDuration:a,animationEasing:c,isAnimationActive:f,active:n,coordinate:s,hasPayload:O,offset:p,position:b,reverseDirection:m,useTranslate3d:_,viewBox:x,wrapperStyle:S},renderContent(u,Tooltip_objectSpread(Tooltip_objectSpread({},this.props),{},{payload:P})))}}])}(o.PureComponent);Tooltip_defineProperty(g,"displayName","Tooltip"),Tooltip_defineProperty(g,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!y.x.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},8357:function(e,t,n){"use strict";n.d(t,{m:function(){return u}});var o=n(2265),i=n(7042),a=n(3843),c=["children","className"];function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}var u=o.forwardRef(function(e,t){var n=e.children,u=e.className,s=_objectWithoutProperties(e,c),l=(0,i.Z)("recharts-layer",u);return o.createElement("g",_extends({className:l},(0,a.L6)(s,!0),{ref:t}),n)})},7434:function(e,t,n){"use strict";n.d(t,{T:function(){return Surface}});var o=n(2265),i=n(7042),a=n(3843),c=["children","width","height","viewBox","className","style","title","desc"];function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function Surface(e){var t=e.children,n=e.width,u=e.height,s=e.viewBox,l=e.className,f=e.style,p=e.title,d=e.desc,y=_objectWithoutProperties(e,c),h=s||{width:n,height:u,x:0,y:0},g=(0,i.Z)("recharts-surface",l);return o.createElement("svg",_extends({},(0,a.L6)(y,!0,"svg"),{className:g,width:n,height:u,style:f,viewBox:"".concat(h.x," ").concat(h.y," ").concat(h.width," ").concat(h.height)}),o.createElement("title",null,p),o.createElement("desc",null,d),t)}},2794:function(e,t,n){"use strict";n.d(t,{br:function(){return ChartLayoutContextProvider},CW:function(){return useArbitraryXAxis},Mw:function(){return useChartHeight},zn:function(){return useChartWidth},sp:function(){return useClipPathId},qD:function(){return useOffset},d2:function(){return useViewBox},bH:function(){return useXAxisOrThrow},Ud:function(){return useYAxisOrThrow},Nf:function(){return useYAxisWithFiniteDomainOrRandom}});var o=n(2265),i=n(2130),a=n(1330),c=n.n(a),u=n(8022),s=n.n(u),l=n(3023),f=n.n(l)()(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),p=n(7281),d=(0,o.createContext)(void 0),y=(0,o.createContext)(void 0),h=(0,o.createContext)(void 0),g=(0,o.createContext)({}),b=(0,o.createContext)(void 0),m=(0,o.createContext)(0),_=(0,o.createContext)(0),ChartLayoutContextProvider=function(e){var t=e.state,n=t.xAxisMap,i=t.yAxisMap,a=t.offset,c=e.clipPathId,u=e.children,s=e.width,l=e.height,p=f(a);return o.createElement(d.Provider,{value:n},o.createElement(y.Provider,{value:i},o.createElement(g.Provider,{value:a},o.createElement(h.Provider,{value:p},o.createElement(b.Provider,{value:c},o.createElement(m.Provider,{value:l},o.createElement(_.Provider,{value:s},u)))))))},useClipPathId=function(){return(0,o.useContext)(b)},useXAxisOrThrow=function(e){var t=(0,o.useContext)(d);null!=t||(0,i.Z)(!1);var n=t[e];return null!=n||(0,i.Z)(!1),n},useArbitraryXAxis=function(){var e=(0,o.useContext)(d);return(0,p.Kt)(e)},useYAxisWithFiniteDomainOrRandom=function(){var e=(0,o.useContext)(y);return c()(e,function(e){return s()(e.domain,Number.isFinite)})||(0,p.Kt)(e)},useYAxisOrThrow=function(e){var t=(0,o.useContext)(y);null!=t||(0,i.Z)(!1);var n=t[e];return null!=n||(0,i.Z)(!1),n},useViewBox=function(){return(0,o.useContext)(h)},useOffset=function(){return(0,o.useContext)(g)},useChartWidth=function(){return(0,o.useContext)(_)},useChartHeight=function(){return(0,o.useContext)(m)}},8485:function(e,t,n){"use strict";n.d(t,{b:function(){return k}});var o=n(2265),i=n(5010),a=n(8614),c=n.n(a),u=n(2077),s=n.n(u),l=n(2727),f=n.n(l),p=n(8293),d=n.n(p),y=n(7042),h=n(8357),g=n(9431),b=n(1224),m=n(3343),_=n(561),x=n(6612),S=n(3843),P=n(9752),O=n(6120),j=n(7281),w=n(3249),C=n(7105),A=n(2655),T=n(7688);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _callSuper(e,t,n){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],_getPrototypeOf(e).constructor):t.apply(e,n))}function _possibleConstructorReturn(e,t){if(t&&("object"===_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var k=function(e){function Pie(e){var t;return _classCallCheck(this,Pie),_defineProperty(t=_callSuper(this,Pie,[e]),"pieRef",null),_defineProperty(t,"sectorRefs",[]),_defineProperty(t,"id",(0,j.EL)("recharts-pie-")),_defineProperty(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),d()(e)&&e()}),_defineProperty(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),d()(e)&&e()}),t.state={isAnimationFinished:!e.isAnimationActive,prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,sectorToFocus:0},t}return _inherits(Pie,e),_createClass(Pie,[{key:"isActiveIndex",value:function(e){var t=this.props.activeIndex;return Array.isArray(t)?-1!==t.indexOf(e):e===t}},{key:"hasActiveIndex",value:function(){var e=this.props.activeIndex;return Array.isArray(e)?0!==e.length:e||0===e}},{key:"renderLabels",value:function(e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,n=t.label,i=t.labelLine,a=t.dataKey,c=t.valueKey,u=(0,S.L6)(this.props,!1),s=(0,S.L6)(n,!1),l=(0,S.L6)(i,!1),p=n&&n.offsetRadius||20,d=e.map(function(e,t){var d=(e.startAngle+e.endAngle)/2,y=(0,O.op)(e.cx,e.cy,e.outerRadius+p,d),g=_objectSpread(_objectSpread(_objectSpread(_objectSpread({},u),e),{},{stroke:"none"},s),{},{index:t,textAnchor:Pie.getTextAnchor(y.x,e.cx)},y),b=_objectSpread(_objectSpread(_objectSpread(_objectSpread({},u),e),{},{fill:"none",stroke:e.fill},l),{},{index:t,points:[(0,O.op)(e.cx,e.cy,e.outerRadius,d),y]}),m=a;return f()(a)&&f()(c)?m="value":f()(a)&&(m=c),o.createElement(h.m,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},i&&Pie.renderLabelLineItem(i,b,"line"),Pie.renderLabelItem(n,g,(0,w.F$)(e,m)))});return o.createElement(h.m,{className:"recharts-pie-labels"},d)}},{key:"renderSectorsStatically",value:function(e){var t=this,n=this.props,i=n.activeShape,a=n.blendStroke,c=n.inactiveShape;return e.map(function(n,u){if((null==n?void 0:n.startAngle)===0&&(null==n?void 0:n.endAngle)===0&&1!==e.length)return null;var s=t.isActiveIndex(u),l=c&&t.hasActiveIndex()?c:null,f=_objectSpread(_objectSpread({},n),{},{stroke:a?n.fill:n.stroke,tabIndex:-1});return o.createElement(h.m,_extends({ref:function(e){e&&!t.sectorRefs.includes(e)&&t.sectorRefs.push(e)},tabIndex:-1,className:"recharts-pie-sector"},(0,A.bw)(t.props,n,u),{key:"sector-".concat(null==n?void 0:n.startAngle,"-").concat(null==n?void 0:n.endAngle,"-").concat(n.midAngle,"-").concat(u)}),o.createElement(T.bn,_extends({option:s?i:l,isActive:s,shapeType:"sector"},f)))})}},{key:"renderSectorsWithAnimation",value:function(){var e=this,t=this.props,n=t.sectors,a=t.isAnimationActive,u=t.animationBegin,s=t.animationDuration,l=t.animationEasing,f=t.animationId,p=this.state,d=p.prevSectors,y=p.prevIsAnimationActive;return o.createElement(i.ZP,{begin:u,duration:s,isActive:a,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(f,"-").concat(y),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(t){var i=t.t,a=[],u=(n&&n[0]).startAngle;return n.forEach(function(e,t){var n=d&&d[t],o=t>0?c()(e,"paddingAngle",0):0;if(n){var s=(0,j.k4)(n.endAngle-n.startAngle,e.endAngle-e.startAngle),l=_objectSpread(_objectSpread({},e),{},{startAngle:u+o,endAngle:u+s(i)+o});a.push(l),u=l.endAngle}else{var f=e.endAngle,p=e.startAngle,y=(0,j.k4)(0,f-p)(i),h=_objectSpread(_objectSpread({},e),{},{startAngle:u+o,endAngle:u+y+o});a.push(h),u=h.endAngle}}),o.createElement(h.m,null,e.renderSectorsStatically(a))})}},{key:"attachKeyboardHandlers",value:function(e){var t=this;e.onkeydown=function(e){if(!e.altKey)switch(e.key){case"ArrowLeft":var n=++t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[n].focus(),t.setState({sectorToFocus:n});break;case"ArrowRight":var o=--t.state.sectorToFocus<0?t.sectorRefs.length-1:t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[o].focus(),t.setState({sectorToFocus:o});break;case"Escape":t.sectorRefs[t.state.sectorToFocus].blur(),t.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var e=this.props,t=e.sectors,n=e.isAnimationActive,o=this.state.prevSectors;return n&&t&&t.length&&(!o||!s()(o,t))?this.renderSectorsWithAnimation():this.renderSectorsStatically(t)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var e=this,t=this.props,n=t.hide,i=t.sectors,a=t.className,c=t.label,u=t.cx,s=t.cy,l=t.innerRadius,f=t.outerRadius,p=t.isAnimationActive,d=this.state.isAnimationFinished;if(n||!i||!i.length||!(0,j.hj)(u)||!(0,j.hj)(s)||!(0,j.hj)(l)||!(0,j.hj)(f))return null;var g=(0,y.Z)("recharts-pie",a);return o.createElement(h.m,{tabIndex:this.props.rootTabIndex,className:g,ref:function(t){e.pieRef=t}},this.renderSectors(),c&&this.renderLabels(i),m._.renderCallByParent(this.props,null,!1),(!p||d)&&_.e.renderCallByParent(this.props,i,!1))}}],[{key:"getDerivedStateFromProps",value:function(e,t){return t.prevIsAnimationActive!==e.isAnimationActive?{prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:[],isAnimationFinished:!0}:e.isAnimationActive&&e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:t.curSectors,isAnimationFinished:!0}:e.sectors!==t.curSectors?{curSectors:e.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(e,t){return e>t?"start":e<t?"end":"middle"}},{key:"renderLabelLineItem",value:function(e,t,n){if(o.isValidElement(e))return o.cloneElement(e,t);if(d()(e))return e(t);var i=(0,y.Z)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return o.createElement(g.H,_extends({},t,{key:n,type:"linear",className:i}))}},{key:"renderLabelItem",value:function(e,t,n){if(o.isValidElement(e))return o.cloneElement(e,t);var i=n;if(d()(e)&&(i=e(t),o.isValidElement(i)))return i;var a=(0,y.Z)("recharts-pie-label-text","boolean"==typeof e||d()(e)?"":e.className);return o.createElement(b.x,_extends({},t,{alignmentBaseline:"middle",className:a}),i)}}])}(o.PureComponent);_defineProperty(k,"displayName","Pie"),_defineProperty(k,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!P.x.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),_defineProperty(k,"parseDeltaAngle",function(e,t){return(0,j.uY)(t-e)*Math.min(Math.abs(t-e),360)}),_defineProperty(k,"getRealPieData",function(e){var t=e.data,n=e.children,o=(0,S.L6)(e,!1),i=(0,S.NN)(n,x.b);return t&&t.length?t.map(function(e,t){return _objectSpread(_objectSpread(_objectSpread({payload:e},o),e),i&&i[t]&&i[t].props)}):i&&i.length?i.map(function(e){return _objectSpread(_objectSpread({},o),e.props)}):[]}),_defineProperty(k,"parseCoordinateOfPie",function(e,t){var n=t.top,o=t.left,i=t.width,a=t.height,c=(0,O.$4)(i,a);return{cx:o+(0,j.h1)(e.cx,i,i/2),cy:n+(0,j.h1)(e.cy,a,a/2),innerRadius:(0,j.h1)(e.innerRadius,c,0),outerRadius:(0,j.h1)(e.outerRadius,c,.8*c),maxRadius:e.maxRadius||Math.sqrt(i*i+a*a)/2}}),_defineProperty(k,"getComposedData",function(e){var t,n,o=e.item,i=e.offset,a=void 0!==o.type.defaultProps?_objectSpread(_objectSpread({},o.type.defaultProps),o.props):o.props,c=k.getRealPieData(a);if(!c||!c.length)return null;var u=a.cornerRadius,s=a.startAngle,l=a.endAngle,p=a.paddingAngle,d=a.dataKey,y=a.nameKey,h=a.valueKey,g=a.tooltipType,b=Math.abs(a.minAngle),m=k.parseCoordinateOfPie(a,i),_=k.parseDeltaAngle(s,l),x=Math.abs(_),S=d;f()(d)&&f()(h)?((0,C.Z)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),S="value"):f()(d)&&((0,C.Z)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),S=h);var P=c.filter(function(e){return 0!==(0,w.F$)(e,S,0)}).length,A=x-P*b-(x>=360?P:P-1)*p,T=c.reduce(function(e,t){var n=(0,w.F$)(t,S,0);return e+((0,j.hj)(n)?n:0)},0);return T>0&&(t=c.map(function(e,t){var o,i=(0,w.F$)(e,S,0),a=(0,w.F$)(e,y,t),c=((0,j.hj)(i)?i:0)/T,l=(o=t?n.endAngle+(0,j.uY)(_)*p*(0!==i?1:0):s)+(0,j.uY)(_)*((0!==i?b:0)+c*A),f=(o+l)/2,d=(m.innerRadius+m.outerRadius)/2,h=[{name:a,value:i,payload:e,dataKey:S,type:g}],x=(0,O.op)(m.cx,m.cy,d,f);return n=_objectSpread(_objectSpread(_objectSpread({percent:c,cornerRadius:u,name:a,tooltipPayload:h,midAngle:f,middleRadius:d,tooltipPosition:x},e),m),{},{value:(0,w.F$)(e,S),startAngle:o,endAngle:l,payload:e,paddingAngle:(0,j.uY)(_)*p})})),_objectSpread(_objectSpread({},m),{},{sectors:t,data:c})})},9431:function(e,t,n){"use strict";n.d(t,{H:function(){return Curve}});var o=n(2265);function noop(){}function point(e,t,n){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+n)/6)}function Basis(e){this._context=e}function basis(e){return new Basis(e)}function BasisClosed(e){this._context=e}function basisClosed(e){return new BasisClosed(e)}function BasisOpen(e){this._context=e}function basisOpen(e){return new BasisOpen(e)}Basis.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:point(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:point(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},BasisClosed.prototype={areaStart:noop,areaEnd:noop,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:point(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},BasisOpen.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var n=(this._x0+4*this._x1+e)/6,o=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(n,o):this._context.moveTo(n,o);break;case 3:this._point=4;default:point(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};let Bump=class Bump{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}};function bumpX(e){return new Bump(e,!0)}function bumpY(e){return new Bump(e,!1)}function LinearClosed(e){this._context=e}function linearClosed(e){return new LinearClosed(e)}function Linear(e){this._context=e}function linear(e){return new Linear(e)}function slope3(e,t,n){var o=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(o||i<0&&-0),c=(n-e._y1)/(i||o<0&&-0);return((a<0?-1:1)+(c<0?-1:1))*Math.min(Math.abs(a),Math.abs(c),.5*Math.abs((a*i+c*o)/(o+i)))||0}function slope2(e,t){var n=e._x1-e._x0;return n?(3*(e._y1-e._y0)/n-t)/2:t}function monotone_point(e,t,n){var o=e._x0,i=e._y0,a=e._x1,c=e._y1,u=(a-o)/3;e._context.bezierCurveTo(o+u,i+u*t,a-u,c-u*n,a,c)}function MonotoneX(e){this._context=e}function MonotoneY(e){this._context=new ReflectContext(e)}function ReflectContext(e){this._context=e}function monotoneX(e){return new MonotoneX(e)}function monotoneY(e){return new MonotoneY(e)}function Natural(e){this._context=e}function controlPoints(e){var t,n,o=e.length-1,i=Array(o),a=Array(o),c=Array(o);for(i[0]=0,a[0]=2,c[0]=e[0]+2*e[1],t=1;t<o-1;++t)i[t]=1,a[t]=4,c[t]=4*e[t]+2*e[t+1];for(i[o-1]=2,a[o-1]=7,c[o-1]=8*e[o-1]+e[o],t=1;t<o;++t)n=i[t]/a[t-1],a[t]-=n,c[t]-=n*c[t-1];for(i[o-1]=c[o-1]/a[o-1],t=o-2;t>=0;--t)i[t]=(c[t]-i[t+1])/a[t];for(t=0,a[o-1]=(e[o]+i[o-1])/2;t<o-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function natural(e){return new Natural(e)}function Step(e,t){this._context=e,this._t=t}function step(e){return new Step(e,.5)}function stepBefore(e){return new Step(e,0)}function stepAfter(e){return new Step(e,1)}LinearClosed.prototype={areaStart:noop,areaEnd:noop,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}},Linear.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},MonotoneX.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:monotone_point(this,this._t0,slope2(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var n=NaN;if(t=+t,(e=+e)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,monotone_point(this,slope2(this,n=slope3(this,e,t)),n);break;default:monotone_point(this,this._t0,n=slope3(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=n}}},(MonotoneY.prototype=Object.create(MonotoneX.prototype)).point=function(e,t){MonotoneX.prototype.point.call(this,t,e)},ReflectContext.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,n,o,i,a){this._context.bezierCurveTo(t,e,o,n,a,i)}},Natural.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,n=e.length;if(n){if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===n)this._context.lineTo(e[1],t[1]);else for(var o=controlPoints(e),i=controlPoints(t),a=0,c=1;c<n;++a,++c)this._context.bezierCurveTo(o[0][a],i[0][a],o[1][a],i[1][a],e[c],t[c])}(this._line||0!==this._line&&1===n)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},Step.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var n=this._x*(1-this._t)+e*this._t;this._context.lineTo(n,this._y),this._context.lineTo(n,t)}}this._x=e,this._y=t}};var i=n(3841),a=n(4299),c=n(730);function point_x(e){return e[0]}function point_y(e){return e[1]}function line(e,t){var n=(0,a.Z)(!0),o=null,u=linear,s=null,l=(0,c.d)(line);function line(a){var c,f,p,d=(a=(0,i.Z)(a)).length,y=!1;for(null==o&&(s=u(p=l())),c=0;c<=d;++c)!(c<d&&n(f=a[c],c,a))===y&&((y=!y)?s.lineStart():s.lineEnd()),y&&s.point(+e(f,c,a),+t(f,c,a));if(p)return s=null,p+""||null}return e="function"==typeof e?e:void 0===e?point_x:(0,a.Z)(e),t="function"==typeof t?t:void 0===t?point_y:(0,a.Z)(t),line.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,a.Z)(+t),line):e},line.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,a.Z)(+e),line):t},line.defined=function(e){return arguments.length?(n="function"==typeof e?e:(0,a.Z)(!!e),line):n},line.curve=function(e){return arguments.length?(u=e,null!=o&&(s=u(o)),line):u},line.context=function(e){return arguments.length?(null==e?o=s=null:s=u(o=e),line):o},line}function src_area(e,t,n){var o=null,u=(0,a.Z)(!0),s=null,l=linear,f=null,p=(0,c.d)(area);function area(a){var c,d,y,h,g,b=(a=(0,i.Z)(a)).length,m=!1,_=Array(b),x=Array(b);for(null==s&&(f=l(g=p())),c=0;c<=b;++c){if(!(c<b&&u(h=a[c],c,a))===m){if(m=!m)d=c,f.areaStart(),f.lineStart();else{for(f.lineEnd(),f.lineStart(),y=c-1;y>=d;--y)f.point(_[y],x[y]);f.lineEnd(),f.areaEnd()}}m&&(_[c]=+e(h,c,a),x[c]=+t(h,c,a),f.point(o?+o(h,c,a):_[c],n?+n(h,c,a):x[c]))}if(g)return f=null,g+""||null}function arealine(){return line().defined(u).curve(l).context(s)}return e="function"==typeof e?e:void 0===e?point_x:(0,a.Z)(+e),t="function"==typeof t?t:void 0===t?(0,a.Z)(0):(0,a.Z)(+t),n="function"==typeof n?n:void 0===n?point_y:(0,a.Z)(+n),area.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,a.Z)(+t),o=null,area):e},area.x0=function(t){return arguments.length?(e="function"==typeof t?t:(0,a.Z)(+t),area):e},area.x1=function(e){return arguments.length?(o=null==e?null:"function"==typeof e?e:(0,a.Z)(+e),area):o},area.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,a.Z)(+e),n=null,area):t},area.y0=function(e){return arguments.length?(t="function"==typeof e?e:(0,a.Z)(+e),area):t},area.y1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,a.Z)(+e),area):n},area.lineX0=area.lineY0=function(){return arealine().x(e).y(t)},area.lineY1=function(){return arealine().x(e).y(n)},area.lineX1=function(){return arealine().x(o).y(t)},area.defined=function(e){return arguments.length?(u="function"==typeof e?e:(0,a.Z)(!!e),area):u},area.curve=function(e){return arguments.length?(l=e,null!=s&&(f=l(s)),area):l},area.context=function(e){return arguments.length?(null==e?s=f=null:f=l(s=e),area):s},area}var u=n(1008),s=n.n(u),l=n(8293),f=n.n(l),p=n(7042),d=n(2655),y=n(3843),h=n(7281);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var g={curveBasisClosed:basisClosed,curveBasisOpen:basisOpen,curveBasis:basis,curveBumpX:bumpX,curveBumpY:bumpY,curveLinearClosed:linearClosed,curveLinear:linear,curveMonotoneX:monotoneX,curveMonotoneY:monotoneY,curveNatural:natural,curveStep:step,curveStepAfter:stepAfter,curveStepBefore:stepBefore},defined=function(e){return e.x===+e.x&&e.y===+e.y},getX=function(e){return e.x},getY=function(e){return e.y},getCurveFactory=function(e,t){if(f()(e))return e;var n="curve".concat(s()(e));return("curveMonotone"===n||"curveBump"===n)&&t?g["".concat(n).concat("vertical"===t?"Y":"X")]:g[n]||linear},getPath=function(e){var t,n=e.type,o=e.points,i=void 0===o?[]:o,a=e.baseLine,c=e.layout,u=e.connectNulls,s=void 0!==u&&u,l=getCurveFactory(void 0===n?"linear":n,c),f=s?i.filter(function(e){return defined(e)}):i;if(Array.isArray(a)){var p=s?a.filter(function(e){return defined(e)}):a,d=f.map(function(e,t){return _objectSpread(_objectSpread({},e),{},{base:p[t]})});return(t="vertical"===c?src_area().y(getY).x1(getX).x0(function(e){return e.base.x}):src_area().x(getX).y1(getY).y0(function(e){return e.base.y})).defined(defined).curve(l),t(d)}return(t="vertical"===c&&(0,h.hj)(a)?src_area().y(getY).x1(getX).x0(a):(0,h.hj)(a)?src_area().x(getX).y1(getY).y0(a):line().x(getX).y(getY)).defined(defined).curve(l),t(f)},Curve=function(e){var t=e.className,n=e.points,i=e.path,a=e.pathRef;if((!n||!n.length)&&!i)return null;var c=n&&n.length?getPath(e):i;return o.createElement("path",_extends({},(0,y.L6)(e,!1),(0,d.Ym)(e),{className:(0,p.Z)("recharts-curve",t),d:c,ref:a}))}},4304:function(e,t,n){"use strict";n.d(t,{o:function(){return Dot}});var o=n(2265),i=n(7042),a=n(2655),c=n(3843);function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}var Dot=function(e){var t=e.cx,n=e.cy,u=e.r,s=e.className,l=(0,i.Z)("recharts-dot",s);return t===+t&&n===+n&&u===+u?o.createElement("circle",_extends({},(0,c.L6)(e,!1),(0,a.Ym)(e),{className:l,cx:t,cy:n,r:u})):null}},6275:function(e,t,n){"use strict";n.d(t,{A:function(){return Rectangle},X:function(){return isInRectangle}});var o=n(2265),i=n(7042),a=n(5010),c=n(3843);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,a,c,u=[],s=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(u.push(o.value),u.length!==t);s=!0);}catch(e){l=!0,i=e}finally{try{if(!s&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(l)throw i}}return u}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var getRectanglePath=function(e,t,n,o,i){var a,c=Math.min(Math.abs(n)/2,Math.abs(o)/2),u=o>=0?1:-1,s=n>=0?1:-1,l=o>=0&&n>=0||o<0&&n<0?1:0;if(c>0&&i instanceof Array){for(var f=[0,0,0,0],p=0;p<4;p++)f[p]=i[p]>c?c:i[p];a="M".concat(e,",").concat(t+u*f[0]),f[0]>0&&(a+="A ".concat(f[0],",").concat(f[0],",0,0,").concat(l,",").concat(e+s*f[0],",").concat(t)),a+="L ".concat(e+n-s*f[1],",").concat(t),f[1]>0&&(a+="A ".concat(f[1],",").concat(f[1],",0,0,").concat(l,",\n        ").concat(e+n,",").concat(t+u*f[1])),a+="L ".concat(e+n,",").concat(t+o-u*f[2]),f[2]>0&&(a+="A ".concat(f[2],",").concat(f[2],",0,0,").concat(l,",\n        ").concat(e+n-s*f[2],",").concat(t+o)),a+="L ".concat(e+s*f[3],",").concat(t+o),f[3]>0&&(a+="A ".concat(f[3],",").concat(f[3],",0,0,").concat(l,",\n        ").concat(e,",").concat(t+o-u*f[3])),a+="Z"}else if(c>0&&i===+i&&i>0){var d=Math.min(c,i);a="M ".concat(e,",").concat(t+u*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(l,",").concat(e+s*d,",").concat(t,"\n            L ").concat(e+n-s*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(l,",").concat(e+n,",").concat(t+u*d,"\n            L ").concat(e+n,",").concat(t+o-u*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(l,",").concat(e+n-s*d,",").concat(t+o,"\n            L ").concat(e+s*d,",").concat(t+o,"\n            A ").concat(d,",").concat(d,",0,0,").concat(l,",").concat(e,",").concat(t+o-u*d," Z")}else a="M ".concat(e,",").concat(t," h ").concat(n," v ").concat(o," h ").concat(-n," Z");return a},isInRectangle=function(e,t){if(!e||!t)return!1;var n=e.x,o=e.y,i=t.x,a=t.y,c=t.width,u=t.height;return!!(Math.abs(c)>0&&Math.abs(u)>0)&&n>=Math.min(i,i+c)&&n<=Math.max(i,i+c)&&o>=Math.min(a,a+u)&&o<=Math.max(a,a+u)},u={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Rectangle=function(e){var t=_objectSpread(_objectSpread({},u),e),n=(0,o.useRef)(),s=_slicedToArray((0,o.useState)(-1),2),l=s[0],f=s[1];(0,o.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&f(e)}catch(e){}},[]);var p=t.x,d=t.y,y=t.width,h=t.height,g=t.radius,b=t.className,m=t.animationEasing,_=t.animationDuration,x=t.animationBegin,S=t.isAnimationActive,P=t.isUpdateAnimationActive;if(p!==+p||d!==+d||y!==+y||h!==+h||0===y||0===h)return null;var O=(0,i.Z)("recharts-rectangle",b);return P?o.createElement(a.ZP,{canBegin:l>0,from:{width:y,height:h,x:p,y:d},to:{width:y,height:h,x:p,y:d},duration:_,animationEasing:m,isActive:P},function(e){var i=e.width,u=e.height,s=e.x,f=e.y;return o.createElement(a.ZP,{canBegin:l>0,from:"0px ".concat(-1===l?1:l,"px"),to:"".concat(l,"px 0px"),attributeName:"strokeDasharray",begin:x,duration:_,isActive:S,easing:m},o.createElement("path",_extends({},(0,c.L6)(t,!0),{className:O,d:getRectanglePath(s,f,i,u,g),ref:n})))}):o.createElement("path",_extends({},(0,c.L6)(t,!0),{className:O,d:getRectanglePath(p,d,y,h,g)}))}},7795:function(e,t,n){"use strict";n.d(t,{L:function(){return Sector}});var o=n(2265),i=n(7042),a=n(3843),c=n(6120),u=n(7281);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var getTangentCircle=function(e){var t=e.cx,n=e.cy,o=e.radius,i=e.angle,a=e.sign,u=e.isExternal,s=e.cornerRadius,l=e.cornerIsExternal,f=s*(u?1:-1)+o,p=Math.asin(s/f)/c.Wk,d=l?i:i+a*p,y=(0,c.op)(t,n,f,d),h=(0,c.op)(t,n,o,d),g=l?i-a*p:i;return{center:y,circleTangency:h,lineTangency:(0,c.op)(t,n,f*Math.cos(p*c.Wk),g),theta:p}},getSectorPath=function(e){var t=e.cx,n=e.cy,o=e.innerRadius,i=e.outerRadius,a=e.startAngle,s=e.endAngle,l=(0,u.uY)(s-a)*Math.min(Math.abs(s-a),359.999),f=a+l,p=(0,c.op)(t,n,i,a),d=(0,c.op)(t,n,i,f),y="M ".concat(p.x,",").concat(p.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(a>f),",\n    ").concat(d.x,",").concat(d.y,"\n  ");if(o>0){var h=(0,c.op)(t,n,o,a),g=(0,c.op)(t,n,o,f);y+="L ".concat(g.x,",").concat(g.y,"\n            A ").concat(o,",").concat(o,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(a<=f),",\n            ").concat(h.x,",").concat(h.y," Z")}else y+="L ".concat(t,",").concat(n," Z");return y},getSectorWithCorner=function(e){var t=e.cx,n=e.cy,o=e.innerRadius,i=e.outerRadius,a=e.cornerRadius,c=e.forceCornerRadius,s=e.cornerIsExternal,l=e.startAngle,f=e.endAngle,p=(0,u.uY)(f-l),d=getTangentCircle({cx:t,cy:n,radius:i,angle:l,sign:p,cornerRadius:a,cornerIsExternal:s}),y=d.circleTangency,h=d.lineTangency,g=d.theta,b=getTangentCircle({cx:t,cy:n,radius:i,angle:f,sign:-p,cornerRadius:a,cornerIsExternal:s}),m=b.circleTangency,_=b.lineTangency,x=b.theta,S=s?Math.abs(l-f):Math.abs(l-f)-g-x;if(S<0)return c?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):getSectorPath({cx:t,cy:n,innerRadius:o,outerRadius:i,startAngle:l,endAngle:f});var P="M ".concat(h.x,",").concat(h.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(S>180),",").concat(+(p<0),",").concat(m.x,",").concat(m.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(_.x,",").concat(_.y,"\n  ");if(o>0){var O=getTangentCircle({cx:t,cy:n,radius:o,angle:l,sign:p,isExternal:!0,cornerRadius:a,cornerIsExternal:s}),j=O.circleTangency,w=O.lineTangency,C=O.theta,A=getTangentCircle({cx:t,cy:n,radius:o,angle:f,sign:-p,isExternal:!0,cornerRadius:a,cornerIsExternal:s}),T=A.circleTangency,k=A.lineTangency,E=A.theta,M=s?Math.abs(l-f):Math.abs(l-f)-C-E;if(M<0&&0===a)return"".concat(P,"L").concat(t,",").concat(n,"Z");P+="L".concat(k.x,",").concat(k.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(T.x,",").concat(T.y,"\n      A").concat(o,",").concat(o,",0,").concat(+(M>180),",").concat(+(p>0),",").concat(j.x,",").concat(j.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(w.x,",").concat(w.y,"Z")}else P+="L".concat(t,",").concat(n,"Z");return P},s={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},Sector=function(e){var t,n=_objectSpread(_objectSpread({},s),e),c=n.cx,l=n.cy,f=n.innerRadius,p=n.outerRadius,d=n.cornerRadius,y=n.forceCornerRadius,h=n.cornerIsExternal,g=n.startAngle,b=n.endAngle,m=n.className;if(p<f||g===b)return null;var _=(0,i.Z)("recharts-sector",m),x=p-f,S=(0,u.h1)(d,x,0,!0);return t=S>0&&360>Math.abs(g-b)?getSectorWithCorner({cx:c,cy:l,innerRadius:f,outerRadius:p,cornerRadius:Math.min(S,x/2),forceCornerRadius:y,cornerIsExternal:h,startAngle:g,endAngle:b}):getSectorPath({cx:c,cy:l,innerRadius:f,outerRadius:p,startAngle:g,endAngle:b}),o.createElement("path",_extends({},(0,a.L6)(n,!0),{className:_,d:t,role:"img"}))}},2245:function(e,t,n){"use strict";n.d(t,{v:function(){return Symbols}});var o=n(2265),i=n(1008),a=n.n(i);let c=Math.cos,u=Math.sin,s=Math.sqrt,l=Math.PI,f=2*l;var p={draw(e,t){let n=s(t/l);e.moveTo(n,0),e.arc(0,0,n,0,f)}};let d=s(1/3),y=2*d,h=u(l/10)/u(7*l/10),g=u(f/10)*h,b=-c(f/10)*h,m=s(3),_=s(3)/2,x=1/s(12),S=(x/2+1)*3;var P=n(4299),O=n(730);function symbol_Symbol(e,t){let n=null,o=(0,O.d)(symbol);function symbol(){let i;if(n||(n=i=o()),e.apply(this,arguments).draw(n,+t.apply(this,arguments)),i)return n=null,i+""||null}return e="function"==typeof e?e:(0,P.Z)(e||p),t="function"==typeof t?t:(0,P.Z)(void 0===t?64:+t),symbol.type=function(t){return arguments.length?(e="function"==typeof t?t:(0,P.Z)(t),symbol):e},symbol.size=function(e){return arguments.length?(t="function"==typeof e?e:(0,P.Z)(+e),symbol):t},symbol.context=function(e){return arguments.length?(n=null==e?null:e,symbol):n},symbol}s(3),s(3);var j=n(7042),w=n(3843);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var C=["type","size","sizeType"];function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}var A={symbolCircle:p,symbolCross:{draw(e,t){let n=s(t/5)/2;e.moveTo(-3*n,-n),e.lineTo(-n,-n),e.lineTo(-n,-3*n),e.lineTo(n,-3*n),e.lineTo(n,-n),e.lineTo(3*n,-n),e.lineTo(3*n,n),e.lineTo(n,n),e.lineTo(n,3*n),e.lineTo(-n,3*n),e.lineTo(-n,n),e.lineTo(-3*n,n),e.closePath()}},symbolDiamond:{draw(e,t){let n=s(t/y),o=n*d;e.moveTo(0,-n),e.lineTo(o,0),e.lineTo(0,n),e.lineTo(-o,0),e.closePath()}},symbolSquare:{draw(e,t){let n=s(t),o=-n/2;e.rect(o,o,n,n)}},symbolStar:{draw(e,t){let n=s(.8908130915292852*t),o=g*n,i=b*n;e.moveTo(0,-n),e.lineTo(o,i);for(let t=1;t<5;++t){let a=f*t/5,s=c(a),l=u(a);e.lineTo(l*n,-s*n),e.lineTo(s*o-l*i,l*o+s*i)}e.closePath()}},symbolTriangle:{draw(e,t){let n=-s(t/(3*m));e.moveTo(0,2*n),e.lineTo(-m*n,-n),e.lineTo(m*n,-n),e.closePath()}},symbolWye:{draw(e,t){let n=s(t/S),o=n/2,i=n*x,a=n*x+n,c=-o;e.moveTo(o,i),e.lineTo(o,a),e.lineTo(c,a),e.lineTo(-.5*o-_*i,_*o+-.5*i),e.lineTo(-.5*o-_*a,_*o+-.5*a),e.lineTo(-.5*c-_*a,_*c+-.5*a),e.lineTo(-.5*o+_*i,-.5*i-_*o),e.lineTo(-.5*o+_*a,-.5*a-_*o),e.lineTo(-.5*c+_*a,-.5*a-_*c),e.closePath()}}},T=Math.PI/180,calculateAreaSize=function(e,t,n){if("area"===t)return e;switch(n){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var o=18*T;return 1.25*e*e*(Math.tan(o)-Math.tan(2*o)*Math.pow(Math.tan(o),2));case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},Symbols=function(e){var t,n=e.type,i=void 0===n?"circle":n,c=e.size,u=void 0===c?64:c,s=e.sizeType,l=void 0===s?"area":s,f=_objectSpread(_objectSpread({},_objectWithoutProperties(e,C)),{},{type:i,size:u,sizeType:l}),d=f.className,y=f.cx,h=f.cy,g=(0,w.L6)(f,!0);return y===+y&&h===+h&&u===+u?o.createElement("path",_extends({},g,{className:(0,j.Z)("recharts-symbols",d),transform:"translate(".concat(y,", ").concat(h,")"),d:(t=A["symbol".concat(a()(i))]||p,symbol_Symbol().type(t).size(calculateAreaSize(u,l,i))())})):null};Symbols.registerSymbol=function(e,t){A["symbol".concat(a()(e))]=t}},7688:function(e,t,n){"use strict";n.d(t,{bn:function(){return Shape},a3:function(){return getActiveShapeIndexForTooltip},lT:function(){return isFunnel},V$:function(){return isPie},w7:function(){return isScatter}});var o=n(2265),i=n(8293),a=n.n(i),c=n(4412),u=n.n(c),s=n(2972),l=n.n(s),f=n(2077),p=n.n(f),d=n(6275),y=n(7042),h=n(5010),g=n(3843);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,a,c,u=[],s=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(u.push(o.value),u.length!==t);s=!0);}catch(e){l=!0,i=e}finally{try{if(!s&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(l)throw i}}return u}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var getTrapezoidPath=function(e,t,n,o,i){var a=n-o;return"M ".concat(e,",").concat(t)+"L ".concat(e+n,",").concat(t)+"L ".concat(e+n-a/2,",").concat(t+i)+"L ".concat(e+n-a/2-o,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},b={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Trapezoid=function(e){var t=_objectSpread(_objectSpread({},b),e),n=(0,o.useRef)(),i=_slicedToArray((0,o.useState)(-1),2),a=i[0],c=i[1];(0,o.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&c(e)}catch(e){}},[]);var u=t.x,s=t.y,l=t.upperWidth,f=t.lowerWidth,p=t.height,d=t.className,m=t.animationEasing,_=t.animationDuration,x=t.animationBegin,S=t.isUpdateAnimationActive;if(u!==+u||s!==+s||l!==+l||f!==+f||p!==+p||0===l&&0===f||0===p)return null;var P=(0,y.Z)("recharts-trapezoid",d);return S?o.createElement(h.ZP,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:p,x:u,y:s},to:{upperWidth:l,lowerWidth:f,height:p,x:u,y:s},duration:_,animationEasing:m,isActive:S},function(e){var i=e.upperWidth,c=e.lowerWidth,u=e.height,s=e.x,l=e.y;return o.createElement(h.ZP,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:x,duration:_,easing:m},o.createElement("path",_extends({},(0,g.L6)(t,!0),{className:P,d:getTrapezoidPath(s,l,i,c,u),ref:n})))}):o.createElement("g",null,o.createElement("path",_extends({},(0,g.L6)(t,!0),{className:P,d:getTrapezoidPath(u,s,l,f,p)})))},m=n(7795),_=n(8357),x=n(2245),S=["option","shapeType","propTransformer","activeClassName","isActive"];function ActiveShapeUtils_typeof(e){return(ActiveShapeUtils_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function ActiveShapeUtils_ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function ActiveShapeUtils_objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ActiveShapeUtils_ownKeys(Object(n),!0).forEach(function(t){ActiveShapeUtils_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ActiveShapeUtils_ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function ActiveShapeUtils_defineProperty(e,t,n){return(t=ActiveShapeUtils_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ActiveShapeUtils_toPropertyKey(e){var t=ActiveShapeUtils_toPrimitive(e,"string");return"symbol"==ActiveShapeUtils_typeof(t)?t:t+""}function ActiveShapeUtils_toPrimitive(e,t){if("object"!=ActiveShapeUtils_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=ActiveShapeUtils_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function defaultPropTransformer(e,t){return ActiveShapeUtils_objectSpread(ActiveShapeUtils_objectSpread({},t),e)}function isSymbolsProps(e,t){return"symbols"===e}function ShapeSelector(e){var t=e.shapeType,n=e.elementProps;switch(t){case"rectangle":return o.createElement(d.A,n);case"trapezoid":return o.createElement(Trapezoid,n);case"sector":return o.createElement(m.L,n);case"symbols":if(isSymbolsProps(t,n))return o.createElement(x.v,n);break;default:return null}}function getPropsFromShapeOption(e){return(0,o.isValidElement)(e)?e.props:e}function Shape(e){var t,n=e.option,i=e.shapeType,c=e.propTransformer,s=e.activeClassName,f=e.isActive,p=_objectWithoutProperties(e,S);if((0,o.isValidElement)(n))t=(0,o.cloneElement)(n,ActiveShapeUtils_objectSpread(ActiveShapeUtils_objectSpread({},p),getPropsFromShapeOption(n)));else if(a()(n))t=n(p);else if(u()(n)&&!l()(n)){var d=(void 0===c?defaultPropTransformer:c)(n,p);t=o.createElement(ShapeSelector,{shapeType:i,elementProps:d})}else t=o.createElement(ShapeSelector,{shapeType:i,elementProps:p});return f?o.createElement(_.m,{className:void 0===s?"recharts-active-shape":s},t):t}function isFunnel(e,t){return null!=t&&"trapezoids"in e.props}function isPie(e,t){return null!=t&&"sectors"in e.props}function isScatter(e,t){return null!=t&&"points"in e.props}function compareFunnel(e,t){var n,o,i=e.x===(null==t||null===(n=t.labelViewBox)||void 0===n?void 0:n.x)||e.x===t.x,a=e.y===(null==t||null===(o=t.labelViewBox)||void 0===o?void 0:o.y)||e.y===t.y;return i&&a}function comparePie(e,t){var n=e.endAngle===t.endAngle,o=e.startAngle===t.startAngle;return n&&o}function compareScatter(e,t){var n=e.x===t.x,o=e.y===t.y,i=e.z===t.z;return n&&o&&i}function getComparisonFn(e,t){var n;return isFunnel(e,t)?n=compareFunnel:isPie(e,t)?n=comparePie:isScatter(e,t)&&(n=compareScatter),n}function getShapeDataKey(e,t){var n;return isFunnel(e,t)?n="trapezoids":isPie(e,t)?n="sectors":isScatter(e,t)&&(n="points"),n}function getActiveShapeTooltipPayload(e,t){var n,o;return isFunnel(e,t)?null===(n=t.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:isPie(e,t)?null===(o=t.tooltipPayload)||void 0===o||null===(o=o[0])||void 0===o||null===(o=o.payload)||void 0===o?void 0:o.payload:isScatter(e,t)?t.payload:{}}function getActiveShapeIndexForTooltip(e){var t=e.activeTooltipItem,n=e.graphicalItem,o=e.itemData,i=getShapeDataKey(n,t),a=getActiveShapeTooltipPayload(n,t),c=o.filter(function(e,o){var c=p()(a,e),u=n.props[i].filter(function(e){return getComparisonFn(n,t)(e,t)}),s=n.props[i].indexOf(u[u.length-1]);return c&&o===s});return o.indexOf(c[c.length-1])}},9677:function(e,t,n){"use strict";n.d(t,{Ky:function(){return createLabeledScales},O1:function(){return rectWithPoints},_b:function(){return rectWithCoords},t9:function(){return formatAxisMap},xE:function(){return getAngledRectangleWidth}});var o=n(1873),i=n.n(o),a=n(8022),c=n.n(a),u=n(3249),s=n(3843),l=n(7281),f=n(1346);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var formatAxisMap=function(e,t,n,o,i){var a=e.width,c=e.height,p=e.layout,d=e.children,y=Object.keys(t),h={left:n.left,leftMirror:n.left,right:a-n.right,rightMirror:a-n.right,top:n.top,topMirror:n.top,bottom:c-n.bottom,bottomMirror:c-n.bottom},g=!!(0,s.sP)(d,f.$);return y.reduce(function(a,c){var s,f,d,y,b,m=t[c],_=m.orientation,x=m.domain,S=m.padding,P=void 0===S?{}:S,O=m.mirror,j=m.reversed,w="".concat(_).concat(O?"Mirror":"");if("number"===m.type&&("gap"===m.padding||"no-gap"===m.padding)){var C=x[1]-x[0],A=1/0,T=m.categoricalDomain.sort(l.fC);if(T.forEach(function(e,t){t>0&&(A=Math.min((e||0)-(T[t-1]||0),A))}),Number.isFinite(A)){var k=A/C,E="vertical"===m.layout?n.height:n.width;if("gap"===m.padding&&(s=k*E/2),"no-gap"===m.padding){var M=(0,l.h1)(e.barCategoryGap,k*E),I=k*E/2;s=I-M-(I-M)/E*M}}}f="xAxis"===o?[n.left+(P.left||0)+(s||0),n.left+n.width-(P.right||0)-(s||0)]:"yAxis"===o?"horizontal"===p?[n.top+n.height-(P.bottom||0),n.top+(P.top||0)]:[n.top+(P.top||0)+(s||0),n.top+n.height-(P.bottom||0)-(s||0)]:m.range,j&&(f=[f[1],f[0]]);var D=(0,u.Hq)(m,i,g),R=D.scale,L=D.realScaleType;R.domain(x).range(f),(0,u.zF)(R);var N=(0,u.g$)(R,_objectSpread(_objectSpread({},m),{},{realScaleType:L}));"xAxis"===o?(b="top"===_&&!O||"bottom"===_&&O,d=n.left,y=h[w]-b*m.height):"yAxis"===o&&(b="left"===_&&!O||"right"===_&&O,d=h[w]-b*m.width,y=n.top);var B=_objectSpread(_objectSpread(_objectSpread({},m),N),{},{realScaleType:L,x:d,y:y,scale:R,width:"xAxis"===o?n.width:m.width,height:"yAxis"===o?n.height:m.height});return B.bandSize=(0,u.zT)(B,N),m.hide||"xAxis"!==o?m.hide||(h[w]+=(b?-1:1)*B.width):h[w]+=(b?-1:1)*B.height,_objectSpread(_objectSpread({},a),{},_defineProperty({},c,B))},{})},rectWithPoints=function(e,t){var n=e.x,o=e.y,i=t.x,a=t.y;return{x:Math.min(n,i),y:Math.min(o,a),width:Math.abs(i-n),height:Math.abs(a-o)}},rectWithCoords=function(e){return rectWithPoints({x:e.x1,y:e.y1},{x:e.x2,y:e.y2})},p=function(){function ScaleHelper(e){_classCallCheck(this,ScaleHelper),this.scale=e}return _createClass(ScaleHelper,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.bandAware,o=t.position;if(void 0!==e){if(o)switch(o){case"start":default:return this.scale(e);case"middle":var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+i;case"end":var a=this.bandwidth?this.bandwidth():0;return this.scale(e)+a}if(n){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+c}return this.scale(e)}}},{key:"isInRange",value:function(e){var t=this.range(),n=t[0],o=t[t.length-1];return n<=o?e>=n&&e<=o:e>=o&&e<=n}}],[{key:"create",value:function(e){return new ScaleHelper(e)}}])}();_defineProperty(p,"EPS",1e-4);var createLabeledScales=function(e){var t=Object.keys(e).reduce(function(t,n){return _objectSpread(_objectSpread({},t),{},_defineProperty({},n,p.create(e[n])))},{});return _objectSpread(_objectSpread({},t),{},{apply:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=n.bandAware,a=n.position;return i()(e,function(e,n){return t[n].apply(e,{bandAware:o,position:a})})},isInRange:function(e){return c()(e,function(e,n){return t[n].isInRange(e)})}})};function normalizeAngle(e){return(e%180+180)%180}var getAngledRectangleWidth=function(e){var t=e.width,n=e.height,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=normalizeAngle(o)*Math.PI/180,a=Math.atan(n/t);return Math.abs(i>a&&i<Math.PI-a?n/Math.sin(i):t/Math.cos(i))}},3249:function(e,t,n){"use strict";n.d(t,{By:function(){return appendOffsetOfLegend},VO:function(){return calculateActiveTickIndex},zF:function(){return checkDomainOfScale},DO:function(){return combineEventHandlers},Bu:function(){return findPositionOfBar},zT:function(){return getBandSizeOfAxis},qz:function(){return getBarPosition},pt:function(){return getBarSizeList},Yj:function(){return getBaseValueOfBar},Fy:function(){return getCateCoordinateOfBar},Hv:function(){return getCateCoordinateOfLine},Rf:function(){return getCoordinatesOfGrid},gF:function(){return getDomainOfDataByKey},s6:function(){return getDomainOfItemsWithSameAxis},EB:function(){return getDomainOfStackGroups},fk:function(){return getMainColorOfGraphicItem},wh:function(){return getStackGroupsByAxisId},O3:function(){return getStackedDataOfItem},uY:function(){return getTicksOfAxis},g$:function(){return getTicksOfScale},Qo:function(){return getTooltipItem},F$:function(){return getValueByDataKey},NA:function(){return isCategoricalAxis},ko:function(){return parseDomainOfCategoryAxis},ZI:function(){return parseErrorBarsOfAxis},Hq:function(){return parseScale},LG:function(){return parseSpecifiedDomain},Vv:function(){return truncateByDomain}});var o,i,a,c,u,s,l,f={};n.r(f),n.d(f,{scaleBand:function(){return p.Z},scaleDiverging:function(){return diverging},scaleDivergingLog:function(){return divergingLog},scaleDivergingPow:function(){return divergingPow},scaleDivergingSqrt:function(){return divergingSqrt},scaleDivergingSymlog:function(){return divergingSymlog},scaleIdentity:function(){return identity_identity},scaleImplicit:function(){return W.O},scaleLinear:function(){return linear_linear},scaleLog:function(){return log},scaleOrdinal:function(){return W.Z},scalePoint:function(){return p.x},scalePow:function(){return pow},scaleQuantile:function(){return quantile_quantile},scaleQuantize:function(){return quantize},scaleRadial:function(){return radial},scaleSequential:function(){return sequential},scaleSequentialLog:function(){return sequentialLog},scaleSequentialPow:function(){return sequentialPow},scaleSequentialQuantile:function(){return sequentialQuantile},scaleSequentialSqrt:function(){return sequentialSqrt},scaleSequentialSymlog:function(){return sequentialSymlog},scaleSqrt:function(){return sqrt},scaleSymlog:function(){return symlog},scaleThreshold:function(){return threshold},scaleTime:function(){return time},scaleUtc:function(){return utcTime},tickFormat:function(){return tickFormat}});var p=n(5779);let d=Math.sqrt(50),y=Math.sqrt(10),h=Math.sqrt(2);function tickSpec(e,t,n){let o,i,a;let c=(t-e)/Math.max(0,n),u=Math.floor(Math.log10(c)),s=c/Math.pow(10,u),l=s>=d?10:s>=y?5:s>=h?2:1;return(u<0?(o=Math.round(e*(a=Math.pow(10,-u)/l)),i=Math.round(t*a),o/a<e&&++o,i/a>t&&--i,a=-a):(o=Math.round(e/(a=Math.pow(10,u)*l)),i=Math.round(t/a),o*a<e&&++o,i*a>t&&--i),i<o&&.5<=n&&n<2)?tickSpec(e,t,2*n):[o,i,a]}function ticks(e,t,n){if(t=+t,e=+e,!((n=+n)>0))return[];if(e===t)return[e];let o=t<e,[i,a,c]=o?tickSpec(t,e,n):tickSpec(e,t,n);if(!(a>=i))return[];let u=a-i+1,s=Array(u);if(o){if(c<0)for(let e=0;e<u;++e)s[e]=-((a-e)/c);else for(let e=0;e<u;++e)s[e]=(a-e)*c}else if(c<0)for(let e=0;e<u;++e)s[e]=-((i+e)/c);else for(let e=0;e<u;++e)s[e]=(i+e)*c;return s}function tickIncrement(e,t,n){return tickSpec(e=+e,t=+t,n=+n)[2]}function tickStep(e,t,n){t=+t,e=+e,n=+n;let o=t<e,i=o?tickIncrement(t,e,n):tickIncrement(e,t,n);return(o?-1:1)*(i<0?-(1/i):i)}function ascending(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function descending(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function bisector(e){let t,n,o;function left(e,o,i=0,a=e.length){if(i<a){if(0!==t(o,o))return a;do{let t=i+a>>>1;0>n(e[t],o)?i=t+1:a=t}while(i<a)}return i}function center(e,t,n=0,i=e.length){let a=left(e,t,n,i-1);return a>n&&o(e[a-1],t)>-o(e[a],t)?a-1:a}return 2!==e.length?(t=ascending,n=(t,n)=>ascending(e(t),n),o=(t,n)=>e(t)-n):(t=e===ascending||e===descending?e:zero,n=e,o=e),{left,center,right:function(e,o,i=0,a=e.length){if(i<a){if(0!==t(o,o))return a;do{let t=i+a>>>1;0>=n(e[t],o)?i=t+1:a=t}while(i<a)}return i}}}function zero(){return 0}function number_number(e){return null===e?NaN:+e}function*numbers(e,t){if(void 0===t)for(let t of e)null!=t&&(t=+t)>=t&&(yield t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(o=+o)>=o&&(yield o)}}let g=bisector(ascending),b=g.right;function src_define(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function extend(e,t){var n=Object.create(e.prototype);for(var o in t)n[o]=t[o];return n}function Color(){}g.left,bisector(number_number).center;var m="\\s*([+-]?\\d+)\\s*",_="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",x="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",S=/^#([0-9a-f]{3,8})$/,P=RegExp(`^rgb\\(${m},${m},${m}\\)$`),O=RegExp(`^rgb\\(${x},${x},${x}\\)$`),j=RegExp(`^rgba\\(${m},${m},${m},${_}\\)$`),w=RegExp(`^rgba\\(${x},${x},${x},${_}\\)$`),C=RegExp(`^hsl\\(${_},${x},${x}\\)$`),A=RegExp(`^hsla\\(${_},${x},${x},${_}\\)$`),T={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function color_formatHex(){return this.rgb().formatHex()}function color_formatRgb(){return this.rgb().formatRgb()}function color(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=S.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?rgbn(t):3===n?new Rgb(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?rgba(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?rgba(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=P.exec(e))?new Rgb(t[1],t[2],t[3],1):(t=O.exec(e))?new Rgb(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=j.exec(e))?rgba(t[1],t[2],t[3],t[4]):(t=w.exec(e))?rgba(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=C.exec(e))?hsla(t[1],t[2]/100,t[3]/100,1):(t=A.exec(e))?hsla(t[1],t[2]/100,t[3]/100,t[4]):T.hasOwnProperty(e)?rgbn(T[e]):"transparent"===e?new Rgb(NaN,NaN,NaN,0):null}function rgbn(e){return new Rgb(e>>16&255,e>>8&255,255&e,1)}function rgba(e,t,n,o){return o<=0&&(e=t=n=NaN),new Rgb(e,t,n,o)}function rgbConvert(e){return(e instanceof Color||(e=color(e)),e)?new Rgb((e=e.rgb()).r,e.g,e.b,e.opacity):new Rgb}function color_rgb(e,t,n,o){return 1==arguments.length?rgbConvert(e):new Rgb(e,t,n,null==o?1:o)}function Rgb(e,t,n,o){this.r=+e,this.g=+t,this.b=+n,this.opacity=+o}function rgb_formatHex(){return`#${hex(this.r)}${hex(this.g)}${hex(this.b)}`}function rgb_formatHex8(){return`#${hex(this.r)}${hex(this.g)}${hex(this.b)}${hex((isNaN(this.opacity)?1:this.opacity)*255)}`}function rgb_formatRgb(){let e=clampa(this.opacity);return`${1===e?"rgb(":"rgba("}${clampi(this.r)}, ${clampi(this.g)}, ${clampi(this.b)}${1===e?")":`, ${e})`}`}function clampa(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function clampi(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function hex(e){return((e=clampi(e))<16?"0":"")+e.toString(16)}function hsla(e,t,n,o){return o<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Hsl(e,t,n,o)}function hslConvert(e){if(e instanceof Hsl)return new Hsl(e.h,e.s,e.l,e.opacity);if(e instanceof Color||(e=color(e)),!e)return new Hsl;if(e instanceof Hsl)return e;var t=(e=e.rgb()).r/255,n=e.g/255,o=e.b/255,i=Math.min(t,n,o),a=Math.max(t,n,o),c=NaN,u=a-i,s=(a+i)/2;return u?(c=t===a?(n-o)/u+(n<o)*6:n===a?(o-t)/u+2:(t-n)/u+4,u/=s<.5?a+i:2-a-i,c*=60):u=s>0&&s<1?0:c,new Hsl(c,u,s,e.opacity)}function hsl(e,t,n,o){return 1==arguments.length?hslConvert(e):new Hsl(e,t,n,null==o?1:o)}function Hsl(e,t,n,o){this.h=+e,this.s=+t,this.l=+n,this.opacity=+o}function clamph(e){return(e=(e||0)%360)<0?e+360:e}function clampt(e){return Math.max(0,Math.min(1,e||0))}function hsl2rgb(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}function basis(e,t,n,o,i){var a=e*e,c=a*e;return((1-3*e+3*a-c)*t+(4-6*a+3*c)*n+(1+3*e+3*a-3*c)*o+c*i)/6}function src_basis(e){var t=e.length-1;return function(n){var o=n<=0?n=0:n>=1?(n=1,t-1):Math.floor(n*t),i=e[o],a=e[o+1],c=o>0?e[o-1]:2*i-a,u=o<t-1?e[o+2]:2*a-i;return basis((n-o/t)*t,c,i,a,u)}}function basisClosed(e){var t=e.length;return function(n){var o=Math.floor(((n%=1)<0?++n:n)*t),i=e[(o+t-1)%t],a=e[o%t],c=e[(o+1)%t],u=e[(o+2)%t];return basis((n-o/t)*t,i,a,c,u)}}src_define(Color,color,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:color_formatHex,formatHex:color_formatHex,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return hslConvert(this).formatHsl()},formatRgb:color_formatRgb,toString:color_formatRgb}),src_define(Rgb,color_rgb,extend(Color,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new Rgb(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new Rgb(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Rgb(clampi(this.r),clampi(this.g),clampi(this.b),clampa(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:rgb_formatHex,formatHex:rgb_formatHex,formatHex8:rgb_formatHex8,formatRgb:rgb_formatRgb,toString:rgb_formatRgb})),src_define(Hsl,hsl,extend(Color,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new Hsl(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new Hsl(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,o=n+(n<.5?n:1-n)*t,i=2*n-o;return new Rgb(hsl2rgb(e>=240?e-240:e+120,i,o),hsl2rgb(e,i,o),hsl2rgb(e<120?e+240:e-120,i,o),this.opacity)},clamp(){return new Hsl(clamph(this.h),clampt(this.s),clampt(this.l),clampa(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=clampa(this.opacity);return`${1===e?"hsl(":"hsla("}${clamph(this.h)}, ${100*clampt(this.s)}%, ${100*clampt(this.l)}%${1===e?")":`, ${e})`}`}}));var src_constant=e=>()=>e;function linear(e,t){return function(n){return e+n*t}}function exponential(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(o){return Math.pow(e+o*t,n)}}function gamma(e){return 1==(e=+e)?nogamma:function(t,n){return n-t?exponential(t,n,e):src_constant(isNaN(t)?n:t)}}function nogamma(e,t){var n=t-e;return n?linear(e,n):src_constant(isNaN(e)?t:e)}var k=function rgbGamma(e){var t=gamma(e);function rgb(e,n){var o=t((e=color_rgb(e)).r,(n=color_rgb(n)).r),i=t(e.g,n.g),a=t(e.b,n.b),c=nogamma(e.opacity,n.opacity);return function(t){return e.r=o(t),e.g=i(t),e.b=a(t),e.opacity=c(t),e+""}}return rgb.gamma=rgbGamma,rgb}(1);function rgbSpline(e){return function(t){var n,o,i=t.length,a=Array(i),c=Array(i),u=Array(i);for(n=0;n<i;++n)o=color_rgb(t[n]),a[n]=o.r||0,c[n]=o.g||0,u[n]=o.b||0;return a=e(a),c=e(c),u=e(u),o.opacity=1,function(e){return o.r=a(e),o.g=c(e),o.b=u(e),o+""}}}function genericArray(e,t){var n,o=t?t.length:0,i=e?Math.min(o,e.length):0,a=Array(i),c=Array(o);for(n=0;n<i;++n)a[n]=value(e[n],t[n]);for(;n<o;++n)c[n]=t[n];return function(e){for(n=0;n<i;++n)c[n]=a[n](e);return c}}function date(e,t){var n=new Date;return e=+e,t=+t,function(o){return n.setTime(e*(1-o)+t*o),n}}function src_number(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}function object(e,t){var n,o={},i={};for(n in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)n in e?o[n]=value(e[n],t[n]):i[n]=t[n];return function(e){for(n in o)i[n]=o[n](e);return i}}rgbSpline(src_basis),rgbSpline(basisClosed);var E=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,M=RegExp(E.source,"g");function string_zero(e){return function(){return e}}function one(e){return function(t){return e(t)+""}}function string(e,t){var n,o,i,a=E.lastIndex=M.lastIndex=0,c=-1,u=[],s=[];for(e+="",t+="";(n=E.exec(e))&&(o=M.exec(t));)(i=o.index)>a&&(i=t.slice(a,i),u[c]?u[c]+=i:u[++c]=i),(n=n[0])===(o=o[0])?u[c]?u[c]+=o:u[++c]=o:(u[++c]=null,s.push({i:c,x:src_number(n,o)})),a=M.lastIndex;return a<t.length&&(i=t.slice(a),u[c]?u[c]+=i:u[++c]=i),u.length<2?s[0]?one(s[0].x):string_zero(t):(t=s.length,function(e){for(var n,o=0;o<t;++o)u[(n=s[o]).i]=n.x(e);return u.join("")})}function src_numberArray(e,t){t||(t=[]);var n,o=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(n=0;n<o;++n)i[n]=e[n]*(1-a)+t[n]*a;return i}}function numberArray_isNumberArray(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function value(e,t){var n,o=typeof t;return null==t||"boolean"===o?src_constant(t):("number"===o?src_number:"string"===o?(n=color(t))?(t=n,k):string:t instanceof color?k:t instanceof Date?date:numberArray_isNumberArray(t)?src_numberArray:Array.isArray(t)?genericArray:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?object:src_number)(e,t)}function round(e,t){return e=+e,t=+t,function(n){return Math.round(e*(1-n)+t*n)}}function constants(e){return function(){return e}}function src_number_number(e){return+e}var I=[0,1];function identity(e){return e}function normalize(e,t){return(t-=e=+e)?function(n){return(n-e)/t}:constants(isNaN(t)?NaN:.5)}function clamper(e,t){var n;return e>t&&(n=e,e=t,t=n),function(n){return Math.max(e,Math.min(t,n))}}function bimap(e,t,n){var o=e[0],i=e[1],a=t[0],c=t[1];return i<o?(o=normalize(i,o),a=n(c,a)):(o=normalize(o,i),a=n(a,c)),function(e){return a(o(e))}}function polymap(e,t,n){var o=Math.min(e.length,t.length)-1,i=Array(o),a=Array(o),c=-1;for(e[o]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++c<o;)i[c]=normalize(e[c],e[c+1]),a[c]=n(t[c],t[c+1]);return function(t){var n=b(e,t,1,o)-1;return a[n](i[n](t))}}function copy(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function transformer(){var e,t,n,o,i,a,c=I,u=I,s=value,l=identity;function rescale(){var e=Math.min(c.length,u.length);return l!==identity&&(l=clamper(c[0],c[e-1])),o=e>2?polymap:bimap,i=a=null,scale}function scale(t){return null==t||isNaN(t=+t)?n:(i||(i=o(c.map(e),u,s)))(e(l(t)))}return scale.invert=function(n){return l(t((a||(a=o(u,c.map(e),src_number)))(n)))},scale.domain=function(e){return arguments.length?(c=Array.from(e,src_number_number),rescale()):c.slice()},scale.range=function(e){return arguments.length?(u=Array.from(e),rescale()):u.slice()},scale.rangeRound=function(e){return u=Array.from(e),s=round,rescale()},scale.clamp=function(e){return arguments.length?(l=!!e||identity,rescale()):l!==identity},scale.interpolate=function(e){return arguments.length?(s=e,rescale()):s},scale.unknown=function(e){return arguments.length?(n=e,scale):n},function(n,o){return e=n,t=o,rescale()}}function continuous(){return transformer()(identity,identity)}var D=n(3757),R=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function formatSpecifier(e){var t;if(!(t=R.exec(e)))throw Error("invalid format: "+e);return new FormatSpecifier({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function FormatSpecifier(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function formatDecimalParts(e,t){if((n=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var n,o=e.slice(0,n);return[o.length>1?o[0]+o.slice(2):o,+e.slice(n+1)]}function exponent(e){return(e=formatDecimalParts(Math.abs(e)))?e[1]:NaN}function precisionPrefix(e,t){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(exponent(t)/3)))-exponent(Math.abs(e)))}function formatGroup(e,t){return function(n,o){for(var i=n.length,a=[],c=0,u=e[0],s=0;i>0&&u>0&&(s+u+1>o&&(u=Math.max(1,o-s)),a.push(n.substring(i-=u,i+u)),!((s+=u+1)>o));)u=e[c=(c+1)%e.length];return a.reverse().join(t)}}function formatNumerals(e){return function(t){return t.replace(/[0-9]/g,function(t){return e[+t]})}}function formatTrim(e){t:for(var t,n=e.length,o=1,i=-1;o<n;++o)switch(e[o]){case".":i=t=o;break;case"0":0===i&&(i=o),t=o;break;default:if(!+e[o])break t;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}function formatRounded(e,t){var n=formatDecimalParts(e,t);if(!n)return e+"";var o=n[0],i=n[1];return i<0?"0."+Array(-i).join("0")+o:o.length>i+1?o.slice(0,i+1)+"."+o.slice(i+1):o+Array(i-o.length+2).join("0")}formatSpecifier.prototype=FormatSpecifier.prototype,FormatSpecifier.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var L={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>formatRounded(100*e,t),r:formatRounded,s:function(e,t){var n=formatDecimalParts(e,t);if(!n)return e+"";var i=n[0],a=n[1],c=a-(o=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,u=i.length;return c===u?i:c>u?i+Array(c-u+1).join("0"):c>0?i.slice(0,c)+"."+i.slice(c):"0."+Array(1-c).join("0")+formatDecimalParts(e,Math.max(0,t+c-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function src_identity(e){return e}var N=Array.prototype.map,B=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function locale(e){var t=void 0===e.grouping||void 0===e.thousands?src_identity:formatGroup(N.call(e.grouping,Number),e.thousands+""),n=void 0===e.currency?"":e.currency[0]+"",i=void 0===e.currency?"":e.currency[1]+"",a=void 0===e.decimal?".":e.decimal+"",c=void 0===e.numerals?src_identity:formatNumerals(N.call(e.numerals,String)),u=void 0===e.percent?"%":e.percent+"",s=void 0===e.minus?"−":e.minus+"",l=void 0===e.nan?"NaN":e.nan+"";function newFormat(e){var f=(e=formatSpecifier(e)).fill,p=e.align,d=e.sign,y=e.symbol,h=e.zero,g=e.width,b=e.comma,m=e.precision,_=e.trim,x=e.type;"n"===x?(b=!0,x="g"):L[x]||(void 0===m&&(m=12),_=!0,x="g"),(h||"0"===f&&"="===p)&&(h=!0,f="0",p="=");var S="$"===y?n:"#"===y&&/[boxX]/.test(x)?"0"+x.toLowerCase():"",P="$"===y?i:/[%p]/.test(x)?u:"",O=L[x],j=/[defgprs%]/.test(x);function format(e){var n,i,u,y=S,w=P;if("c"===x)w=O(e)+w,e="";else{var C=(e=+e)<0||1/e<0;if(e=isNaN(e)?l:O(Math.abs(e),m),_&&(e=formatTrim(e)),C&&0==+e&&"+"!==d&&(C=!1),y=(C?"("===d?d:s:"-"===d||"("===d?"":d)+y,w=("s"===x?B[8+o/3]:"")+w+(C&&"("===d?")":""),j){for(n=-1,i=e.length;++n<i;)if(48>(u=e.charCodeAt(n))||u>57){w=(46===u?a+e.slice(n+1):e.slice(n))+w,e=e.slice(0,n);break}}}b&&!h&&(e=t(e,1/0));var A=y.length+e.length+w.length,T=A<g?Array(g-A+1).join(f):"";switch(b&&h&&(e=t(T+e,T.length?g-w.length:1/0),T=""),p){case"<":e=y+e+w+T;break;case"=":e=y+T+e+w;break;case"^":e=T.slice(0,A=T.length>>1)+y+e+w+T.slice(A);break;default:e=T+y+e+w}return c(e)}return m=void 0===m?6:/[gprs]/.test(x)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),format.toString=function(){return e+""},format}function formatPrefix(e,t){var n=newFormat(((e=formatSpecifier(e)).type="f",e)),o=3*Math.max(-8,Math.min(8,Math.floor(exponent(t)/3))),i=Math.pow(10,-o),a=B[8+o/3];return function(e){return n(i*e)+a}}return{format:newFormat,formatPrefix:formatPrefix}}function precisionRound(e,t){return Math.max(0,exponent(t=Math.abs(t)-(e=Math.abs(e)))-exponent(e))+1}function precisionFixed(e){return Math.max(0,-exponent(Math.abs(e)))}function tickFormat(e,t,n,o){var i,u=tickStep(e,t,n);switch((o=formatSpecifier(null==o?",f":o)).type){case"s":var s=Math.max(Math.abs(e),Math.abs(t));return null!=o.precision||isNaN(i=precisionPrefix(u,s))||(o.precision=i),c(o,s);case"":case"e":case"g":case"p":case"r":null!=o.precision||isNaN(i=precisionRound(u,Math.max(Math.abs(e),Math.abs(t))))||(o.precision=i-("e"===o.type));break;case"f":case"%":null!=o.precision||isNaN(i=precisionFixed(u))||(o.precision=i-("%"===o.type)*2)}return a(o)}function linearish(e){var t=e.domain;return e.ticks=function(e){var n=t();return ticks(n[0],n[n.length-1],null==e?10:e)},e.tickFormat=function(e,n){var o=t();return tickFormat(o[0],o[o.length-1],null==e?10:e,n)},e.nice=function(n){null==n&&(n=10);var o,i,a=t(),c=0,u=a.length-1,s=a[c],l=a[u],f=10;for(l<s&&(i=s,s=l,l=i,i=c,c=u,u=i);f-- >0;){if((i=tickIncrement(s,l,n))===o)return a[c]=s,a[u]=l,t(a);if(i>0)s=Math.floor(s/i)*i,l=Math.ceil(l/i)*i;else if(i<0)s=Math.ceil(s*i)/i,l=Math.floor(l*i)/i;else break;o=i}return e},e}function linear_linear(){var e=continuous();return e.copy=function(){return copy(e,linear_linear())},D.o.apply(e,arguments),linearish(e)}function identity_identity(e){var t;function scale(e){return null==e||isNaN(e=+e)?t:e}return scale.invert=scale,scale.domain=scale.range=function(t){return arguments.length?(e=Array.from(t,src_number_number),scale):e.slice()},scale.unknown=function(e){return arguments.length?(t=e,scale):t},scale.copy=function(){return identity_identity(e).unknown(t)},e=arguments.length?Array.from(e,src_number_number):[0,1],linearish(scale)}function nice(e,t){e=e.slice();var n,o=0,i=e.length-1,a=e[o],c=e[i];return c<a&&(n=o,o=i,i=n,n=a,a=c,c=n),e[o]=t.floor(a),e[i]=t.ceil(c),e}function transformLog(e){return Math.log(e)}function transformExp(e){return Math.exp(e)}function transformLogn(e){return-Math.log(-e)}function transformExpn(e){return-Math.exp(-e)}function pow10(e){return isFinite(e)?+("1e"+e):e<0?0:e}function powp(e){return 10===e?pow10:e===Math.E?Math.exp:t=>Math.pow(e,t)}function logp(e){return e===Math.E?Math.log:10===e&&Math.log10||2===e&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function reflect(e){return(t,n)=>-e(-t,n)}function loggish(e){let t,n;let o=e(transformLog,transformExp),i=o.domain,c=10;function rescale(){return t=logp(c),n=powp(c),i()[0]<0?(t=reflect(t),n=reflect(n),e(transformLogn,transformExpn)):e(transformLog,transformExp),o}return o.base=function(e){return arguments.length?(c=+e,rescale()):c},o.domain=function(e){return arguments.length?(i(e),rescale()):i()},o.ticks=e=>{let o,a;let u=i(),s=u[0],l=u[u.length-1],f=l<s;f&&([s,l]=[l,s]);let p=t(s),d=t(l),y=null==e?10:+e,h=[];if(!(c%1)&&d-p<y){if(p=Math.floor(p),d=Math.ceil(d),s>0){for(;p<=d;++p)for(o=1;o<c;++o)if(!((a=p<0?o/n(-p):o*n(p))<s)){if(a>l)break;h.push(a)}}else for(;p<=d;++p)for(o=c-1;o>=1;--o)if(!((a=p>0?o/n(-p):o*n(p))<s)){if(a>l)break;h.push(a)}2*h.length<y&&(h=ticks(s,l,y))}else h=ticks(p,d,Math.min(d-p,y)).map(n);return f?h.reverse():h},o.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===c?"s":","),"function"!=typeof i&&(c%1||null!=(i=formatSpecifier(i)).precision||(i.trim=!0),i=a(i)),e===1/0)return i;let u=Math.max(1,c*e/o.ticks().length);return e=>{let o=e/n(Math.round(t(e)));return o*c<c-.5&&(o*=c),o<=u?i(e):""}},o.nice=()=>i(nice(i(),{floor:e=>n(Math.floor(t(e))),ceil:e=>n(Math.ceil(t(e)))})),o}function log(){let e=loggish(transformer()).domain([1,10]);return e.copy=()=>copy(e,log()).base(e.base()),D.o.apply(e,arguments),e}function transformSymlog(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function transformSymexp(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function symlogish(e){var t=1,n=e(transformSymlog(1),transformSymexp(t));return n.constant=function(n){return arguments.length?e(transformSymlog(t=+n),transformSymexp(t)):t},linearish(n)}function symlog(){var e=symlogish(transformer());return e.copy=function(){return copy(e,symlog()).constant(e.constant())},D.o.apply(e,arguments)}a=(i=locale({thousands:",",grouping:[3],currency:["$",""]})).format,c=i.formatPrefix;var W=n(4713);function transformPow(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function transformSqrt(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function transformSquare(e){return e<0?-e*e:e*e}function powish(e){var t=e(identity,identity),n=1;function rescale(){return 1===n?e(identity,identity):.5===n?e(transformSqrt,transformSquare):e(transformPow(n),transformPow(1/n))}return t.exponent=function(e){return arguments.length?(n=+e,rescale()):n},linearish(t)}function pow(){var e=powish(transformer());return e.copy=function(){return copy(e,pow()).exponent(e.exponent())},D.o.apply(e,arguments),e}function sqrt(){return pow.apply(null,arguments).exponent(.5)}function square(e){return Math.sign(e)*e*e}function unsquare(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function radial(){var e,t=continuous(),n=[0,1],o=!1;function scale(n){var i=unsquare(t(n));return isNaN(i)?e:o?Math.round(i):i}return scale.invert=function(e){return t.invert(square(e))},scale.domain=function(e){return arguments.length?(t.domain(e),scale):t.domain()},scale.range=function(e){return arguments.length?(t.range((n=Array.from(e,src_number_number)).map(square)),scale):n.slice()},scale.rangeRound=function(e){return scale.range(e).round(!0)},scale.round=function(e){return arguments.length?(o=!!e,scale):o},scale.clamp=function(e){return arguments.length?(t.clamp(e),scale):t.clamp()},scale.unknown=function(t){return arguments.length?(e=t,scale):e},scale.copy=function(){return radial(t.domain(),n).round(o).clamp(t.clamp()).unknown(e)},D.o.apply(scale,arguments),linearish(scale)}function max(e,t){let n;if(void 0===t)for(let t of e)null!=t&&(n<t||void 0===n&&t>=t)&&(n=t);else{let o=-1;for(let i of e)null!=(i=t(i,++o,e))&&(n<i||void 0===n&&i>=i)&&(n=i)}return n}function min(e,t){let n;if(void 0===t)for(let t of e)null!=t&&(n>t||void 0===n&&t>=t)&&(n=t);else{let o=-1;for(let i of e)null!=(i=t(i,++o,e))&&(n>i||void 0===n&&i>=i)&&(n=i)}return n}function compareDefined(e=ascending){if(e===ascending)return sort_ascendingDefined;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,n)=>{let o=e(t,n);return o||0===o?o:(0===e(n,n))-(0===e(t,t))}}function sort_ascendingDefined(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:e>t?1:0)}function quickselect_quickselect(e,t,n=0,o=1/0,i){if(t=Math.floor(t),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=t&&t<=o))return e;for(i=void 0===i?sort_ascendingDefined:compareDefined(i);o>n;){if(o-n>600){let a=o-n+1,c=t-n+1,u=Math.log(a),s=.5*Math.exp(2*u/3),l=.5*Math.sqrt(u*s*(a-s)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(t-c*s/a+l)),p=Math.min(o,Math.floor(t+(a-c)*s/a+l));quickselect_quickselect(e,t,f,p,i)}let a=e[t],c=n,u=o;for(swap(e,n,t),i(e[o],a)>0&&swap(e,n,o);c<u;){for(swap(e,c,u),++c,--u;0>i(e[c],a);)++c;for(;i(e[u],a)>0;)--u}0===i(e[n],a)?swap(e,n,u):swap(e,++u,o),u<=t&&(n=u+1),t<=u&&(o=u-1)}return e}function swap(e,t,n){let o=e[t];e[t]=e[n],e[n]=o}function quantile(e,t,n){if(!(!(o=(e=Float64Array.from(numbers(e,n))).length)||isNaN(t=+t))){if(t<=0||o<2)return min(e);if(t>=1)return max(e);var o,i=(o-1)*t,a=Math.floor(i),c=max(quickselect_quickselect(e,a).subarray(0,a+1));return c+(min(e.subarray(a+1))-c)*(i-a)}}function quantileSorted(e,t,n=number_number){if(!(!(o=e.length)||isNaN(t=+t))){if(t<=0||o<2)return+n(e[0],0,e);if(t>=1)return+n(e[o-1],o-1,e);var o,i=(o-1)*t,a=Math.floor(i),c=+n(e[a],a,e);return c+(+n(e[a+1],a+1,e)-c)*(i-a)}}function quantile_quantile(){var e,t=[],n=[],o=[];function rescale(){var e=0,i=Math.max(1,n.length);for(o=Array(i-1);++e<i;)o[e-1]=quantileSorted(t,e/i);return scale}function scale(t){return null==t||isNaN(t=+t)?e:n[b(o,t)]}return scale.invertExtent=function(e){var i=n.indexOf(e);return i<0?[NaN,NaN]:[i>0?o[i-1]:t[0],i<o.length?o[i]:t[t.length-1]]},scale.domain=function(e){if(!arguments.length)return t.slice();for(let n of(t=[],e))null==n||isNaN(n=+n)||t.push(n);return t.sort(ascending),rescale()},scale.range=function(e){return arguments.length?(n=Array.from(e),rescale()):n.slice()},scale.unknown=function(t){return arguments.length?(e=t,scale):e},scale.quantiles=function(){return o.slice()},scale.copy=function(){return quantile_quantile().domain(t).range(n).unknown(e)},D.o.apply(scale,arguments)}function quantize(){var e,t=0,n=1,o=1,i=[.5],a=[0,1];function scale(t){return null!=t&&t<=t?a[b(i,t,0,o)]:e}function rescale(){var e=-1;for(i=Array(o);++e<o;)i[e]=((e+1)*n-(e-o)*t)/(o+1);return scale}return scale.domain=function(e){return arguments.length?([t,n]=e,t=+t,n=+n,rescale()):[t,n]},scale.range=function(e){return arguments.length?(o=(a=Array.from(e)).length-1,rescale()):a.slice()},scale.invertExtent=function(e){var c=a.indexOf(e);return c<0?[NaN,NaN]:c<1?[t,i[0]]:c>=o?[i[o-1],n]:[i[c-1],i[c]]},scale.unknown=function(t){return arguments.length&&(e=t),scale},scale.thresholds=function(){return i.slice()},scale.copy=function(){return quantize().domain([t,n]).range(a).unknown(e)},D.o.apply(linearish(scale),arguments)}function threshold(){var e,t=[.5],n=[0,1],o=1;function scale(i){return null!=i&&i<=i?n[b(t,i,0,o)]:e}return scale.domain=function(e){return arguments.length?(o=Math.min((t=Array.from(e)).length,n.length-1),scale):t.slice()},scale.range=function(e){return arguments.length?(n=Array.from(e),o=Math.min(t.length,n.length-1),scale):n.slice()},scale.invertExtent=function(e){var o=n.indexOf(e);return[t[o-1],t[o]]},scale.unknown=function(t){return arguments.length?(e=t,scale):e},scale.copy=function(){return threshold().domain(t).range(n).unknown(e)},D.o.apply(scale,arguments)}let U=864e5,z=7*U,K=30*U,F=365*U,H=new Date,Z=new Date;function timeInterval(e,t,n,o){function interval(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return interval.floor=t=>(e(t=new Date(+t)),t),interval.ceil=n=>(e(n=new Date(n-1)),t(n,1),e(n),n),interval.round=e=>{let t=interval(e),n=interval.ceil(e);return e-t<n-e?t:n},interval.offset=(e,n)=>(t(e=new Date(+e),null==n?1:Math.floor(n)),e),interval.range=(n,o,i)=>{let a;let c=[];if(n=interval.ceil(n),i=null==i?1:Math.floor(i),!(n<o)||!(i>0))return c;do c.push(a=new Date(+n)),t(n,i),e(n);while(a<n&&n<o);return c},interval.filter=n=>timeInterval(t=>{if(t>=t)for(;e(t),!n(t);)t.setTime(t-1)},(e,o)=>{if(e>=e){if(o<0)for(;++o<=0;)for(;t(e,-1),!n(e););else for(;--o>=0;)for(;t(e,1),!n(e););}}),n&&(interval.count=(t,o)=>(H.setTime(+t),Z.setTime(+o),e(H),e(Z),Math.floor(n(H,Z))),interval.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?interval.filter(o?t=>o(t)%e==0:t=>interval.count(0,t)%e==0):interval:null),interval}let $=timeInterval(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);$.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?timeInterval(t=>{t.setTime(Math.floor(t/e)*e)},(t,n)=>{t.setTime(+t+n*e)},(t,n)=>(n-t)/e):$:null,$.range;let V=timeInterval(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());V.range;let Y=timeInterval(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());Y.range;let G=timeInterval(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());G.range;let X=timeInterval(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());X.range;let Q=timeInterval(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());Q.range;let J=timeInterval(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/U,e=>e.getDate()-1);J.range;let ee=timeInterval(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/U,e=>e.getUTCDate()-1);ee.range;let et=timeInterval(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/U,e=>Math.floor(e/U));function timeWeekday(e){return timeInterval(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/z)}et.range;let er=timeWeekday(0),en=timeWeekday(1),eo=timeWeekday(2),ei=timeWeekday(3),ea=timeWeekday(4),ec=timeWeekday(5),eu=timeWeekday(6);function utcWeekday(e){return timeInterval(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/z)}er.range,en.range,eo.range,ei.range,ea.range,ec.range,eu.range;let es=utcWeekday(0),el=utcWeekday(1),ef=utcWeekday(2),ep=utcWeekday(3),ed=utcWeekday(4),ey=utcWeekday(5),eh=utcWeekday(6);es.range,el.range,ef.range,ep.range,ed.range,ey.range,eh.range;let eg=timeInterval(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());eg.range;let eb=timeInterval(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());eb.range;let em=timeInterval(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());em.every=e=>isFinite(e=Math.floor(e))&&e>0?timeInterval(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,n)=>{t.setFullYear(t.getFullYear()+n*e)}):null,em.range;let ev=timeInterval(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function ticker(e,t,n,o,i,a){let c=[[V,1,1e3],[V,5,5e3],[V,15,15e3],[V,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[o,1,U],[o,2,2*U],[n,1,z],[t,1,K],[t,3,3*K],[e,1,F]];function tickInterval(t,n,o){let i=Math.abs(n-t)/o,a=bisector(([,,e])=>e).right(c,i);if(a===c.length)return e.every(tickStep(t/F,n/F,o));if(0===a)return $.every(Math.max(tickStep(t,n,o),1));let[u,s]=c[i/c[a-1][2]<c[a][2]/i?a-1:a];return u.every(s)}return[function(e,t,n){let o=t<e;o&&([e,t]=[t,e]);let i=n&&"function"==typeof n.range?n:tickInterval(e,t,n),a=i?i.range(e,+t+1):[];return o?a.reverse():a},tickInterval]}ev.every=e=>isFinite(e=Math.floor(e))&&e>0?timeInterval(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n*e)}):null,ev.range;let[e_,ex]=ticker(ev,eb,es,et,Q,G),[eS,eP]=ticker(em,eg,er,J,X,Y);function localDate(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function utcDate(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function newDate(e,t,n){return{y:e,m:t,d:n,H:0,M:0,S:0,L:0}}function formatLocale(e){var t=e.dateTime,n=e.date,o=e.time,i=e.periods,a=e.days,c=e.shortDays,u=e.months,s=e.shortMonths,l=formatRe(i),f=formatLookup(i),p=formatRe(a),d=formatLookup(a),y=formatRe(c),h=formatLookup(c),g=formatRe(u),b=formatLookup(u),m=formatRe(s),_=formatLookup(s),x={a:formatShortWeekday,A:formatWeekday,b:formatShortMonth,B:formatMonth,c:null,d:formatDayOfMonth,e:formatDayOfMonth,f:formatMicroseconds,g:formatYearISO,G:formatFullYearISO,H:formatHour24,I:formatHour12,j:formatDayOfYear,L:formatMilliseconds,m:formatMonthNumber,M:formatMinutes,p:formatPeriod,q:formatQuarter,Q:formatUnixTimestamp,s:formatUnixTimestampSeconds,S:formatSeconds,u:formatWeekdayNumberMonday,U:formatWeekNumberSunday,V:formatWeekNumberISO,w:formatWeekdayNumberSunday,W:formatWeekNumberMonday,x:null,X:null,y:formatYear,Y:formatFullYear,Z:formatZone,"%":formatLiteralPercent},S={a:formatUTCShortWeekday,A:formatUTCWeekday,b:formatUTCShortMonth,B:formatUTCMonth,c:null,d:formatUTCDayOfMonth,e:formatUTCDayOfMonth,f:formatUTCMicroseconds,g:formatUTCYearISO,G:formatUTCFullYearISO,H:formatUTCHour24,I:formatUTCHour12,j:formatUTCDayOfYear,L:formatUTCMilliseconds,m:formatUTCMonthNumber,M:formatUTCMinutes,p:formatUTCPeriod,q:formatUTCQuarter,Q:formatUnixTimestamp,s:formatUnixTimestampSeconds,S:formatUTCSeconds,u:formatUTCWeekdayNumberMonday,U:formatUTCWeekNumberSunday,V:formatUTCWeekNumberISO,w:formatUTCWeekdayNumberSunday,W:formatUTCWeekNumberMonday,x:null,X:null,y:formatUTCYear,Y:formatUTCFullYear,Z:formatUTCZone,"%":formatLiteralPercent},P={a:parseShortWeekday,A:parseWeekday,b:parseShortMonth,B:parseMonth,c:parseLocaleDateTime,d:parseDayOfMonth,e:parseDayOfMonth,f:parseMicroseconds,g:parseYear,G:parseFullYear,H:parseHour24,I:parseHour24,j:parseDayOfYear,L:parseMilliseconds,m:parseMonthNumber,M:parseMinutes,p:parsePeriod,q:parseQuarter,Q:parseUnixTimestamp,s:parseUnixTimestampSeconds,S:parseSeconds,u:parseWeekdayNumberMonday,U:parseWeekNumberSunday,V:parseWeekNumberISO,w:parseWeekdayNumberSunday,W:parseWeekNumberMonday,x:parseLocaleDate,X:parseLocaleTime,y:parseYear,Y:parseFullYear,Z:parseZone,"%":parseLiteralPercent};function newFormat(e,t){return function(n){var o,i,a,c=[],u=-1,s=0,l=e.length;for(n instanceof Date||(n=new Date(+n));++u<l;)37===e.charCodeAt(u)&&(c.push(e.slice(s,u)),null!=(i=eO[o=e.charAt(++u)])?o=e.charAt(++u):i="e"===o?" ":"0",(a=t[o])&&(o=a(n,i)),c.push(o),s=u+1);return c.push(e.slice(s,u)),c.join("")}}function newParse(e,t){return function(n){var o,i,a=newDate(1900,void 0,1);if(parseSpecifier(a,e,n+="",0)!=n.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(o=(i=(o=utcDate(newDate(a.y,0,1))).getUTCDay())>4||0===i?el.ceil(o):el(o),o=ee.offset(o,(a.V-1)*7),a.y=o.getUTCFullYear(),a.m=o.getUTCMonth(),a.d=o.getUTCDate()+(a.w+6)%7):(o=(i=(o=localDate(newDate(a.y,0,1))).getDay())>4||0===i?en.ceil(o):en(o),o=J.offset(o,(a.V-1)*7),a.y=o.getFullYear(),a.m=o.getMonth(),a.d=o.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:"W"in a?1:0),i="Z"in a?utcDate(newDate(a.y,0,1)).getUTCDay():localDate(newDate(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,utcDate(a)):localDate(a)}}function parseSpecifier(e,t,n,o){for(var i,a,c=0,u=t.length,s=n.length;c<u;){if(o>=s)return -1;if(37===(i=t.charCodeAt(c++))){if(!(a=P[(i=t.charAt(c++))in eO?t.charAt(c++):i])||(o=a(e,n,o))<0)return -1}else if(i!=n.charCodeAt(o++))return -1}return o}function parsePeriod(e,t,n){var o=l.exec(t.slice(n));return o?(e.p=f.get(o[0].toLowerCase()),n+o[0].length):-1}function parseShortWeekday(e,t,n){var o=y.exec(t.slice(n));return o?(e.w=h.get(o[0].toLowerCase()),n+o[0].length):-1}function parseWeekday(e,t,n){var o=p.exec(t.slice(n));return o?(e.w=d.get(o[0].toLowerCase()),n+o[0].length):-1}function parseShortMonth(e,t,n){var o=m.exec(t.slice(n));return o?(e.m=_.get(o[0].toLowerCase()),n+o[0].length):-1}function parseMonth(e,t,n){var o=g.exec(t.slice(n));return o?(e.m=b.get(o[0].toLowerCase()),n+o[0].length):-1}function parseLocaleDateTime(e,n,o){return parseSpecifier(e,t,n,o)}function parseLocaleDate(e,t,o){return parseSpecifier(e,n,t,o)}function parseLocaleTime(e,t,n){return parseSpecifier(e,o,t,n)}function formatShortWeekday(e){return c[e.getDay()]}function formatWeekday(e){return a[e.getDay()]}function formatShortMonth(e){return s[e.getMonth()]}function formatMonth(e){return u[e.getMonth()]}function formatPeriod(e){return i[+(e.getHours()>=12)]}function formatQuarter(e){return 1+~~(e.getMonth()/3)}function formatUTCShortWeekday(e){return c[e.getUTCDay()]}function formatUTCWeekday(e){return a[e.getUTCDay()]}function formatUTCShortMonth(e){return s[e.getUTCMonth()]}function formatUTCMonth(e){return u[e.getUTCMonth()]}function formatUTCPeriod(e){return i[+(e.getUTCHours()>=12)]}function formatUTCQuarter(e){return 1+~~(e.getUTCMonth()/3)}return x.x=newFormat(n,x),x.X=newFormat(o,x),x.c=newFormat(t,x),S.x=newFormat(n,S),S.X=newFormat(o,S),S.c=newFormat(t,S),{format:function(e){var t=newFormat(e+="",x);return t.toString=function(){return e},t},parse:function(e){var t=newParse(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=newFormat(e+="",S);return t.toString=function(){return e},t},utcParse:function(e){var t=newParse(e+="",!0);return t.toString=function(){return e},t}}}var eO={"-":"",_:" ",0:"0"},ej=/^\s*\d+/,ew=/^%/,eC=/[\\^$*+?|[\]().{}]/g;function pad(e,t,n){var o=e<0?"-":"",i=(o?-e:e)+"",a=i.length;return o+(a<n?Array(n-a+1).join(t)+i:i)}function requote(e){return e.replace(eC,"\\$&")}function formatRe(e){return RegExp("^(?:"+e.map(requote).join("|")+")","i")}function formatLookup(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function parseWeekdayNumberSunday(e,t,n){var o=ej.exec(t.slice(n,n+1));return o?(e.w=+o[0],n+o[0].length):-1}function parseWeekdayNumberMonday(e,t,n){var o=ej.exec(t.slice(n,n+1));return o?(e.u=+o[0],n+o[0].length):-1}function parseWeekNumberSunday(e,t,n){var o=ej.exec(t.slice(n,n+2));return o?(e.U=+o[0],n+o[0].length):-1}function parseWeekNumberISO(e,t,n){var o=ej.exec(t.slice(n,n+2));return o?(e.V=+o[0],n+o[0].length):-1}function parseWeekNumberMonday(e,t,n){var o=ej.exec(t.slice(n,n+2));return o?(e.W=+o[0],n+o[0].length):-1}function parseFullYear(e,t,n){var o=ej.exec(t.slice(n,n+4));return o?(e.y=+o[0],n+o[0].length):-1}function parseYear(e,t,n){var o=ej.exec(t.slice(n,n+2));return o?(e.y=+o[0]+(+o[0]>68?1900:2e3),n+o[0].length):-1}function parseZone(e,t,n){var o=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(n,n+6));return o?(e.Z=o[1]?0:-(o[2]+(o[3]||"00")),n+o[0].length):-1}function parseQuarter(e,t,n){var o=ej.exec(t.slice(n,n+1));return o?(e.q=3*o[0]-3,n+o[0].length):-1}function parseMonthNumber(e,t,n){var o=ej.exec(t.slice(n,n+2));return o?(e.m=o[0]-1,n+o[0].length):-1}function parseDayOfMonth(e,t,n){var o=ej.exec(t.slice(n,n+2));return o?(e.d=+o[0],n+o[0].length):-1}function parseDayOfYear(e,t,n){var o=ej.exec(t.slice(n,n+3));return o?(e.m=0,e.d=+o[0],n+o[0].length):-1}function parseHour24(e,t,n){var o=ej.exec(t.slice(n,n+2));return o?(e.H=+o[0],n+o[0].length):-1}function parseMinutes(e,t,n){var o=ej.exec(t.slice(n,n+2));return o?(e.M=+o[0],n+o[0].length):-1}function parseSeconds(e,t,n){var o=ej.exec(t.slice(n,n+2));return o?(e.S=+o[0],n+o[0].length):-1}function parseMilliseconds(e,t,n){var o=ej.exec(t.slice(n,n+3));return o?(e.L=+o[0],n+o[0].length):-1}function parseMicroseconds(e,t,n){var o=ej.exec(t.slice(n,n+6));return o?(e.L=Math.floor(o[0]/1e3),n+o[0].length):-1}function parseLiteralPercent(e,t,n){var o=ew.exec(t.slice(n,n+1));return o?n+o[0].length:-1}function parseUnixTimestamp(e,t,n){var o=ej.exec(t.slice(n));return o?(e.Q=+o[0],n+o[0].length):-1}function parseUnixTimestampSeconds(e,t,n){var o=ej.exec(t.slice(n));return o?(e.s=+o[0],n+o[0].length):-1}function formatDayOfMonth(e,t){return pad(e.getDate(),t,2)}function formatHour24(e,t){return pad(e.getHours(),t,2)}function formatHour12(e,t){return pad(e.getHours()%12||12,t,2)}function formatDayOfYear(e,t){return pad(1+J.count(em(e),e),t,3)}function formatMilliseconds(e,t){return pad(e.getMilliseconds(),t,3)}function formatMicroseconds(e,t){return formatMilliseconds(e,t)+"000"}function formatMonthNumber(e,t){return pad(e.getMonth()+1,t,2)}function formatMinutes(e,t){return pad(e.getMinutes(),t,2)}function formatSeconds(e,t){return pad(e.getSeconds(),t,2)}function formatWeekdayNumberMonday(e){var t=e.getDay();return 0===t?7:t}function formatWeekNumberSunday(e,t){return pad(er.count(em(e)-1,e),t,2)}function dISO(e){var t=e.getDay();return t>=4||0===t?ea(e):ea.ceil(e)}function formatWeekNumberISO(e,t){return e=dISO(e),pad(ea.count(em(e),e)+(4===em(e).getDay()),t,2)}function formatWeekdayNumberSunday(e){return e.getDay()}function formatWeekNumberMonday(e,t){return pad(en.count(em(e)-1,e),t,2)}function formatYear(e,t){return pad(e.getFullYear()%100,t,2)}function formatYearISO(e,t){return pad((e=dISO(e)).getFullYear()%100,t,2)}function formatFullYear(e,t){return pad(e.getFullYear()%1e4,t,4)}function formatFullYearISO(e,t){var n=e.getDay();return pad((e=n>=4||0===n?ea(e):ea.ceil(e)).getFullYear()%1e4,t,4)}function formatZone(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+pad(t/60|0,"0",2)+pad(t%60,"0",2)}function formatUTCDayOfMonth(e,t){return pad(e.getUTCDate(),t,2)}function formatUTCHour24(e,t){return pad(e.getUTCHours(),t,2)}function formatUTCHour12(e,t){return pad(e.getUTCHours()%12||12,t,2)}function formatUTCDayOfYear(e,t){return pad(1+ee.count(ev(e),e),t,3)}function formatUTCMilliseconds(e,t){return pad(e.getUTCMilliseconds(),t,3)}function formatUTCMicroseconds(e,t){return formatUTCMilliseconds(e,t)+"000"}function formatUTCMonthNumber(e,t){return pad(e.getUTCMonth()+1,t,2)}function formatUTCMinutes(e,t){return pad(e.getUTCMinutes(),t,2)}function formatUTCSeconds(e,t){return pad(e.getUTCSeconds(),t,2)}function formatUTCWeekdayNumberMonday(e){var t=e.getUTCDay();return 0===t?7:t}function formatUTCWeekNumberSunday(e,t){return pad(es.count(ev(e)-1,e),t,2)}function UTCdISO(e){var t=e.getUTCDay();return t>=4||0===t?ed(e):ed.ceil(e)}function formatUTCWeekNumberISO(e,t){return e=UTCdISO(e),pad(ed.count(ev(e),e)+(4===ev(e).getUTCDay()),t,2)}function formatUTCWeekdayNumberSunday(e){return e.getUTCDay()}function formatUTCWeekNumberMonday(e,t){return pad(el.count(ev(e)-1,e),t,2)}function formatUTCYear(e,t){return pad(e.getUTCFullYear()%100,t,2)}function formatUTCYearISO(e,t){return pad((e=UTCdISO(e)).getUTCFullYear()%100,t,2)}function formatUTCFullYear(e,t){return pad(e.getUTCFullYear()%1e4,t,4)}function formatUTCFullYearISO(e,t){var n=e.getUTCDay();return pad((e=n>=4||0===n?ed(e):ed.ceil(e)).getUTCFullYear()%1e4,t,4)}function formatUTCZone(){return"+0000"}function formatLiteralPercent(){return"%"}function formatUnixTimestamp(e){return+e}function formatUnixTimestampSeconds(e){return Math.floor(+e/1e3)}function time_date(e){return new Date(e)}function time_number(e){return e instanceof Date?+e:+new Date(+e)}function calendar(e,t,n,o,i,a,c,u,s,l){var f=continuous(),p=f.invert,d=f.domain,y=l(".%L"),h=l(":%S"),g=l("%I:%M"),b=l("%I %p"),m=l("%a %d"),_=l("%b %d"),x=l("%B"),S=l("%Y");function tickFormat(e){return(s(e)<e?y:u(e)<e?h:c(e)<e?g:a(e)<e?b:o(e)<e?i(e)<e?m:_:n(e)<e?x:S)(e)}return f.invert=function(e){return new Date(p(e))},f.domain=function(e){return arguments.length?d(Array.from(e,time_number)):d().map(time_date)},f.ticks=function(t){var n=d();return e(n[0],n[n.length-1],null==t?10:t)},f.tickFormat=function(e,t){return null==t?tickFormat:l(t)},f.nice=function(e){var n=d();return e&&"function"==typeof e.range||(e=t(n[0],n[n.length-1],null==e?10:e)),e?d(nice(n,e)):f},f.copy=function(){return copy(f,calendar(e,t,n,o,i,a,c,u,s,l))},f}function time(){return D.o.apply(calendar(eS,eP,em,eg,er,J,X,Y,V,s).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function utcTime(){return D.o.apply(calendar(e_,ex,ev,eb,es,ee,Q,G,V,l).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function sequential_transformer(){var e,t,n,o,i,a=0,c=1,u=identity,s=!1;function scale(t){return null==t||isNaN(t=+t)?i:u(0===n?.5:(t=(o(t)-e)*n,s?Math.max(0,Math.min(1,t)):t))}function range(e){return function(t){var n,o;return arguments.length?([n,o]=t,u=e(n,o),scale):[u(0),u(1)]}}return scale.domain=function(i){return arguments.length?([a,c]=i,e=o(a=+a),t=o(c=+c),n=e===t?0:1/(t-e),scale):[a,c]},scale.clamp=function(e){return arguments.length?(s=!!e,scale):s},scale.interpolator=function(e){return arguments.length?(u=e,scale):u},scale.range=range(value),scale.rangeRound=range(round),scale.unknown=function(e){return arguments.length?(i=e,scale):i},function(i){return o=i,e=i(a),t=i(c),n=e===t?0:1/(t-e),scale}}function sequential_copy(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function sequential(){var e=linearish(sequential_transformer()(identity));return e.copy=function(){return sequential_copy(e,sequential())},D.O.apply(e,arguments)}function sequentialLog(){var e=loggish(sequential_transformer()).domain([1,10]);return e.copy=function(){return sequential_copy(e,sequentialLog()).base(e.base())},D.O.apply(e,arguments)}function sequentialSymlog(){var e=symlogish(sequential_transformer());return e.copy=function(){return sequential_copy(e,sequentialSymlog()).constant(e.constant())},D.O.apply(e,arguments)}function sequentialPow(){var e=powish(sequential_transformer());return e.copy=function(){return sequential_copy(e,sequentialPow()).exponent(e.exponent())},D.O.apply(e,arguments)}function sequentialSqrt(){return sequentialPow.apply(null,arguments).exponent(.5)}function sequentialQuantile(){var e=[],t=identity;function scale(n){if(null!=n&&!isNaN(n=+n))return t((b(e,n,1)-1)/(e.length-1))}return scale.domain=function(t){if(!arguments.length)return e.slice();for(let n of(e=[],t))null==n||isNaN(n=+n)||e.push(n);return e.sort(ascending),scale},scale.interpolator=function(e){return arguments.length?(t=e,scale):t},scale.range=function(){return e.map((n,o)=>t(o/(e.length-1)))},scale.quantiles=function(t){return Array.from({length:t+1},(n,o)=>quantile(e,o/t))},scale.copy=function(){return sequentialQuantile(t).domain(e)},D.O.apply(scale,arguments)}function piecewise(e,t){void 0===t&&(t=e,e=value);for(var n=0,o=t.length-1,i=t[0],a=Array(o<0?0:o);n<o;)a[n]=e(i,i=t[++n]);return function(e){var t=Math.max(0,Math.min(o-1,Math.floor(e*=o)));return a[t](e-t)}}function diverging_transformer(){var e,t,n,o,i,a,c,u=0,s=.5,l=1,f=1,p=identity,d=!1;function scale(e){return isNaN(e=+e)?c:(e=.5+((e=+a(e))-t)*(f*e<f*t?o:i),p(d?Math.max(0,Math.min(1,e)):e))}function range(e){return function(t){var n,o,i;return arguments.length?([n,o,i]=t,p=piecewise(e,[n,o,i]),scale):[p(0),p(.5),p(1)]}}return scale.domain=function(c){return arguments.length?([u,s,l]=c,e=a(u=+u),t=a(s=+s),n=a(l=+l),o=e===t?0:.5/(t-e),i=t===n?0:.5/(n-t),f=t<e?-1:1,scale):[u,s,l]},scale.clamp=function(e){return arguments.length?(d=!!e,scale):d},scale.interpolator=function(e){return arguments.length?(p=e,scale):p},scale.range=range(value),scale.rangeRound=range(round),scale.unknown=function(e){return arguments.length?(c=e,scale):c},function(c){return a=c,e=c(u),t=c(s),n=c(l),o=e===t?0:.5/(t-e),i=t===n?0:.5/(n-t),f=t<e?-1:1,scale}}function diverging(){var e=linearish(diverging_transformer()(identity));return e.copy=function(){return sequential_copy(e,diverging())},D.O.apply(e,arguments)}function divergingLog(){var e=loggish(diverging_transformer()).domain([.1,1,10]);return e.copy=function(){return sequential_copy(e,divergingLog()).base(e.base())},D.O.apply(e,arguments)}function divergingSymlog(){var e=symlogish(diverging_transformer());return e.copy=function(){return sequential_copy(e,divergingSymlog()).constant(e.constant())},D.O.apply(e,arguments)}function divergingPow(){var e=powish(diverging_transformer());return e.copy=function(){return sequential_copy(e,divergingPow()).exponent(e.exponent())},D.O.apply(e,arguments)}function divergingSqrt(){return divergingPow.apply(null,arguments).exponent(.5)}function none(e,t){if((i=e.length)>1)for(var n,o,i,a=1,c=e[t[0]],u=c.length;a<i;++a)for(o=c,c=e[t[a]],n=0;n<u;++n)c[n][1]+=c[n][0]=isNaN(o[n][1])?o[n][0]:o[n][1]}function expand(e,t){if((o=e.length)>0){for(var n,o,i,a=0,c=e[0].length;a<c;++a){for(i=n=0;n<o;++n)i+=e[n][a][1]||0;if(i)for(n=0;n<o;++n)e[n][a][1]/=i}none(e,t)}}function silhouette(e,t){if((n=e.length)>0){for(var n,o=0,i=e[t[0]],a=i.length;o<a;++o){for(var c=0,u=0;c<n;++c)u+=e[c][o][1]||0;i[o][1]+=i[o][0]=-u/2}none(e,t)}}function wiggle(e,t){if((i=e.length)>0&&(o=(n=e[t[0]]).length)>0){for(var n,o,i,a=0,c=1;c<o;++c){for(var u=0,s=0,l=0;u<i;++u){for(var f=e[t[u]],p=f[c][1]||0,d=(p-(f[c-1][1]||0))/2,y=0;y<u;++y){var h=e[t[y]];d+=(h[c][1]||0)-(h[c-1][1]||0)}s+=p,l+=d*p}n[c-1][1]+=n[c-1][0]=a,s&&(a-=l/s)}n[c-1][1]+=n[c-1][0]=a,none(e,t)}}s=(u=formatLocale({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,u.parse,l=u.utcFormat,u.utcParse;var eA=n(3841),eT=n(4299);function order_none(e){for(var t=e.length,n=Array(t);--t>=0;)n[t]=t;return n}function stackValue(e,t){return e[t]}function stackSeries(e){let t=[];return t.key=e,t}function src_stack(){var e=(0,eT.Z)([]),t=order_none,n=none,o=stackValue;function stack(i){var a,c,u=Array.from(e.apply(this,arguments),stackSeries),s=u.length,l=-1;for(let e of i)for(a=0,++l;a<s;++a)(u[a][l]=[0,+o(e,u[a].key,l,i)]).data=e;for(a=0,c=(0,eA.Z)(t(u));a<s;++a)u[c[a]].index=a;return n(u,c),u}return stack.keys=function(t){return arguments.length?(e="function"==typeof t?t:(0,eT.Z)(Array.from(t)),stack):e},stack.value=function(e){return arguments.length?(o="function"==typeof e?e:(0,eT.Z)(+e),stack):o},stack.order=function(e){return arguments.length?(t=null==e?order_none:"function"==typeof e?e:(0,eT.Z)(Array.from(e)),stack):t},stack.offset=function(e){return arguments.length?(n=null==e?none:e,stack):n},stack}var ek=n(8412),eE=n.n(ek),eM=n(1843),eI=n.n(eM),eD=n(2727),eR=n.n(eD),eL=n(8293),eN=n.n(eL),eB=n(3874),eW=n.n(eB),eU=n(8614),ez=n.n(eU),eK=n(437),eF=n.n(eK),eH=n(9027),eZ=n.n(eH),e$=n(1008),eV=n.n(e$),eY=n(2077),eG=n.n(eY),eX=n(1864),eQ=n.n(eX),eJ=n(539),e0=n.n(eJ);function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _iterableToArray(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}var utils_identity=function(e){return e},e1={},isPlaceHolder=function(e){return e===e1},curry0=function(e){return function _curried(){return 0==arguments.length||1==arguments.length&&isPlaceHolder(arguments.length<=0?void 0:arguments[0])?_curried:e.apply(void 0,arguments)}},curry=function(e){return function curryN(e,t){return 1===e?t:curry0(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(e){return e!==e1}).length;return a>=e?t.apply(void 0,o):curryN(e-a,curry0(function(){for(var e=arguments.length,n=Array(e),i=0;i<e;i++)n[i]=arguments[i];var a=o.map(function(e){return isPlaceHolder(e)?n.shift():e});return t.apply(void 0,_toConsumableArray(a).concat(n))}))})}(e.length,e)},range=function(e,t){for(var n=[],o=e;o<t;++o)n[o-e]=o;return n},e2=curry(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(e){return t[e]}).map(e)}),compose=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(!t.length)return utils_identity;var o=t.reverse(),i=o[0],a=o.slice(1);return function(){return a.reduce(function(e,t){return t(e)},i.apply(void 0,arguments))}},reverse=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},memoize=function(e){var t=null,n=null;return function(){for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t&&i.every(function(e,n){return e===t[n]})?n:(t=i,n=e.apply(void 0,i))}},e3={rangeStep:function(e,t,n){for(var o=new(e0())(e),i=0,a=[];o.lt(t)&&i<1e5;)a.push(o.toNumber()),o=o.add(n),i++;return a},getDigitCount:function(e){return 0===e?1:Math.floor(new(e0())(e).abs().log(10).toNumber())+1},interpolateNumber:curry(function(e,t,n){var o=+e;return o+n*(+t-o)}),uninterpolateNumber:curry(function(e,t,n){var o=t-+e;return(n-e)/(o=o||1/0)}),uninterpolateTruncation:curry(function(e,t,n){var o=t-+e;return Math.max(0,Math.min(1,(n-e)/(o=o||1/0)))})};function getNiceTickValues_toConsumableArray(e){return getNiceTickValues_arrayWithoutHoles(e)||getNiceTickValues_iterableToArray(e)||getNiceTickValues_unsupportedIterableToArray(e)||getNiceTickValues_nonIterableSpread()}function getNiceTickValues_nonIterableSpread(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function getNiceTickValues_iterableToArray(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function getNiceTickValues_arrayWithoutHoles(e){if(Array.isArray(e))return getNiceTickValues_arrayLikeToArray(e)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||getNiceTickValues_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function getNiceTickValues_unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return getNiceTickValues_arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return getNiceTickValues_arrayLikeToArray(e,t)}}function getNiceTickValues_arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function _iterableToArrayLimit(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var n=[],o=!0,i=!1,a=void 0;try{for(var c,u=e[Symbol.iterator]();!(o=(c=u.next()).done)&&(n.push(c.value),!t||n.length!==t);o=!0);}catch(e){i=!0,a=e}finally{try{o||null==u.return||u.return()}finally{if(i)throw a}}return n}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function getValidInterval(e){var t=_slicedToArray(e,2),n=t[0],o=t[1],i=n,a=o;return n>o&&(i=o,a=n),[i,a]}function getFormatStep(e,t,n){if(e.lte(0))return new(e0())(0);var o=e3.getDigitCount(e.toNumber()),i=new(e0())(10).pow(o),a=e.div(i),c=1!==o?.05:.1,u=new(e0())(Math.ceil(a.div(c).toNumber())).add(n).mul(c).mul(i);return t?u:new(e0())(Math.ceil(u))}function getTickOfSingleValue(e,t,n){var o=1,i=new(e0())(e);if(!i.isint()&&n){var a=Math.abs(e);a<1?(o=new(e0())(10).pow(e3.getDigitCount(e)-1),i=new(e0())(Math.floor(i.div(o).toNumber())).mul(o)):a>1&&(i=new(e0())(Math.floor(e)))}else 0===e?i=new(e0())(Math.floor((t-1)/2)):n||(i=new(e0())(Math.floor(e)));var c=Math.floor((t-1)/2);return compose(e2(function(e){return i.add(new(e0())(e-c).mul(o)).toNumber()}),range)(0,t)}function calculateStep(e,t,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(n-1)))return{step:new(e0())(0),tickMin:new(e0())(0),tickMax:new(e0())(0)};var c=getFormatStep(new(e0())(t).sub(e).div(n-1),o,a),u=Math.ceil((i=e<=0&&t>=0?new(e0())(0):(i=new(e0())(e).add(t).div(2)).sub(new(e0())(i).mod(c))).sub(e).div(c).toNumber()),s=Math.ceil(new(e0())(t).sub(i).div(c).toNumber()),l=u+s+1;return l>n?calculateStep(e,t,n,o,a+1):(l<n&&(s=t>0?s+(n-l):s,u=t>0?u:u+(n-l)),{step:c,tickMin:i.sub(new(e0())(u).mul(c)),tickMax:i.add(new(e0())(s).mul(c))})}function getTickValuesFn(e){var t=_slicedToArray(e,2),n=t[0],o=t[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,a=!(arguments.length>2)||void 0===arguments[2]||arguments[2],c=Math.max(i,2),u=_slicedToArray(getValidInterval([n,o]),2),s=u[0],l=u[1];if(s===-1/0||l===1/0)return[n,o];if(s===l)return getTickOfSingleValue(s,i,a);var f=getFormatStep(new(e0())(l).sub(s).div(c-1),a,0),p=compose(e2(function(e){return new(e0())(s).add(new(e0())(e).mul(f)).toNumber()}),range)(0,c).filter(function(e){return e>=s&&e<=l});return n>o?reverse(p):p}function getTickValuesFixedDomainFn(e,t){var n=_slicedToArray(e,2),o=n[0],i=n[1],a=!(arguments.length>2)||void 0===arguments[2]||arguments[2],c=_slicedToArray(getValidInterval([o,i]),2),u=c[0],s=c[1];if(u===-1/0||s===1/0)return[o,i];if(u===s)return[u];var l=getFormatStep(new(e0())(s).sub(u).div(Math.max(t,2)-1),a,0),f=[].concat(getNiceTickValues_toConsumableArray(e3.rangeStep(new(e0())(u),new(e0())(s).sub(new(e0())(.99).mul(l)),l)),[s]);return o>i?reverse(f):f}var e4=memoize(function(e){var t=_slicedToArray(e,2),n=t[0],o=t[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,a=!(arguments.length>2)||void 0===arguments[2]||arguments[2],c=Math.max(i,2),u=_slicedToArray(getValidInterval([n,o]),2),s=u[0],l=u[1];if(s===-1/0||l===1/0){var f=l===1/0?[s].concat(getNiceTickValues_toConsumableArray(range(0,i-1).map(function(){return 1/0}))):[].concat(getNiceTickValues_toConsumableArray(range(0,i-1).map(function(){return-1/0})),[l]);return n>o?reverse(f):f}if(s===l)return getTickOfSingleValue(s,i,a);var p=calculateStep(s,l,c,a),d=p.step,y=p.tickMin,h=p.tickMax,g=e3.rangeStep(y,h.add(new(e0())(.1).mul(d)),d);return n>o?reverse(g):g});memoize(getTickValuesFn);var e6=memoize(getTickValuesFixedDomainFn),e5=n(2494),e7=n(7281),e8=n(3843),e9=n(9776);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ChartUtils_toConsumableArray(e){return ChartUtils_arrayWithoutHoles(e)||ChartUtils_iterableToArray(e)||ChartUtils_unsupportedIterableToArray(e)||ChartUtils_nonIterableSpread()}function ChartUtils_nonIterableSpread(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ChartUtils_unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return ChartUtils_arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ChartUtils_arrayLikeToArray(e,t)}}function ChartUtils_iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function ChartUtils_arrayWithoutHoles(e){if(Array.isArray(e))return ChartUtils_arrayLikeToArray(e)}function ChartUtils_arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function getValueByDataKey(e,t,n){return eR()(e)||eR()(t)?n:(0,e7.P2)(t)?ez()(e,t,n):eN()(t)?t(e):n}function getDomainOfDataByKey(e,t,n,o){var i=eF()(e,function(e){return getValueByDataKey(e,t)});if("number"===n){var a=i.filter(function(e){return(0,e7.hj)(e)||parseFloat(e)});return a.length?[eI()(a),eE()(a)]:[1/0,-1/0]}return(o?i.filter(function(e){return!eR()(e)}):i).map(function(e){return(0,e7.P2)(e)||e instanceof Date?e:""})}var calculateActiveTickIndex=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,a=-1,c=null!==(t=null==n?void 0:n.length)&&void 0!==t?t:0;if(c<=1)return 0;if(i&&"angleAxis"===i.axisType&&1e-6>=Math.abs(Math.abs(i.range[1]-i.range[0])-360))for(var u=i.range,s=0;s<c;s++){var l=s>0?o[s-1].coordinate:o[c-1].coordinate,f=o[s].coordinate,p=s>=c-1?o[0].coordinate:o[s+1].coordinate,d=void 0;if((0,e7.uY)(f-l)!==(0,e7.uY)(p-f)){var y=[];if((0,e7.uY)(p-f)===(0,e7.uY)(u[1]-u[0])){d=p;var h=f+u[1]-u[0];y[0]=Math.min(h,(h+l)/2),y[1]=Math.max(h,(h+l)/2)}else{d=l;var g=p+u[1]-u[0];y[0]=Math.min(f,(g+f)/2),y[1]=Math.max(f,(g+f)/2)}var b=[Math.min(f,(d+f)/2),Math.max(f,(d+f)/2)];if(e>b[0]&&e<=b[1]||e>=y[0]&&e<=y[1]){a=o[s].index;break}}else{var m=Math.min(l,p),_=Math.max(l,p);if(e>(m+f)/2&&e<=(_+f)/2){a=o[s].index;break}}}else for(var x=0;x<c;x++)if(0===x&&e<=(n[x].coordinate+n[x+1].coordinate)/2||x>0&&x<c-1&&e>(n[x].coordinate+n[x-1].coordinate)/2&&e<=(n[x].coordinate+n[x+1].coordinate)/2||x===c-1&&e>(n[x].coordinate+n[x-1].coordinate)/2){a=n[x].index;break}return a},getMainColorOfGraphicItem=function(e){var t,n,o=e.type.displayName,i=null!==(t=e.type)&&void 0!==t&&t.defaultProps?_objectSpread(_objectSpread({},e.type.defaultProps),e.props):e.props,a=i.stroke,c=i.fill;switch(o){case"Line":n=a;break;case"Area":case"Radar":n=a&&"none"!==a?a:c;break;default:n=c}return n},getBarSizeList=function(e){var t=e.barSize,n=e.totalSize,o=e.stackGroups,i=void 0===o?{}:o;if(!i)return{};for(var a={},c=Object.keys(i),u=0,s=c.length;u<s;u++)for(var l=i[c[u]].stackGroups,f=Object.keys(l),p=0,d=f.length;p<d;p++){var y=l[f[p]],h=y.items,g=y.cateAxisId,b=h.filter(function(e){return(0,e8.Gf)(e.type).indexOf("Bar")>=0});if(b&&b.length){var m=b[0].type.defaultProps,_=void 0!==m?_objectSpread(_objectSpread({},m),b[0].props):b[0].props,x=_.barSize,S=_[g];a[S]||(a[S]=[]);var P=eR()(x)?t:x;a[S].push({item:b[0],stackList:b.slice(1),barSize:eR()(P)?void 0:(0,e7.h1)(P,n,0)})}}return a},getBarPosition=function(e){var t,n=e.barGap,o=e.barCategoryGap,i=e.bandSize,a=e.sizeList,c=void 0===a?[]:a,u=e.maxBarSize,s=c.length;if(s<1)return null;var l=(0,e7.h1)(n,i,0,!0),f=[];if(c[0].barSize===+c[0].barSize){var p=!1,d=i/s,y=c.reduce(function(e,t){return e+t.barSize||0},0);(y+=(s-1)*l)>=i&&(y-=(s-1)*l,l=0),y>=i&&d>0&&(p=!0,d*=.9,y=s*d);var h={offset:((i-y)/2>>0)-l,size:0};t=c.reduce(function(e,t){var n={item:t.item,position:{offset:h.offset+h.size+l,size:p?d:t.barSize}},o=[].concat(ChartUtils_toConsumableArray(e),[n]);return h=o[o.length-1].position,t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){o.push({item:e,position:h})}),o},f)}else{var g=(0,e7.h1)(o,i,0,!0);i-2*g-(s-1)*l<=0&&(l=0);var b=(i-2*g-(s-1)*l)/s;b>1&&(b>>=0);var m=u===+u?Math.min(b,u):b;t=c.reduce(function(e,t,n){var o=[].concat(ChartUtils_toConsumableArray(e),[{item:t.item,position:{offset:g+(b+l)*n+(b-m)/2,size:m}}]);return t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){o.push({item:e,position:o[o.length-1].position})}),o},f)}return t},appendOffsetOfLegend=function(e,t,n,o){var i=n.children,a=n.width,c=n.margin,u=a-(c.left||0)-(c.right||0),s=(0,e9.z)({children:i,legendWidth:u});if(s){var l=o||{},f=l.width,p=l.height,d=s.align,y=s.verticalAlign,h=s.layout;if(("vertical"===h||"horizontal"===h&&"middle"===y)&&"center"!==d&&(0,e7.hj)(e[d]))return _objectSpread(_objectSpread({},e),{},_defineProperty({},d,e[d]+(f||0)));if(("horizontal"===h||"vertical"===h&&"center"===d)&&"middle"!==y&&(0,e7.hj)(e[y]))return _objectSpread(_objectSpread({},e),{},_defineProperty({},y,e[y]+(p||0)))}return e},getDomainOfErrorBars=function(e,t,n,o,i){var a=t.props.children,c=(0,e8.NN)(a,e5.W).filter(function(e){var t;return t=e.props.direction,!!eR()(i)||("horizontal"===o?"yAxis"===i:"vertical"===o||"x"===t?"xAxis"===i:"y"!==t||"yAxis"===i)});if(c&&c.length){var u=c.map(function(e){return e.props.dataKey});return e.reduce(function(e,t){var o=getValueByDataKey(t,n);if(eR()(o))return e;var i=Array.isArray(o)?[eI()(o),eE()(o)]:[o,o],a=u.reduce(function(e,n){var o=getValueByDataKey(t,n,0),a=i[0]-Math.abs(Array.isArray(o)?o[0]:o),c=i[1]+Math.abs(Array.isArray(o)?o[1]:o);return[Math.min(a,e[0]),Math.max(c,e[1])]},[1/0,-1/0]);return[Math.min(a[0],e[0]),Math.max(a[1],e[1])]},[1/0,-1/0])}return null},parseErrorBarsOfAxis=function(e,t,n,o,i){var a=t.map(function(t){return getDomainOfErrorBars(e,t,n,i,o)}).filter(function(e){return!eR()(e)});return a&&a.length?a.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]):null},getDomainOfItemsWithSameAxis=function(e,t,n,o,i){var a=t.map(function(t){var a=t.props.dataKey;return"number"===n&&a&&getDomainOfErrorBars(e,t,a,o)||getDomainOfDataByKey(e,a,n,i)});if("number"===n)return a.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]);var c={};return a.reduce(function(e,t){for(var n=0,o=t.length;n<o;n++)c[t[n]]||(c[t[n]]=!0,e.push(t[n]));return e},[])},isCategoricalAxis=function(e,t){return"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t},getCoordinatesOfGrid=function(e,t,n,o){if(o)return e.map(function(e){return e.coordinate});var i,a,c=e.map(function(e){return e.coordinate===t&&(i=!0),e.coordinate===n&&(a=!0),e.coordinate});return i||c.push(t),a||c.push(n),c},getTicksOfAxis=function(e,t,n){if(!e)return null;var o=e.scale,i=e.duplicateDomain,a=e.type,c=e.range,u="scaleBand"===e.realScaleType?o.bandwidth()/2:2,s=(t||n)&&"category"===a&&o.bandwidth?o.bandwidth()/u:0;return(s="angleAxis"===e.axisType&&(null==c?void 0:c.length)>=2?2*(0,e7.uY)(c[0]-c[1])*s:s,t&&(e.ticks||e.niceTicks))?(e.ticks||e.niceTicks).map(function(e){return{coordinate:o(i?i.indexOf(e):e)+s,value:e,offset:s}}).filter(function(e){return!eZ()(e.coordinate)}):e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map(function(e,t){return{coordinate:o(e)+s,value:e,index:t,offset:s}}):o.ticks&&!n?o.ticks(e.tickCount).map(function(e){return{coordinate:o(e)+s,value:e,offset:s}}):o.domain().map(function(e,t){return{coordinate:o(e)+s,value:i?i[e]:e,index:t,offset:s}})},te=new WeakMap,combineEventHandlers=function(e,t){if("function"!=typeof t)return e;te.has(e)||te.set(e,new WeakMap);var n=te.get(e);if(n.has(t))return n.get(t);var combineHandler=function(){e.apply(void 0,arguments),t.apply(void 0,arguments)};return n.set(t,combineHandler),combineHandler},parseScale=function(e,t,n){var o=e.scale,i=e.type,a=e.layout,c=e.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:p.Z(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:linear_linear(),realScaleType:"linear"}:"category"===i&&t&&(t.indexOf("LineChart")>=0||t.indexOf("AreaChart")>=0||t.indexOf("ComposedChart")>=0&&!n)?{scale:p.x(),realScaleType:"point"}:"category"===i?{scale:p.Z(),realScaleType:"band"}:{scale:linear_linear(),realScaleType:"linear"};if(eW()(o)){var u="scale".concat(eV()(o));return{scale:(f[u]||p.x)(),realScaleType:f[u]?u:"point"}}return eN()(o)?{scale:o}:{scale:p.x(),realScaleType:"point"}},checkDomainOfScale=function(e){var t=e.domain();if(t&&!(t.length<=2)){var n=t.length,o=e.range(),i=Math.min(o[0],o[1])-1e-4,a=Math.max(o[0],o[1])+1e-4,c=e(t[0]),u=e(t[n-1]);(c<i||c>a||u<i||u>a)&&e.domain([t[0],t[n-1]])}},findPositionOfBar=function(e,t){if(!e)return null;for(var n=0,o=e.length;n<o;n++)if(e[n].item===t)return e[n].position;return null},truncateByDomain=function(e,t){if(!t||2!==t.length||!(0,e7.hj)(t[0])||!(0,e7.hj)(t[1]))return e;var n=Math.min(t[0],t[1]),o=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!(0,e7.hj)(e[0])||e[0]<n)&&(i[0]=n),(!(0,e7.hj)(e[1])||e[1]>o)&&(i[1]=o),i[0]>o&&(i[0]=o),i[1]<n&&(i[1]=n),i},tt={sign:function(e){var t=e.length;if(!(t<=0))for(var n=0,o=e[0].length;n<o;++n)for(var i=0,a=0,c=0;c<t;++c){var u=eZ()(e[c][n][1])?e[c][n][0]:e[c][n][1];u>=0?(e[c][n][0]=i,e[c][n][1]=i+u,i=e[c][n][1]):(e[c][n][0]=a,e[c][n][1]=a+u,a=e[c][n][1])}},expand:expand,none:none,silhouette:silhouette,wiggle:wiggle,positive:function(e){var t=e.length;if(!(t<=0))for(var n=0,o=e[0].length;n<o;++n)for(var i=0,a=0;a<t;++a){var c=eZ()(e[a][n][1])?e[a][n][0]:e[a][n][1];c>=0?(e[a][n][0]=i,e[a][n][1]=i+c,i=e[a][n][1]):(e[a][n][0]=0,e[a][n][1]=0)}}},getStackedData=function(e,t,n){var o=t.map(function(e){return e.props.dataKey}),i=tt[n];return src_stack().keys(o).value(function(e,t){return+getValueByDataKey(e,t,0)}).order(order_none).offset(i)(e)},getStackGroupsByAxisId=function(e,t,n,o,i,a){if(!e)return null;var c=(a?t.reverse():t).reduce(function(e,t){var i,a=null!==(i=t.type)&&void 0!==i&&i.defaultProps?_objectSpread(_objectSpread({},t.type.defaultProps),t.props):t.props,c=a.stackId;if(a.hide)return e;var u=a[n],s=e[u]||{hasStack:!1,stackGroups:{}};if((0,e7.P2)(c)){var l=s.stackGroups[c]||{numericAxisId:n,cateAxisId:o,items:[]};l.items.push(t),s.hasStack=!0,s.stackGroups[c]=l}else s.stackGroups[(0,e7.EL)("_stackId_")]={numericAxisId:n,cateAxisId:o,items:[t]};return _objectSpread(_objectSpread({},e),{},_defineProperty({},u,s))},{});return Object.keys(c).reduce(function(t,a){var u=c[a];return u.hasStack&&(u.stackGroups=Object.keys(u.stackGroups).reduce(function(t,a){var c=u.stackGroups[a];return _objectSpread(_objectSpread({},t),{},_defineProperty({},a,{numericAxisId:n,cateAxisId:o,items:c.items,stackedData:getStackedData(e,c.items,i)}))},{})),_objectSpread(_objectSpread({},t),{},_defineProperty({},a,u))},{})},getTicksOfScale=function(e,t){var n=t.realScaleType,o=t.type,i=t.tickCount,a=t.originalDomain,c=t.allowDecimals,u=n||t.scale;if("auto"!==u&&"linear"!==u)return null;if(i&&"number"===o&&a&&("auto"===a[0]||"auto"===a[1])){var s=e.domain();if(!s.length)return null;var l=e4(s,i,c);return e.domain([eI()(l),eE()(l)]),{niceTicks:l}}return i&&"number"===o?{niceTicks:e6(e.domain(),i,c)}:null};function getCateCoordinateOfLine(e){var t=e.axis,n=e.ticks,o=e.bandSize,i=e.entry,a=e.index,c=e.dataKey;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!eR()(i[t.dataKey])){var u=(0,e7.Ap)(n,"value",i[t.dataKey]);if(u)return u.coordinate+o/2}return n[a]?n[a].coordinate+o/2:null}var s=getValueByDataKey(i,eR()(c)?t.dataKey:c);return eR()(s)?null:t.scale(s)}var getCateCoordinateOfBar=function(e){var t=e.axis,n=e.ticks,o=e.offset,i=e.bandSize,a=e.entry,c=e.index;if("category"===t.type)return n[c]?n[c].coordinate+o:null;var u=getValueByDataKey(a,t.dataKey,t.domain[c]);return eR()(u)?null:t.scale(u)-i/2+o},getBaseValueOfBar=function(e){var t=e.numericAxis,n=t.scale.domain();if("number"===t.type){var o=Math.min(n[0],n[1]),i=Math.max(n[0],n[1]);return o<=0&&i>=0?0:i<0?i:o}return n[0]},getStackedDataOfItem=function(e,t){var n,o=(null!==(n=e.type)&&void 0!==n&&n.defaultProps?_objectSpread(_objectSpread({},e.type.defaultProps),e.props):e.props).stackId;if((0,e7.P2)(o)){var i=t[o];if(i){var a=i.items.indexOf(e);return a>=0?i.stackedData[a]:null}}return null},getDomainOfStackGroups=function(e,t,n){return Object.keys(e).reduce(function(o,i){var a=e[i].stackedData.reduce(function(e,o){var i=o.slice(t,n+1).reduce(function(e,t){return[eI()(t.concat([e[0]]).filter(e7.hj)),eE()(t.concat([e[1]]).filter(e7.hj))]},[1/0,-1/0]);return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(a[0],o[0]),Math.max(a[1],o[1])]},[1/0,-1/0]).map(function(e){return e===1/0||e===-1/0?0:e})},tr=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,tn=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,parseSpecifiedDomain=function(e,t,n){if(eN()(e))return e(t,n);if(!Array.isArray(e))return t;var o=[];if((0,e7.hj)(e[0]))o[0]=n?e[0]:Math.min(e[0],t[0]);else if(tr.test(e[0])){var i=+tr.exec(e[0])[1];o[0]=t[0]-i}else eN()(e[0])?o[0]=e[0](t[0]):o[0]=t[0];if((0,e7.hj)(e[1]))o[1]=n?e[1]:Math.max(e[1],t[1]);else if(tn.test(e[1])){var a=+tn.exec(e[1])[1];o[1]=t[1]+a}else eN()(e[1])?o[1]=e[1](t[1]):o[1]=t[1];return o},getBandSizeOfAxis=function(e,t,n){if(e&&e.scale&&e.scale.bandwidth){var o=e.scale.bandwidth();if(!n||o>0)return o}if(e&&t&&t.length>=2){for(var i=eQ()(t,function(e){return e.coordinate}),a=1/0,c=1,u=i.length;c<u;c++){var s=i[c],l=i[c-1];a=Math.min((s.coordinate||0)-(l.coordinate||0),a)}return a===1/0?0:a}return n?void 0:0},parseDomainOfCategoryAxis=function(e,t,n){return!e||!e.length||eG()(e,ez()(n,"type.defaultProps.domain"))?t:e},getTooltipItem=function(e,t){var n=e.type.defaultProps?_objectSpread(_objectSpread({},e.type.defaultProps),e.props):e.props,o=n.dataKey,i=n.name,a=n.unit,c=n.formatter,u=n.tooltipType,s=n.chartType,l=n.hide;return _objectSpread(_objectSpread({},(0,e8.L6)(e,!1)),{},{dataKey:o,unit:a,formatter:c,name:i||o,color:getMainColorOfGraphicItem(e),value:getValueByDataKey(t,o),type:u,payload:t,chartType:s,hide:l})}},4768:function(e,t,n){"use strict";n.d(t,{os:function(){return getOffset},xE:function(){return getStringSize}});var o=n(9752);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var i={widthCache:{},cacheCount:0},a={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},c="recharts_measurement_span";function removeInvalidKeys(e){var t=_objectSpread({},e);return Object.keys(t).forEach(function(e){t[e]||delete t[e]}),t}var getStringSize=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||o.x.isSsr)return{width:0,height:0};var n=removeInvalidKeys(t),u=JSON.stringify({text:e,copyStyle:n});if(i.widthCache[u])return i.widthCache[u];try{var s=document.getElementById(c);s||((s=document.createElement("span")).setAttribute("id",c),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var l=_objectSpread(_objectSpread({},a),n);Object.assign(s.style,l),s.textContent="".concat(e);var f=s.getBoundingClientRect(),p={width:f.width,height:f.height};return i.widthCache[u]=p,++i.cacheCount>2e3&&(i.cacheCount=0,i.widthCache={}),p}catch(e){return{width:0,height:0}}},getOffset=function(e){return{top:e.top+window.scrollY-document.documentElement.clientTop,left:e.left+window.scrollX-document.documentElement.clientLeft}}},7281:function(e,t,n){"use strict";n.d(t,{Ap:function(){return findEntryInArray},EL:function(){return uniqueId},Kt:function(){return getAnyElementOfObject},P2:function(){return isNumOrStr},Rw:function(){return isNullish},bv:function(){return hasDuplicate},fC:function(){return compareValues},h1:function(){return getPercentValue},hU:function(){return isPercent},hj:function(){return isNumber},k4:function(){return interpolateNumber},uY:function(){return mathSign}});var o=n(3874),i=n.n(o),a=n(9027),c=n.n(a),u=n(8614),s=n.n(u),l=n(1881),f=n.n(l),p=n(2727),d=n.n(p),mathSign=function(e){return 0===e?0:e>0?1:-1},isPercent=function(e){return i()(e)&&e.indexOf("%")===e.length-1},isNumber=function(e){return f()(e)&&!c()(e)},isNullish=function(e){return d()(e)},isNumOrStr=function(e){return isNumber(e)||i()(e)},y=0,uniqueId=function(e){var t=++y;return"".concat(e||"").concat(t)},getPercentValue=function(e,t){var n,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!isNumber(e)&&!i()(e))return o;if(isPercent(e)){var u=e.indexOf("%");n=t*parseFloat(e.slice(0,u))/100}else n=+e;return c()(n)&&(n=o),a&&n>t&&(n=t),n},getAnyElementOfObject=function(e){if(!e)return null;var t=Object.keys(e);return t&&t.length?e[t[0]]:null},hasDuplicate=function(e){if(!Array.isArray(e))return!1;for(var t=e.length,n={},o=0;o<t;o++){if(n[e[o]])return!0;n[e[o]]=!0}return!1},interpolateNumber=function(e,t){return isNumber(e)&&isNumber(t)?function(n){return e+n*(t-e)}:function(){return t}};function findEntryInArray(e,t,n){return e&&e.length?e.find(function(e){return e&&("function"==typeof t?t(e):s()(e,t))===n}):null}var compareValues=function(e,t){return isNumber(e)&&isNumber(t)?e-t:i()(e)&&i()(t)?e.localeCompare(t):e instanceof Date&&t instanceof Date?e.getTime()-t.getTime():String(e).localeCompare(String(t))}},9752:function(e,t,n){"use strict";n.d(t,{x:function(){return o}});var o={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(e){return o[e]},set:function(e,t){if("string"==typeof e)o[e]=t;else{var n=Object.keys(e);n&&n.length&&n.forEach(function(t){o[t]=e[t]})}}}},7105:function(e,t,n){"use strict";n.d(t,{Z:function(){return warn}});var warn=function(e,t){for(var n=arguments.length,o=Array(n>2?n-2:0),i=2;i<n;i++)o[i-2]=arguments[i]}},6120:function(e,t,n){"use strict";n.d(t,{$4:function(){return getMaxRadius},$S:function(){return getTickClassName},Wk:function(){return f},op:function(){return polarToCartesian},t9:function(){return formatAxisMap},z3:function(){return inRangeOfSector}});var o=n(2727),i=n.n(o),a=n(2265),c=n(8293),u=n.n(c),s=n(7281),l=n(3249);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,a,c,u=[],s=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(u.push(o.value),u.length!==t);s=!0);}catch(e){l=!0,i=e}finally{try{if(!s&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(l)throw i}}return u}}function _arrayWithHoles(e){if(Array.isArray(e))return e}var f=Math.PI/180,polarToCartesian=function(e,t,n,o){return{x:e+Math.cos(-f*o)*n,y:t+Math.sin(-f*o)*n}},getMaxRadius=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(n.left||0)-(n.right||0)),Math.abs(t-(n.top||0)-(n.bottom||0)))/2},formatAxisMap=function(e,t,n,o,a){var c=e.width,u=e.height,f=e.startAngle,p=e.endAngle,d=(0,s.h1)(e.cx,c,c/2),y=(0,s.h1)(e.cy,u,u/2),h=getMaxRadius(c,u,n),g=(0,s.h1)(e.innerRadius,h,0),b=(0,s.h1)(e.outerRadius,h,.8*h);return Object.keys(t).reduce(function(e,n){var c,u=t[n],s=u.domain,h=u.reversed;if(i()(u.range))"angleAxis"===o?c=[f,p]:"radiusAxis"===o&&(c=[g,b]),h&&(c=[c[1],c[0]]);else{var m=_slicedToArray(c=u.range,2);f=m[0],p=m[1]}var _=(0,l.Hq)(u,a),x=_.realScaleType,S=_.scale;S.domain(s).range(c),(0,l.zF)(S);var P=(0,l.g$)(S,_objectSpread(_objectSpread({},u),{},{realScaleType:x})),O=_objectSpread(_objectSpread(_objectSpread({},u),P),{},{range:c,radius:b,realScaleType:x,scale:S,cx:d,cy:y,innerRadius:g,outerRadius:b,startAngle:f,endAngle:p});return _objectSpread(_objectSpread({},e),{},_defineProperty({},n,O))},{})},distanceBetweenPoints=function(e,t){var n=e.x,o=e.y;return Math.sqrt(Math.pow(n-t.x,2)+Math.pow(o-t.y,2))},getAngleOfPoint=function(e,t){var n=e.x,o=e.y,i=t.cx,a=t.cy,c=distanceBetweenPoints({x:n,y:o},{x:i,y:a});if(c<=0)return{radius:c};var u=Math.acos((n-i)/c);return o>a&&(u=2*Math.PI-u),{radius:c,angle:180*u/Math.PI,angleInRadian:u}},formatAngleOfSector=function(e){var t=e.startAngle,n=e.endAngle,o=Math.min(Math.floor(t/360),Math.floor(n/360));return{startAngle:t-360*o,endAngle:n-360*o}},inRangeOfSector=function(e,t){var n,o=getAngleOfPoint({x:e.x,y:e.y},t),i=o.radius,a=o.angle,c=t.innerRadius,u=t.outerRadius;if(i<c||i>u)return!1;if(0===i)return!0;var s=formatAngleOfSector(t),l=s.startAngle,f=s.endAngle,p=a;if(l<=f){for(;p>f;)p-=360;for(;p<l;)p+=360;n=p>=l&&p<=f}else{for(;p>l;)p-=360;for(;p<f;)p+=360;n=p>=f&&p<=l}return n?_objectSpread(_objectSpread({},t),{},{radius:i,angle:p+360*Math.min(Math.floor(t.startAngle/360),Math.floor(t.endAngle/360))}):null},getTickClassName=function(e){return(0,a.isValidElement)(e)||u()(e)||"boolean"==typeof e?"":e.className}},3843:function(e,t,n){"use strict";n.d(t,{$R:function(){return parseChildIndex},Bh:function(){return getReactEventByType},Gf:function(){return getDisplayName},L6:function(){return filterProps},NN:function(){return findAllByType},TT:function(){return validateWidthHeight},eu:function(){return renderByOrder},jf:function(){return hasClipDot},rL:function(){return isChildrenEqual},sP:function(){return findChildByType}});var o=n(8614),i=n.n(o),a=n(2727),c=n.n(a),u=n(3874),s=n.n(u),l=n(8293),f=n.n(l),p=n(6905),d=n.n(p),y=n(2265),h=n(6859),g=n(7281),b=n(1374),m=n(2655),_=["children"],x=["children"];function _objectWithoutProperties(e,t){if(null==e)return{};var n,o,i=_objectWithoutPropertiesLoose(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){if(t.indexOf(o)>=0)continue;n[o]=e[o]}return n}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var S={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},getDisplayName=function(e){return"string"==typeof e?e:e?e.displayName||e.name||"Component":""},P=null,O=null,toArray=function toArray(e){if(e===P&&Array.isArray(O))return O;var t=[];return y.Children.forEach(e,function(e){c()(e)||((0,h.isFragment)(e)?t=t.concat(toArray(e.props.children)):t.push(e))}),O=t,P=e,t};function findAllByType(e,t){var n=[],o=[];return o=Array.isArray(t)?t.map(function(e){return getDisplayName(e)}):[getDisplayName(t)],toArray(e).forEach(function(e){var t=i()(e,"type.displayName")||i()(e,"type.name");-1!==o.indexOf(t)&&n.push(e)}),n}function findChildByType(e,t){var n=findAllByType(e,t);return n&&n[0]}var validateWidthHeight=function(e){if(!e||!e.props)return!1;var t=e.props,n=t.width,o=t.height;return!!(0,g.hj)(n)&&!(n<=0)&&!!(0,g.hj)(o)&&!(o<=0)},j=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],hasClipDot=function(e){return e&&"object"===_typeof(e)&&"clipDot"in e},isValidSpreadableProp=function(e,t,n,o){var i,a=null!==(i=null===m.ry||void 0===m.ry?void 0:m.ry[o])&&void 0!==i?i:[];return t.startsWith("data-")||!f()(e)&&(o&&a.includes(t)||m.Yh.includes(t))||n&&m.nv.includes(t)},filterProps=function(e,t,n){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var o=e;if((0,y.isValidElement)(e)&&(o=e.props),!d()(o))return null;var i={};return Object.keys(o).forEach(function(e){var a;isValidSpreadableProp(null===(a=o)||void 0===a?void 0:a[e],e,t,n)&&(i[e]=o[e])}),i},isChildrenEqual=function isChildrenEqual(e,t){if(e===t)return!0;var n=y.Children.count(e);if(n!==y.Children.count(t))return!1;if(0===n)return!0;if(1===n)return isSingleChildEqual(Array.isArray(e)?e[0]:e,Array.isArray(t)?t[0]:t);for(var o=0;o<n;o++){var i=e[o],a=t[o];if(Array.isArray(i)||Array.isArray(a)){if(!isChildrenEqual(i,a))return!1}else if(!isSingleChildEqual(i,a))return!1}return!0},isSingleChildEqual=function(e,t){if(c()(e)&&c()(t))return!0;if(!c()(e)&&!c()(t)){var n=e.props||{},o=n.children,i=_objectWithoutProperties(n,_),a=t.props||{},u=a.children,s=_objectWithoutProperties(a,x);if(o&&u)return(0,b.w)(i,s)&&isChildrenEqual(o,u);if(!o&&!u)return(0,b.w)(i,s)}return!1},renderByOrder=function(e,t){var n=[],o={};return toArray(e).forEach(function(e,i){if(e&&e.type&&s()(e.type)&&j.indexOf(e.type)>=0)n.push(e);else if(e){var a=getDisplayName(e.type),c=t[a]||{},u=c.handler,l=c.once;if(u&&(!l||!o[a])){var f=u(e,a,i);n.push(f),o[a]=!0}}}),n},getReactEventByType=function(e){var t=e&&e.type;return t&&S[t]?S[t]:null},parseChildIndex=function(e,t){return toArray(t).indexOf(e)}},1374:function(e,t,n){"use strict";function shallowEqual(e,t){for(var n in e)if(({}).hasOwnProperty.call(e,n)&&(!({}).hasOwnProperty.call(t,n)||e[n]!==t[n]))return!1;for(var o in t)if(({}).hasOwnProperty.call(t,o)&&!({}).hasOwnProperty.call(e,o))return!1;return!0}n.d(t,{w:function(){return shallowEqual}})},9776:function(e,t,n){"use strict";n.d(t,{z:function(){return getLegendProps}});var o=n(9857),i=n(3249),a=n(3843);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=_typeof(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var getLegendProps=function(e){var t,n=e.children,c=e.formattedGraphicalItems,u=e.legendWidth,s=e.legendContent,l=(0,a.sP)(n,o.D);if(!l)return null;var f=o.D.defaultProps,p=void 0!==f?_objectSpread(_objectSpread({},f),l.props):{};return t=l.props&&l.props.payload?l.props&&l.props.payload:"children"===s?(c||[]).reduce(function(e,t){var n=t.item,o=t.props,i=o.sectors||o.data||[];return e.concat(i.map(function(e){return{type:l.props.iconType||n.props.legendType,value:e.name,color:e.fill,payload:e}}))},[]):(c||[]).map(function(e){var t=e.item,n=t.type.defaultProps,o=void 0!==n?_objectSpread(_objectSpread({},n),t.props):{},a=o.dataKey,c=o.name,u=o.legendType;return{inactive:o.hide,dataKey:a,type:p.iconType||u||"square",color:(0,i.fk)(t),value:c||a,payload:o}}),_objectSpread(_objectSpread(_objectSpread({},p),o.D.getWithHeight(l,u)),{},{payload:t,item:l})}},200:function(e,t,n){"use strict";n.d(t,{z:function(){return getUniqPayload}});var o=n(3705),i=n.n(o),a=n(8293),c=n.n(a);function getUniqPayload(e,t,n){return!0===t?i()(e,n):c()(t)?i()(e,t):e}},2655:function(e,t,n){"use strict";n.d(t,{Yh:function(){return c},Ym:function(){return adaptEventHandlers},bw:function(){return adaptEventsOfChild},nv:function(){return l},ry:function(){return s}});var o=n(2265),i=n(6905),a=n.n(i);function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var c=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],u=["points","pathLength"],s={svg:["viewBox","children"],polygon:u,polyline:u},l=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],adaptEventHandlers=function(e,t){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,o.isValidElement)(e)&&(n=e.props),!a()(n))return null;var i={};return Object.keys(n).forEach(function(e){l.includes(e)&&(i[e]=t||function(t){return n[e](n,t)})}),i},adaptEventsOfChild=function(e,t,n){if(!a()(e)||"object"!==_typeof(e))return null;var o=null;return Object.keys(e).forEach(function(i){var a=e[i];l.includes(i)&&"function"==typeof a&&(o||(o={}),o[i]=function(e){return a(t,n,e),null})}),o}},4537:function(e,t){"use strict";/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),s=Symbol.for("react.context"),l=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),y=Symbol.for("react.memo"),h=Symbol.for("react.lazy");function v(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case i:case c:case a:case p:case d:return e;default:switch(e=e&&e.$$typeof){case l:case s:case f:case h:case y:case u:return e;default:return t}}case o:return t}}}Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),t.isFragment=function(e){return v(e)===i}},6859:function(e,t,n){"use strict";e.exports=n(4537)},7042:function(e,t,n){"use strict";function r(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e){if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(n=r(e[t]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n)}return o}function clsx(){for(var e,t,n=0,o="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=r(e))&&(o&&(o+=" "),o+=t);return o}t.Z=clsx},5779:function(e,t,n){"use strict";function range(e,t,n){e=+e,t=+t,n=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+n;for(var o=-1,i=0|Math.max(0,Math.ceil((t-e)/n)),a=Array(i);++o<i;)a[o]=e+o*n;return a}n.d(t,{Z:function(){return band},x:function(){return point}});var o=n(3757),i=n(4713);function band(){var e,t,n=(0,i.Z)().unknown(void 0),a=n.domain,c=n.range,u=0,s=1,l=!1,f=0,p=0,d=.5;function rescale(){var n=a().length,o=s<u,i=o?s:u,y=o?u:s;e=(y-i)/Math.max(1,n-f+2*p),l&&(e=Math.floor(e)),i+=(y-i-e*(n-f))*d,t=e*(1-f),l&&(i=Math.round(i),t=Math.round(t));var h=range(n).map(function(t){return i+e*t});return c(o?h.reverse():h)}return delete n.unknown,n.domain=function(e){return arguments.length?(a(e),rescale()):a()},n.range=function(e){return arguments.length?([u,s]=e,u=+u,s=+s,rescale()):[u,s]},n.rangeRound=function(e){return[u,s]=e,u=+u,s=+s,l=!0,rescale()},n.bandwidth=function(){return t},n.step=function(){return e},n.round=function(e){return arguments.length?(l=!!e,rescale()):l},n.padding=function(e){return arguments.length?(f=Math.min(1,p=+e),rescale()):f},n.paddingInner=function(e){return arguments.length?(f=Math.min(1,e),rescale()):f},n.paddingOuter=function(e){return arguments.length?(p=+e,rescale()):p},n.align=function(e){return arguments.length?(d=Math.max(0,Math.min(1,e)),rescale()):d},n.copy=function(){return band(a(),[u,s]).round(l).paddingInner(f).paddingOuter(p).align(d)},o.o.apply(rescale(),arguments)}function pointish(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return pointish(t())},e}function point(){return pointish(band.apply(null,arguments).paddingInner(1))}},3757:function(e,t,n){"use strict";function initRange(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function initInterpolator(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}n.d(t,{O:function(){return initInterpolator},o:function(){return initRange}})},4713:function(e,t,n){"use strict";n.d(t,{Z:function(){return ordinal},O:function(){return i}});let InternMap=class InternMap extends Map{constructor(e,t=keyof){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,n]of e)this.set(t,n)}get(e){return super.get(intern_get(this,e))}has(e){return super.has(intern_get(this,e))}set(e,t){return super.set(intern_set(this,e),t)}delete(e){return super.delete(intern_delete(this,e))}};function intern_get({_intern:e,_key:t},n){let o=t(n);return e.has(o)?e.get(o):n}function intern_set({_intern:e,_key:t},n){let o=t(n);return e.has(o)?e.get(o):(e.set(o,n),n)}function intern_delete({_intern:e,_key:t},n){let o=t(n);return e.has(o)&&(n=e.get(o),e.delete(o)),n}function keyof(e){return null!==e&&"object"==typeof e?e.valueOf():e}var o=n(3757);let i=Symbol("implicit");function ordinal(){var e=new InternMap,t=[],n=[],a=i;function scale(o){let c=e.get(o);if(void 0===c){if(a!==i)return a;e.set(o,c=t.push(o)-1)}return n[c%n.length]}return scale.domain=function(n){if(!arguments.length)return t.slice();for(let o of(t=[],e=new InternMap,n))e.has(o)||e.set(o,t.push(o)-1);return scale},scale.range=function(e){return arguments.length?(n=Array.from(e),scale):n.slice()},scale.unknown=function(e){return arguments.length?(a=e,scale):a},scale.copy=function(){return ordinal(t,n).unknown(a)},o.o.apply(scale,arguments),scale}},3841:function(e,t,n){"use strict";function __WEBPACK_DEFAULT_EXPORT__(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}n.d(t,{Z:function(){return __WEBPACK_DEFAULT_EXPORT__}}),Array.prototype.slice},4299:function(e,t,n){"use strict";function __WEBPACK_DEFAULT_EXPORT__(e){return function(){return e}}n.d(t,{Z:function(){return __WEBPACK_DEFAULT_EXPORT__}})},730:function(e,t,n){"use strict";n.d(t,{d:function(){return withPath}});let o=Math.PI,i=2*o,a=i-1e-6;function append(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=arguments[t]+e[t]}function appendRound(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return append;let n=10**t;return function(e){this._+=e[0];for(let t=1,o=e.length;t<o;++t)this._+=Math.round(arguments[t]*n)/n+e[t]}}let Path=class Path{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?append:appendRound(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,n,o){this._append`Q${+e},${+t},${this._x1=+n},${this._y1=+o}`}bezierCurveTo(e,t,n,o,i,a){this._append`C${+e},${+t},${+n},${+o},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,n,i,a){if(e=+e,t=+t,n=+n,i=+i,(a=+a)<0)throw Error(`negative radius: ${a}`);let c=this._x1,u=this._y1,s=n-e,l=i-t,f=c-e,p=u-t,d=f*f+p*p;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(d>1e-6){if(Math.abs(p*s-l*f)>1e-6&&a){let y=n-c,h=i-u,g=s*s+l*l,b=Math.sqrt(g),m=Math.sqrt(d),_=a*Math.tan((o-Math.acos((g+d-(y*y+h*h))/(2*b*m)))/2),x=_/m,S=_/b;Math.abs(x-1)>1e-6&&this._append`L${e+x*f},${t+x*p}`,this._append`A${a},${a},0,0,${+(p*y>f*h)},${this._x1=e+S*s},${this._y1=t+S*l}`}else this._append`L${this._x1=e},${this._y1=t}`}}arc(e,t,n,c,u,s){if(e=+e,t=+t,s=!!s,(n=+n)<0)throw Error(`negative radius: ${n}`);let l=n*Math.cos(c),f=n*Math.sin(c),p=e+l,d=t+f,y=1^s,h=s?c-u:u-c;null===this._x1?this._append`M${p},${d}`:(Math.abs(this._x1-p)>1e-6||Math.abs(this._y1-d)>1e-6)&&this._append`L${p},${d}`,n&&(h<0&&(h=h%i+i),h>a?this._append`A${n},${n},0,1,${y},${e-l},${t-f}A${n},${n},0,1,${y},${this._x1=p},${this._y1=d}`:h>1e-6&&this._append`A${n},${n},0,${+(h>=o)},${y},${this._x1=e+n*Math.cos(u)},${this._y1=t+n*Math.sin(u)}`)}rect(e,t,n,o){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${n=+n}v${+o}h${-n}Z`}toString(){return this._}};function withPath(e){let t=3;return e.digits=function(n){if(!arguments.length)return t;if(null==n)t=null;else{let e=Math.floor(n);if(!(e>=0))throw RangeError(`invalid digits: ${n}`);t=e}return e},()=>new Path(t)}Path.prototype},2130:function(e,t,n){"use strict";function invariant(e,t){if(!e)throw Error("Invariant failed")}n.d(t,{Z:function(){return invariant}})}}]);