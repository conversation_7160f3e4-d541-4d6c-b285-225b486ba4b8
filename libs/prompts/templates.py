"""
LLM Prompt Templates for RAG Log Analysis
Contains prompts for log summarization, root cause analysis, and natural language query translation
"""

from typing import List, Dict, Any
from datetime import datetime

class PromptTemplates:
    """Collection of prompt templates for different log analysis tasks"""
    
    @staticmethod
    def single_log_summary_prompt(log_data: Dict[str, Any]) -> str:
        """Generate prompt to summarize a single log entry"""
        return f"""
Analyze this single log entry and provide a concise summary:

**Log Details:**
- Timestamp: {log_data.get('timestamp', 'N/A')}
- Level: {log_data.get('level', 'N/A')}
- Module: {log_data.get('module', 'N/A')}
- Status: {log_data.get('status', 'N/A')}
- Method: {log_data.get('method', 'N/A')}
- Response Time: {log_data.get('response_time', 'N/A')}ms
- Customer ID: {log_data.get('customer_id', 'N/A')}
- Session ID: {log_data.get('session_id', 'N/A')}
- Platform: {log_data.get('platform', 'N/A')}
- Log Source: {log_data.get('log_source', 'N/A')}
- Raw Data: {log_data.get('raw_data', 'N/A')}

**Task:**
Provide a clear, one-paragraph summary of what happened in this log entry. Include:
1. What action was attempted
2. Whether it succeeded or failed
3. Any notable performance metrics
4. Potential impact on user experience

**Summary:**"""

    @staticmethod
    def multiple_logs_root_cause_prompt(logs_data: List[Dict[str, Any]], query_context: str = "") -> str:
        """Generate prompt to analyze multiple logs for root cause analysis"""
        
        # Prepare logs summary
        logs_summary = []
        for i, log in enumerate(logs_data[:10], 1):  # Limit to first 10 logs to avoid token limits
            logs_summary.append(f"""
Log {i}:
- Time: {log.get('timestamp', 'N/A')}
- Level: {log.get('level', 'N/A')}
- Module: {log.get('module', 'N/A')}
- Status: {log.get('status', 'N/A')}
- Response Time: {log.get('response_time', 'N/A')}ms
- Customer: {log.get('customer_id', 'N/A')}
- Message: {log.get('raw_data', 'N/A')[:100]}...
""")
        
        total_logs = len(logs_data)
        context_note = f"\n\n**Query Context:** {query_context}" if query_context else ""
        
        return f"""
Analyze these {total_logs} log entries to identify patterns, root causes, and provide actionable insights:

**Logs to Analyze:**
{''.join(logs_summary)}

{f"(Showing first 10 of {total_logs} total logs)" if total_logs > 10 else ""}
{context_note}

**Analysis Tasks:**
1. **Pattern Identification:** What common patterns do you see across these logs?
2. **Root Cause Analysis:** What are the likely root causes of any errors or performance issues?
3. **Impact Assessment:** How severe are the issues and what's the impact on users?
4. **Recommendations:** What specific actions should be taken to resolve the issues?
5. **Monitoring Suggestions:** What metrics should be monitored to prevent similar issues?

**Provide a structured analysis with clear sections for each task above.**

**Analysis:**"""

    @staticmethod
    def natural_query_to_filters_prompt(user_query: str) -> str:
        """Generate prompt to translate natural language queries to structured filters"""
        return f"""
Convert this natural language query into structured log filters:

**User Query:** "{user_query}"

**Available Filter Options:**
- timestamp: Date/time range (e.g., "last hour", "yesterday", "2024-07-14")
- level: Log levels (INFO, WARN, ERROR, DEBUG, FATAL)
- module: Modules (FlightBooking, HotelReservation, LoyaltyPoints, Activities, etc.)
- status: HTTP status codes (200, 400, 404, 500, etc.)
- platform: Platform types (web, mobile, api)
- log_source: Log sources (flights, hotels, activities, loyalty, pointTransfer, common)
- response_time: Performance filters (>1000ms, <500ms, etc.)
- customer_id: Specific customer ID
- session_id: Specific session ID

**Instructions:**
1. Extract all relevant filter criteria from the user query
2. Convert time expressions to specific date ranges
3. Map keywords to appropriate filter values
4. Return a JSON object with the filters

**Examples:**
- "flight errors in the last hour" → {{"level": "ERROR", "module": "FlightBooking", "timestamp": "last_hour"}}
- "slow hotel requests yesterday" → {{"log_source": "hotels", "response_time": ">1000", "timestamp": "yesterday"}}
- "loyalty failures for customer 67890" → {{"log_source": "loyalty", "level": "ERROR", "customer_id": "67890"}}

**JSON Filters:**"""

    @staticmethod
    def log_search_summary_prompt(logs_data: List[Dict[str, Any]], original_query: str) -> str:
        """Generate prompt to summarize search results"""
        
        # Calculate statistics
        total_logs = len(logs_data)
        error_count = len([log for log in logs_data if log.get('level') == 'ERROR'])
        warn_count = len([log for log in logs_data if log.get('level') == 'WARN'])
        
        # Get unique modules and sources
        modules = {log.get('module', 'Unknown') for log in logs_data}
        sources = {log.get('log_source', 'Unknown') for log in logs_data}
        
        # Get time range
        timestamps = [log.get('timestamp') for log in logs_data if log.get('timestamp')]
        time_range = ""
        if timestamps:
            try:
                sorted_times = sorted(timestamps)
                time_range = f"From {sorted_times[0]} to {sorted_times[-1]}"
            except (TypeError, ValueError):
                time_range = "Various times"
        
        return f"""
Summarize these search results for the user query: "{original_query}"

**Search Results Statistics:**
- Total logs found: {total_logs}
- Error logs: {error_count}
- Warning logs: {warn_count}
- Time range: {time_range}
- Modules involved: {', '.join(modules)}
- Log sources: {', '.join(sources)}

**Sample Log Entries (first 5):**
{self._format_sample_logs(logs_data[:5])}

**Task:**
Provide a concise summary that includes:
1. Overview of what was found
2. Key issues or patterns identified
3. Most critical findings that need attention
4. Suggested next steps for investigation

**Summary:**"""

    @staticmethod
    def _format_sample_logs(logs: List[Dict[str, Any]]) -> str:
        """Format sample logs for display in prompts"""
        formatted = []
        for i, log in enumerate(logs, 1):
            formatted.append(f"""
{i}. [{log.get('timestamp', 'N/A')}] {log.get('level', 'N/A')} - {log.get('module', 'N/A')}
   Status: {log.get('status', 'N/A')} | Response: {log.get('response_time', 'N/A')}ms
   Message: {log.get('raw_data', 'N/A')[:80]}...
""")
        return ''.join(formatted)

    @staticmethod
    def performance_analysis_prompt(logs_data: List[Dict[str, Any]]) -> str:
        """Generate prompt for performance analysis"""
        
        # Calculate performance metrics
        response_times = [log.get('response_time') for log in logs_data if log.get('response_time') is not None]
        
        if response_times:
            avg_response = sum(response_times) / len(response_times)
            max_response = max(response_times)
            min_response = min(response_times)
            slow_requests = len([rt for rt in response_times if rt > 1000])
        else:
            avg_response = max_response = min_response = slow_requests = 0
        
        return f"""
Analyze the performance characteristics of these {len(logs_data)} log entries:

**Performance Metrics:**
- Average Response Time: {avg_response:.2f}ms
- Maximum Response Time: {max_response}ms
- Minimum Response Time: {min_response}ms
- Slow Requests (>1000ms): {slow_requests}
- Total Requests Analyzed: {len(response_times)}

**Sample Performance Data:**
{self._format_performance_logs(logs_data[:10])}

**Analysis Requirements:**
1. **Performance Trends:** Identify any concerning performance patterns
2. **Bottlenecks:** Highlight potential system bottlenecks
3. **Module Performance:** Compare performance across different modules
4. **Optimization Opportunities:** Suggest specific areas for performance improvement
5. **Alerting Thresholds:** Recommend performance monitoring thresholds

**Performance Analysis:**"""

    @staticmethod
    def _format_performance_logs(logs: List[Dict[str, Any]]) -> str:
        """Format logs with focus on performance metrics"""
        formatted = []
        for log in logs:
            if log.get('response_time') is not None:
                formatted.append(f"""
- {log.get('module', 'N/A')}: {log.get('response_time')}ms [{log.get('status', 'N/A')}] - {log.get('method', 'N/A')} {log.get('origin_url', 'N/A')}
""")
        return ''.join(formatted)

    @staticmethod
    def error_pattern_analysis_prompt(error_logs: List[Dict[str, Any]]) -> str:
        """Generate prompt for error pattern analysis"""
        
        # Group errors by status code and module
        status_counts = {}
        module_counts = {}
        
        for log in error_logs:
            status = log.get('status', 'Unknown')
            module = log.get('module', 'Unknown')
            
            status_counts[status] = status_counts.get(status, 0) + 1
            module_counts[module] = module_counts.get(module, 0) + 1
        
        return f"""
Analyze error patterns in these {len(error_logs)} error log entries:

**Error Distribution:**
- By Status Code: {dict(sorted(status_counts.items(), key=lambda x: x[1], reverse=True))}
- By Module: {dict(sorted(module_counts.items(), key=lambda x: x[1], reverse=True))}

**Recent Error Examples:**
{self._format_sample_logs(error_logs[:5])}

**Analysis Focus:**
1. **Error Clustering:** Are errors concentrated in specific modules or time periods?
2. **Error Cascading:** Do errors in one module trigger errors in others?
3. **User Impact:** Which errors have the highest impact on user experience?
4. **Resolution Priority:** How should these errors be prioritized for fixing?
5. **Prevention Strategies:** What can be done to prevent these error patterns?

**Error Pattern Analysis:**"""

# Export the class for use in other modules
__all__ = ['PromptTemplates']
