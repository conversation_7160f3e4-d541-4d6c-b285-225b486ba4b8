"""
Log Parser for RAG Log Analyzer
Extracts structured fields from log lines with the format:
[timestamp][level][module][sessionId][platform][customerId][clientIp][requestId][auditType][appName][responseTime][status][method][originUrl][extraData] : rawData
"""

import re
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path

@dataclass
class ParsedLog:
    """Structured representation of a parsed log entry"""
    timestamp: datetime
    level: str
    module: str
    session_id: str
    platform: str
    customer_id: str
    client_ip: str
    request_id: str
    audit_type: str
    app_name: str
    response_time: Optional[float]
    status: str
    method: str
    origin_url: str
    extra_data: str
    raw_data: str
    log_source: str  # flights, hotels, activities, etc.
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'level': self.level,
            'module': self.module,
            'session_id': self.session_id,
            'platform': self.platform,
            'customer_id': self.customer_id,
            'client_ip': self.client_ip,
            'request_id': self.request_id,
            'audit_type': self.audit_type,
            'app_name': self.app_name,
            'response_time': self.response_time,
            'status': self.status,
            'method': self.method,
            'origin_url': self.origin_url,
            'extra_data': self.extra_data,
            'raw_data': self.raw_data,
            'log_source': self.log_source
        }

class LogParser:
    """Parser for structured log entries"""
    
    # Regex pattern to match the log format
    LOG_PATTERN = re.compile(
        r'\[([^\]]+)\]'     # timestamp
        r'\[([^\]]+)\]'     # level
        r'\[([^\]]+)\]'     # module
        r'\[([^\]]+)\]'     # sessionId
        r'\[([^\]]+)\]'     # platform
        r'\[([^\]]+)\]'     # customerId
        r'\[([^\]]+)\]'     # clientIp
        r'\[([^\]]+)\]'     # requestId
        r'\[([^\]]+)\]'     # auditType
        r'\[([^\]]+)\]'     # appName
        r'\[([^\]]+)\]'     # responseTime
        r'\[([^\]]+)\]'     # status
        r'\[([^\]]+)\]'     # method
        r'\[([^\]]+)\]'     # originUrl
        r'\[([^\]]+)\]'     # extraData
        r'\s*:\s*(.+)'      # rawData
    )
    
    @staticmethod
    def parse_timestamp(timestamp_str: str) -> datetime:
        """Parse timestamp string to datetime object"""
        # Common timestamp formats
        formats = [
            '%Y-%m-%d %H:%M:%S.%f',
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%dT%H:%M:%S.%fZ',
            '%Y-%m-%dT%H:%M:%SZ',
            '%d/%m/%Y %H:%M:%S',
            '%m/%d/%Y %H:%M:%S'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(timestamp_str, fmt)
            except ValueError:
                continue
        
        # If all else fails, try to parse as ISO format
        try:
            return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        except ValueError:
            # Return current time if parsing fails
            return datetime.now()
    
    @staticmethod
    def parse_response_time(response_time_str: str) -> Optional[float]:
        """Parse response time to float (milliseconds)"""
        try:
            # Remove 'ms' suffix if present
            cleaned = response_time_str.replace('ms', '').strip()
            return float(cleaned)
        except (ValueError, AttributeError):
            return None
    
    @staticmethod
    def determine_log_source(file_path: str) -> str:
        """Determine log source from file path"""
        path_lower = file_path.lower()
        if 'flights' in path_lower:
            return 'flights'
        elif 'hotels' in path_lower:
            return 'hotels'
        elif 'activities' in path_lower:
            return 'activities'
        elif 'pointtransfer' in path_lower:
            return 'pointTransfer'
        elif 'loyalty' in path_lower:
            return 'loyalty'
        elif 'common' in path_lower:
            return 'common'
        else:
            return 'unknown'
    
    def parse_log_line(self, log_line: str, file_path: str = '') -> Optional[ParsedLog]:
        """Parse a single log line into structured data"""
        match = self.LOG_PATTERN.match(log_line.strip())
        
        if not match:
            return None
        
        groups = match.groups()
        
        try:
            parsed_log = ParsedLog(
                timestamp=self.parse_timestamp(groups[0]),
                level=groups[1],
                module=groups[2],
                session_id=groups[3],
                platform=groups[4],
                customer_id=groups[5],
                client_ip=groups[6],
                request_id=groups[7],
                audit_type=groups[8],
                app_name=groups[9],
                response_time=self.parse_response_time(groups[10]),
                status=groups[11],
                method=groups[12],
                origin_url=groups[13],
                extra_data=groups[14],
                raw_data=groups[15],
                log_source=self.determine_log_source(file_path)
            )
            
            return parsed_log
            
        except Exception as e:
            print(f"Error parsing log line: {e}")
            return None
    
    def parse_log_file(self, file_path: str) -> List[ParsedLog]:
        """Parse an entire log file"""
        parsed_logs = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    if line.strip():  # Skip empty lines
                        parsed_log = self.parse_log_line(line, file_path)
                        if parsed_log:
                            parsed_logs.append(parsed_log)
                        else:
                            print(f"Failed to parse line {line_num} in {file_path}")
        
        except FileNotFoundError:
            print(f"File not found: {file_path}")
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")
        
        return parsed_logs
    
    def parse_directory(self, directory_path: str) -> List[ParsedLog]:
        """Parse all log files in a directory"""
        all_logs = []
        directory = Path(directory_path)
        
        if not directory.exists():
            print(f"Directory not found: {directory_path}")
            return all_logs
        
        # Find all log files (typically .log, .txt, or no extension)
        log_files = []
        for pattern in ['*.log', '*.txt', '*']:
            log_files.extend(directory.glob(pattern))
        
        # Filter to only actual files
        log_files = [f for f in log_files if f.is_file()]
        
        for log_file in log_files:
            print(f"Parsing {log_file}")
            file_logs = self.parse_log_file(str(log_file))
            all_logs.extend(file_logs)
        
        return all_logs

def create_sample_logs():
    """Create sample log entries for testing"""
    sample_logs = [
        "[2024-07-14 10:30:15.123][INFO][FlightBooking][sess_12345][web][cust_67890][192.168.1.100][req_abc123][BOOKING][AxisFlights][250ms][200][POST][/api/flights/book][userId:67890,flightId:FL123] : Successfully booked flight FL123 for customer 67890",
        "[2024-07-14 10:31:22.456][ERROR][HotelReservation][sess_12346][mobile][cust_67891][192.168.1.101][req_def456][RESERVATION][AxisHotels][1500ms][500][POST][/api/hotels/reserve][hotelId:HTL456,roomType:deluxe] : Database connection timeout during hotel reservation",
        "[2024-07-14 10:32:30.789][WARN][LoyaltyPoints][sess_12347][web][cust_67892][192.168.1.102][req_ghi789][TRANSFER][AxisLoyalty][800ms][400][PUT][/api/loyalty/transfer][fromAccount:67892,toAccount:67893,points:1000] : Insufficient loyalty points for transfer",
        "[2024-07-14 10:33:45.012][INFO][Activities][sess_12348][mobile][cust_67893][192.168.1.103][req_jkl012][BOOKING][AxisActivities][300ms][200][GET][/api/activities/search][location:Paris,date:2024-08-15] : Found 25 activities in Paris for date 2024-08-15"
    ]
    
    return sample_logs

if __name__ == "__main__":
    # Test the parser with sample logs
    parser = LogParser()
    sample_logs = create_sample_logs()
    
    print("Parsing sample logs:")
    print("=" * 50)
    
    for i, log_line in enumerate(sample_logs, 1):
        print(f"\nLog {i}:")
        print(f"Raw: {log_line}")
        
        parsed = parser.parse_log_line(log_line, f"/log/axisLogs/test/sample_{i}.log")
        if parsed:
            print(f"Parsed: {json.dumps(parsed.to_dict(), indent=2)}")
        else:
            print("Failed to parse")
