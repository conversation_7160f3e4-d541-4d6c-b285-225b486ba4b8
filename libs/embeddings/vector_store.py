"""
Embedding and Vector Database Setup for RAG Log Analysis
Supports OpenAI embeddings and both Pinecone and FAISS vector stores
"""

import os
import json
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import logging
from abc import ABC, abstractmethod

# Optional imports - will fail gracefully if not installed
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

try:
    import pinecone
    PINECONE_AVAILABLE = True
except ImportError:
    PINECONE_AVAILABLE = False

try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False

@dataclass
class LogEmbedding:
    """Container for log entry and its embedding"""
    log_id: str
    embedding: List[float]
    metadata: Dict[str, Any]
    text_content: str

class EmbeddingGenerator(ABC):
    """Abstract base class for embedding generators"""
    
    @abstractmethod
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for given text"""
        pass
    
    @abstractmethod
    def get_dimension(self) -> int:
        """Get embedding dimension"""
        pass

class OpenAIEmbeddingGenerator(EmbeddingGenerator):
    """OpenAI embedding generator"""
    
    def __init__(self, model: str = "text-embedding-ada-002"):
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI package not available. Install with: pip install openai")
        
        self.client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.model = model
        self._dimension = 1536  # Ada-002 dimension
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding using OpenAI API"""
        try:
            response = self.client.embeddings.create(
                model=self.model,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logging.error(f"Error generating OpenAI embedding: {e}")
            raise
    
    def get_dimension(self) -> int:
        return self._dimension

class SentenceTransformerEmbeddingGenerator(EmbeddingGenerator):
    """Sentence Transformer embedding generator"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            raise ImportError("sentence-transformers package not available. Install with: pip install sentence-transformers")
        
        self.model = SentenceTransformer(model_name)
        self._dimension = self.model.get_sentence_embedding_dimension()
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding using Sentence Transformers"""
        try:
            embedding = self.model.encode(text)
            return embedding.tolist()
        except Exception as e:
            logging.error(f"Error generating SentenceTransformer embedding: {e}")
            raise
    
    def get_dimension(self) -> int:
        return self._dimension

class VectorStore(ABC):
    """Abstract base class for vector stores"""
    
    @abstractmethod
    def add_embeddings(self, embeddings: List[LogEmbedding]) -> None:
        """Add embeddings to the vector store"""
        pass
    
    @abstractmethod
    def search(self, query_embedding: List[float], k: int = 5, filters: Optional[Dict] = None) -> List[Tuple[str, float, Dict]]:
        """Search for similar embeddings"""
        pass
    
    @abstractmethod
    def delete(self, log_ids: List[str]) -> None:
        """Delete embeddings by log IDs"""
        pass

class PineconeVectorStore(VectorStore):
    """Pinecone vector store implementation"""
    
    def __init__(self, index_name: str, dimension: int, environment: Optional[str] = None):
        if not PINECONE_AVAILABLE:
            raise ImportError("Pinecone package not available. Install with: pip install pinecone-client")
        
        api_key = os.getenv("PINECONE_API_KEY")
        if not api_key:
            raise ValueError("PINECONE_API_KEY environment variable not set")
        
        # Initialize Pinecone
        pinecone.init(
            api_key=api_key,
            environment=environment or os.getenv("PINECONE_ENVIRONMENT", "us-west1-gcp")
        )
        
        self.index_name = index_name
        self.dimension = dimension
        
        # Create index if it doesn't exist
        if index_name not in pinecone.list_indexes():
            pinecone.create_index(
                name=index_name,
                dimension=dimension,
                metric="cosine"
            )
        
        self.index = pinecone.Index(index_name)
    
    def add_embeddings(self, embeddings: List[LogEmbedding]) -> None:
        """Add embeddings to Pinecone"""
        try:
            vectors = [
                (emb.log_id, emb.embedding, emb.metadata)
                for emb in embeddings
            ]
            self.index.upsert(vectors=vectors)
        except Exception as e:
            logging.error(f"Error adding embeddings to Pinecone: {e}")
            raise
    
    def search(self, query_embedding: List[float], k: int = 5, filters: Optional[Dict] = None) -> List[Tuple[str, float, Dict]]:
        """Search Pinecone for similar embeddings"""
        try:
            results = self.index.query(
                vector=query_embedding,
                top_k=k,
                filter=filters,
                include_metadata=True
            )
            
            return [
                (match.id, match.score, match.metadata)
                for match in results.matches
            ]
        except Exception as e:
            logging.error(f"Error searching Pinecone: {e}")
            raise
    
    def delete(self, log_ids: List[str]) -> None:
        """Delete embeddings from Pinecone"""
        try:
            self.index.delete(ids=log_ids)
        except Exception as e:
            logging.error(f"Error deleting from Pinecone: {e}")
            raise

class FAISSVectorStore(VectorStore):
    """FAISS vector store implementation"""
    
    def __init__(self, dimension: int, index_file: str = "faiss_index.bin", metadata_file: str = "faiss_metadata.json"):
        if not FAISS_AVAILABLE:
            raise ImportError("FAISS package not available. Install with: pip install faiss-cpu")
        
        self.dimension = dimension
        self.index_file = index_file
        self.metadata_file = metadata_file
        
        # Initialize FAISS index
        self.index = faiss.IndexFlatIP(dimension)  # Inner product (cosine similarity)
        self.metadata = {}  # Store metadata separately
        self.id_to_idx = {}  # Map log IDs to FAISS indices
        
        # Load existing index if available
        self._load_index()
    
    def _load_index(self) -> None:
        """Load existing FAISS index and metadata"""
        try:
            if os.path.exists(self.index_file):
                self.index = faiss.read_index(self.index_file)
            
            if os.path.exists(self.metadata_file):
                with open(self.metadata_file, 'r') as f:
                    data = json.load(f)
                    self.metadata = data.get('metadata', {})
                    self.id_to_idx = data.get('id_to_idx', {})
        except Exception as e:
            logging.error(f"Error loading FAISS index: {e}")
    
    def _save_index(self) -> None:
        """Save FAISS index and metadata to disk"""
        try:
            faiss.write_index(self.index, self.index_file)
            
            with open(self.metadata_file, 'w') as f:
                json.dump({
                    'metadata': self.metadata,
                    'id_to_idx': self.id_to_idx
                }, f)
        except Exception as e:
            logging.error(f"Error saving FAISS index: {e}")
    
    def add_embeddings(self, embeddings: List[LogEmbedding]) -> None:
        """Add embeddings to FAISS"""
        try:
            vectors = np.array([emb.embedding for emb in embeddings], dtype=np.float32)
            
            # Normalize vectors for cosine similarity
            faiss.normalize_L2(vectors)
            
            # Get starting index
            start_idx = self.index.ntotal
            
            # Add to FAISS
            self.index.add(vectors)
            
            # Update metadata and ID mapping
            for i, emb in enumerate(embeddings):
                idx = start_idx + i
                self.id_to_idx[emb.log_id] = idx
                self.metadata[emb.log_id] = emb.metadata
            
            # Save to disk
            self._save_index()
        except Exception as e:
            logging.error(f"Error adding embeddings to FAISS: {e}")
            raise
    
    def search(self, query_embedding: List[float], k: int = 5, filters: Optional[Dict] = None) -> List[Tuple[str, float, Dict]]:
        """Search FAISS for similar embeddings"""
        try:
            # Normalize query vector
            query_vector = np.array([query_embedding], dtype=np.float32)
            faiss.normalize_L2(query_vector)
            
            # Search
            scores, indices = self.index.search(query_vector, k)
            
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx == -1:  # FAISS returns -1 for missing results
                    continue
                
                # Find log ID by index
                log_id = None
                for lid, lidx in self.id_to_idx.items():
                    if lidx == idx:
                        log_id = lid
                        break
                
                if log_id and log_id in self.metadata:
                    metadata = self.metadata[log_id]
                    
                    # Apply filters if provided
                    if filters:
                        if not self._match_filters(metadata, filters):
                            continue
                    
                    results.append((log_id, float(score), metadata))
            
            return results
        except Exception as e:
            logging.error(f"Error searching FAISS: {e}")
            raise
    
    def _match_filters(self, metadata: Dict, filters: Dict) -> bool:
        """Check if metadata matches filters"""
        for key, value in filters.items():
            if key not in metadata:
                return False
            if metadata[key] != value:
                return False
        return True
    
    def delete(self, log_ids: List[str]) -> None:
        """Delete embeddings from FAISS (not efficiently supported, requires rebuild)"""
        # FAISS doesn't support efficient deletion, so we'd need to rebuild the index
        # For now, just remove from metadata
        for log_id in log_ids:
            if log_id in self.metadata:
                del self.metadata[log_id]
            if log_id in self.id_to_idx:
                del self.id_to_idx[log_id]
        
        self._save_index()

class LogEmbeddingService:
    """Main service for handling log embeddings and vector operations"""
    
    def __init__(self, embedding_generator: EmbeddingGenerator, vector_store: VectorStore):
        self.embedding_generator = embedding_generator
        self.vector_store = vector_store
    
    def create_log_embedding_text(self, log_data: Dict[str, Any]) -> str:
        """Create searchable text representation of log entry"""
        # Combine key fields into searchable text
        text_parts = [
            f"Module: {log_data.get('module', '')}",
            f"Level: {log_data.get('level', '')}",
            f"Status: {log_data.get('status', '')}",
            f"Platform: {log_data.get('platform', '')}",
            f"Source: {log_data.get('log_source', '')}",
            f"Method: {log_data.get('method', '')}",
            f"Message: {log_data.get('raw_data', '')}"
        ]
        
        return " | ".join(text_parts)
    
    def process_logs(self, logs_data: List[Dict[str, Any]]) -> None:
        """Process and embed a batch of logs"""
        embeddings = []
        
        for log_data in logs_data:
            try:
                # Create searchable text
                text_content = self.create_log_embedding_text(log_data)
                
                # Generate embedding
                embedding_vector = self.embedding_generator.generate_embedding(text_content)
                
                # Create log embedding object
                log_embedding = LogEmbedding(
                    log_id=log_data.get('request_id', f"log_{len(embeddings)}"),
                    embedding=embedding_vector,
                    metadata=log_data,
                    text_content=text_content
                )
                
                embeddings.append(log_embedding)
                
            except Exception as e:
                logging.error(f"Error processing log: {e}")
                continue
        
        # Add to vector store
        if embeddings:
            self.vector_store.add_embeddings(embeddings)
            logging.info(f"Successfully processed {len(embeddings)} log embeddings")
    
    def search_logs(self, query: str, k: int = 5, filters: Optional[Dict] = None) -> List[Tuple[str, float, Dict]]:
        """Search for logs similar to the query"""
        try:
            # Generate query embedding
            query_embedding = self.embedding_generator.generate_embedding(query)
            
            # Search vector store
            results = self.vector_store.search(query_embedding, k, filters)
            
            return results
        except Exception as e:
            logging.error(f"Error searching logs: {e}")
            raise

def create_embedding_service(provider: str = "openai", vector_store_type: str = "faiss") -> LogEmbeddingService:
    """Factory function to create embedding service"""
    
    # Create embedding generator
    if provider.lower() == "openai":
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI not available. Install with: pip install openai")
        embedding_generator = OpenAIEmbeddingGenerator()
    elif provider.lower() == "sentence-transformers":
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            raise ImportError("sentence-transformers not available. Install with: pip install sentence-transformers")
        embedding_generator = SentenceTransformerEmbeddingGenerator()
    else:
        raise ValueError(f"Unsupported embedding provider: {provider}")
    
    # Create vector store
    dimension = embedding_generator.get_dimension()
    
    if vector_store_type.lower() == "pinecone":
        if not PINECONE_AVAILABLE:
            raise ImportError("Pinecone not available. Install with: pip install pinecone-client")
        vector_store = PineconeVectorStore("log-analyzer", dimension)
    elif vector_store_type.lower() == "faiss":
        if not FAISS_AVAILABLE:
            raise ImportError("FAISS not available. Install with: pip install faiss-cpu")
        vector_store = FAISSVectorStore(dimension)
    else:
        raise ValueError(f"Unsupported vector store type: {vector_store_type}")
    
    return LogEmbeddingService(embedding_generator, vector_store)

# Export main classes
__all__ = [
    'LogEmbeddingService', 'LogEmbedding', 'EmbeddingGenerator', 'VectorStore',
    'OpenAIEmbeddingGenerator', 'SentenceTransformerEmbeddingGenerator',
    'PineconeVectorStore', 'FAISSVectorStore', 'create_embedding_service'
]
