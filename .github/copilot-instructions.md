<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

# RAG Log Analyzer - Copilot Instructions

This is a full-stack RAG-powered log analysis application similar to Kibana, built with Next.js frontend and FastAPI backend.

## Project Structure
- `/apps/frontend` - Next.js frontend (currently in root for simplicity)
- `/apps/backend` - FastAPI backend with RAG capabilities
- `/libs/parsers` - Log parsing utilities
- `/libs/prompts` - LLM prompt templates
- `/libs/embeddings` - Vector database and embedding services

## Key Technologies
- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS, Recharts
- **Backend**: FastAPI, Python, OpenAI/SentenceTransformers, FAISS/Pinecone
- **Log Format**: Structured format with [timestamp][level][module]...[extraData] : rawData

## Development Guidelines
1. **Log Parsing**: Use the structured format defined in `libs/parsers/log_parser.py`
2. **Embeddings**: Support both OpenAI and SentenceTransformers with FAISS/Pinecone backends
3. **LLM Integration**: Use prompt templates from `libs/prompts/templates.py`
4. **API Design**: RESTful endpoints in FastAPI with proper error handling
5. **UI Components**: Reusable React components with TypeScript interfaces
6. **Charts**: Use Recharts for data visualization

## Code Patterns
- Use TypeScript interfaces for data structures
- Implement proper error handling and loading states
- Follow Next.js App Router patterns
- Use Tailwind for styling with consistent design system
- Implement responsive design for mobile/desktop

## Environment Setup
- Frontend runs on port 3000
- Backend runs on port 8000
- Requires OpenAI API key for embeddings (optional, falls back to SentenceTransformers)
- Supports both FAISS (local) and Pinecone (cloud) vector stores
