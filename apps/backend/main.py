"""
FastAPI Backend for RAG Log Analyzer
Provides REST API endpoints for log analysis, search, and RAG operations
"""

import os
import sys
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging
from pathlib import Path

# Add libs to path for imports
sys.path.append(str(Path(__file__).parent.parent / "libs"))

from fastapi import FastAPI, HTTPException, Query, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

# Import our custom modules
from parsers.log_parser import LogParser, ParsedLog
from prompts.templates import PromptTemplates
from embeddings.vector_store import create_embedding_service, LogEmbeddingService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="RAG Log Analyzer API",
    description="REST API for log analysis with RAG capabilities",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],  # Next.js dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global services
log_parser = LogParser()
embedding_service: Optional[LogEmbeddingService] = None
prompt_templates = PromptTemplates()

# Pydantic models for API
class LogQuery(BaseModel):
    query: str = Field(..., description="Natural language query for log search")
    max_results: int = Field(10, description="Maximum number of results to return")
    filters: Optional[Dict[str, Any]] = Field(None, description="Additional filters")

class LogAnalysisRequest(BaseModel):
    log_ids: List[str] = Field(..., description="List of log IDs to analyze")
    analysis_type: str = Field("root_cause", description="Type of analysis: root_cause, performance, error_pattern")

class ParseLogsRequest(BaseModel):
    log_lines: List[str] = Field(..., description="Raw log lines to parse")
    source_path: str = Field("", description="Source file path for context")

class ParseLogsResponse(BaseModel):
    parsed_logs: List[Dict[str, Any]]
    failed_count: int
    total_count: int

class SearchResponse(BaseModel):
    results: List[Dict[str, Any]]
    total_found: int
    query_embedding_time: float
    search_time: float

class AnalysisResponse(BaseModel):
    analysis: str
    log_count: int
    analysis_type: str
    generated_at: str

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    global embedding_service
    
    try:
        # Try to initialize with OpenAI first, fall back to sentence transformers
        try:
            embedding_service = create_embedding_service("openai", "faiss")
            logger.info("Initialized with OpenAI embeddings and FAISS vector store")
        except Exception as e:
            logger.warning(f"Failed to initialize OpenAI embeddings: {e}")
            try:
                embedding_service = create_embedding_service("sentence-transformers", "faiss")
                logger.info("Initialized with SentenceTransformers embeddings and FAISS vector store")
            except Exception as e:
                logger.error(f"Failed to initialize any embedding service: {e}")
                embedding_service = None
    
    except Exception as e:
        logger.error(f"Error during startup: {e}")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "embedding_service_available": embedding_service is not None
    }

# Parse logs endpoint
@app.post("/api/parse-logs", response_model=ParseLogsResponse)
async def parse_logs(request: ParseLogsRequest):
    """Parse raw log lines into structured format"""
    try:
        parsed_logs = []
        failed_count = 0
        
        for log_line in request.log_lines:
            parsed = log_parser.parse_log_line(log_line, request.source_path)
            if parsed:
                parsed_logs.append(parsed.to_dict())
            else:
                failed_count += 1
        
        return ParseLogsResponse(
            parsed_logs=parsed_logs,
            failed_count=failed_count,
            total_count=len(request.log_lines)
        )
    
    except Exception as e:
        logger.error(f"Error parsing logs: {e}")
        raise HTTPException(status_code=500, detail=f"Error parsing logs: {str(e)}")

# Upload and parse log file
@app.post("/api/upload-logs")
async def upload_logs(file: UploadFile = File(...)):
    """Upload and parse a log file"""
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    try:
        # Read file content
        content = await file.read()
        log_lines = content.decode('utf-8').strip().split('\n')
        
        # Parse logs
        parsed_logs = []
        failed_count = 0
        
        for log_line in log_lines:
            if log_line.strip():  # Skip empty lines
                parsed = log_parser.parse_log_line(log_line, file.filename)
                if parsed:
                    parsed_logs.append(parsed.to_dict())
                else:
                    failed_count += 1
        
        # Process embeddings if service is available
        embedding_processed = False
        if embedding_service and parsed_logs:
            try:
                embedding_service.process_logs(parsed_logs)
                embedding_processed = True
                logger.info(f"Processed {len(parsed_logs)} log embeddings")
            except Exception as e:
                logger.error(f"Error processing embeddings: {e}")
        
        return {
            "parsed_logs": parsed_logs,
            "failed_count": failed_count,
            "total_count": len(log_lines),
            "filename": file.filename,
            "embedding_processed": embedding_processed
        }
    
    except Exception as e:
        logger.error(f"Error uploading/parsing file: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")

# Search logs endpoint
@app.post("/api/search", response_model=SearchResponse)
async def search_logs(request: LogQuery):
    """Search logs using natural language query"""
    if not embedding_service:
        raise HTTPException(status_code=503, detail="Embedding service not available")
    
    try:
        start_time = datetime.now()
        
        # Search for similar logs
        results = embedding_service.search_logs(
            query=request.query,
            k=request.max_results,
            filters=request.filters
        )
        
        search_time = (datetime.now() - start_time).total_seconds()
        
        # Format results
        formatted_results = []
        for log_id, score, metadata in results:
            formatted_results.append({
                "log_id": log_id,
                "similarity_score": score,
                "metadata": metadata
            })
        
        return SearchResponse(
            results=formatted_results,
            total_found=len(results),
            query_embedding_time=0.0,  # Would measure separately if needed
            search_time=search_time
        )
    
    except Exception as e:
        logger.error(f"Error searching logs: {e}")
        raise HTTPException(status_code=500, detail=f"Error searching logs: {str(e)}")

# Analyze logs endpoint
@app.post("/api/analyze", response_model=AnalysisResponse)
async def analyze_logs(request: LogAnalysisRequest):
    """Analyze logs using LLM"""
    if not embedding_service:
        raise HTTPException(status_code=503, detail="Embedding service not available")
    
    try:
        # For demo purposes, we'll simulate analysis
        # In a real implementation, you'd retrieve the actual logs and send to LLM
        
        analysis_text = ""
        if request.analysis_type == "root_cause":
            analysis_text = f"Root cause analysis for {len(request.log_ids)} logs would be performed here. This would involve retrieving the logs, analyzing patterns, and generating insights using an LLM."
        elif request.analysis_type == "performance":
            analysis_text = f"Performance analysis for {len(request.log_ids)} logs would show response time trends, bottlenecks, and optimization opportunities."
        elif request.analysis_type == "error_pattern":
            analysis_text = f"Error pattern analysis for {len(request.log_ids)} logs would identify common error types, frequencies, and suggested remediation steps."
        else:
            analysis_text = f"General analysis for {len(request.log_ids)} logs."
        
        return AnalysisResponse(
            analysis=analysis_text,
            log_count=len(request.log_ids),
            analysis_type=request.analysis_type,
            generated_at=datetime.now().isoformat()
        )
    
    except Exception as e:
        logger.error(f"Error analyzing logs: {e}")
        raise HTTPException(status_code=500, detail=f"Error analyzing logs: {str(e)}")

# Get log statistics
@app.get("/api/stats")
async def get_log_stats():
    """Get log statistics"""
    # This would typically query your database/vector store for stats
    # For demo, returning mock data
    return {
        "total_logs": 1250,
        "logs_by_level": {
            "INFO": 850,
            "WARN": 250,
            "ERROR": 120,
            "DEBUG": 30
        },
        "logs_by_source": {
            "flights": 400,
            "hotels": 350,
            "activities": 200,
            "loyalty": 180,
            "pointTransfer": 120
        },
        "avg_response_time": 487.5,
        "error_rate": 9.6,
        "last_updated": datetime.now().isoformat()
    }

# Process directory of logs
@app.post("/api/process-directory")
async def process_log_directory(directory_path: str = Query(..., description="Path to log directory")):
    """Process all log files in a directory"""
    try:
        all_logs = log_parser.parse_directory(directory_path)
        
        if not all_logs:
            raise HTTPException(status_code=404, detail=f"No logs found in directory: {directory_path}")
        
        # Convert to dicts
        logs_data = [log.to_dict() for log in all_logs]
        
        # Process embeddings if service is available
        embedding_processed = False
        if embedding_service:
            try:
                embedding_service.process_logs(logs_data)
                embedding_processed = True
                logger.info(f"Processed {len(logs_data)} log embeddings from directory")
            except Exception as e:
                logger.error(f"Error processing embeddings: {e}")
        
        return {
            "directory": directory_path,
            "total_logs_processed": len(all_logs),
            "logs_by_source": {},  # Could calculate this
            "embedding_processed": embedding_processed,
            "processed_at": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"Error processing directory: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing directory: {str(e)}")

# Create sample data endpoint (for testing)
@app.post("/api/create-sample-data")
async def create_sample_data():
    """Create sample log data for testing"""
    try:
        from parsers.log_parser import create_sample_logs
        
        sample_log_lines = create_sample_logs()
        parsed_logs = []
        
        for line in sample_log_lines:
            parsed = log_parser.parse_log_line(line, "/sample/logs/test.log")
            if parsed:
                parsed_logs.append(parsed.to_dict())
        
        # Process embeddings if available
        embedding_processed = False
        if embedding_service and parsed_logs:
            try:
                embedding_service.process_logs(parsed_logs)
                embedding_processed = True
                logger.info(f"Created and processed {len(parsed_logs)} sample log embeddings")
            except Exception as e:
                logger.error(f"Error processing sample embeddings: {e}")
        
        return {
            "sample_logs_created": len(parsed_logs),
            "embedding_processed": embedding_processed,
            "logs": parsed_logs
        }
    
    except Exception as e:
        logger.error(f"Error creating sample data: {e}")
        raise HTTPException(status_code=500, detail=f"Error creating sample data: {str(e)}")

if __name__ == "__main__":
    # Run the server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
